# CCP PDF Reader - ProGuard Configuration
# Optimized for production release

# Basic ProGuard configuration
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose
-dontoptimize
-dontpreverify

# Keep line numbers for debugging stack traces
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# Keep annotations
-keepattributes *Annotation*
-keepattributes Signature
-keepattributes InnerClasses
-keepattributes EnclosingMethod

# PDF viewer library classes
-keep class com.github.barteksc.pdfviewer.** { *; }
-keep class com.shockwave.pdfium.** { *; }
-dontwarn com.github.barteksc.pdfviewer.**
-dontwarn com.shockwave.pdfium.**

# Application classes
-keep class com.ccppdfreader.app.CCPPDFReaderApplication { *; }

# Keep model classes (for JSON serialization)
-keep class com.ccppdfreader.app.models.** { *; }

# Keep interfaces (MVP pattern)
-keep interface com.ccppdfreader.app.interfaces.** { *; }

# Keep presenters (MVP pattern)
-keep class com.ccppdfreader.app.presenters.** { *; }

# Keep managers
-keep class com.ccppdfreader.app.managers.** { *; }

# Keep activities and fragments
-keep class com.ccppdfreader.app.activities.** { *; }
-keep class com.ccppdfreader.app.fragments.** { *; }

# Keep adapters
-keep class com.ccppdfreader.app.adapters.** { *; }

# Keep enum classes
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep Parcelable implementations
-keep class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Keep serializable classes
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# Gson specific classes
-keepattributes Signature
-keepattributes *Annotation*
-keep class sun.misc.Unsafe { *; }
-keep class com.google.gson.stream.** { *; }

# Material Design
-keep class com.google.android.material.** { *; }
-dontwarn com.google.android.material.**

# AndroidX
-keep class androidx.** { *; }
-keep interface androidx.** { *; }
-dontwarn androidx.**

# ViewBinding
-keep class * implements androidx.viewbinding.ViewBinding {
    public static *** inflate(android.view.LayoutInflater);
    public static *** inflate(android.view.LayoutInflater, android.view.ViewGroup, boolean);
    public static *** bind(android.view.View);
}

# Dexter permissions library
-keep class com.karumi.dexter.** { *; }
-dontwarn com.karumi.dexter.**

# Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Remove debug code
-assumenosideeffects class com.ccppdfreader.app.utils.PerformanceUtils {
    public static void logPerformance(...);
}

# Optimization settings
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep custom views
-keep public class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
    public void set*(...);
    *** get*();
}

# Keep activity methods
-keepclassmembers class * extends android.app.Activity {
   public void *(android.view.View);
}

# Keep fragment methods
-keepclassmembers class * extends androidx.fragment.app.Fragment {
   public void *(android.view.View);
}
