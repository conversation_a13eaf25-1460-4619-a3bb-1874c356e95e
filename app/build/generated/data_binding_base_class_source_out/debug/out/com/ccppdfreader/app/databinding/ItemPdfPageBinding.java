// Generated by view binder compiler. Do not edit!
package com.ccppdfreader.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.ccppdfreader.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPdfPageBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView imageViewPage;

  @NonNull
  public final TextView textViewPageNumber;

  private ItemPdfPageBinding(@NonNull LinearLayout rootView, @NonNull ImageView imageViewPage,
      @NonNull TextView textViewPageNumber) {
    this.rootView = rootView;
    this.imageViewPage = imageViewPage;
    this.textViewPageNumber = textViewPageNumber;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPdfPageBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPdfPageBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_pdf_page, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPdfPageBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.imageViewPage;
      ImageView imageViewPage = ViewBindings.findChildViewById(rootView, id);
      if (imageViewPage == null) {
        break missingId;
      }

      id = R.id.textViewPageNumber;
      TextView textViewPageNumber = ViewBindings.findChildViewById(rootView, id);
      if (textViewPageNumber == null) {
        break missingId;
      }

      return new ItemPdfPageBinding((LinearLayout) rootView, imageViewPage, textViewPageNumber);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
