// Generated by view binder compiler. Do not edit!
package com.ccppdfreader.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.ccppdfreader.app.R;
import com.google.android.material.card.MaterialCardView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemBookmarkBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton btnMoreOptions;

  @NonNull
  public final ImageView ivBookmarkIcon;

  @NonNull
  public final TextView tvBookmarkInfo;

  @NonNull
  public final TextView tvBookmarkTitle;

  @NonNull
  public final TextView tvFileName;

  private ItemBookmarkBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageButton btnMoreOptions, @NonNull ImageView ivBookmarkIcon,
      @NonNull TextView tvBookmarkInfo, @NonNull TextView tvBookmarkTitle,
      @NonNull TextView tvFileName) {
    this.rootView = rootView;
    this.btnMoreOptions = btnMoreOptions;
    this.ivBookmarkIcon = ivBookmarkIcon;
    this.tvBookmarkInfo = tvBookmarkInfo;
    this.tvBookmarkTitle = tvBookmarkTitle;
    this.tvFileName = tvFileName;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemBookmarkBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemBookmarkBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_bookmark, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemBookmarkBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_more_options;
      ImageButton btnMoreOptions = ViewBindings.findChildViewById(rootView, id);
      if (btnMoreOptions == null) {
        break missingId;
      }

      id = R.id.iv_bookmark_icon;
      ImageView ivBookmarkIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivBookmarkIcon == null) {
        break missingId;
      }

      id = R.id.tv_bookmark_info;
      TextView tvBookmarkInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvBookmarkInfo == null) {
        break missingId;
      }

      id = R.id.tv_bookmark_title;
      TextView tvBookmarkTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvBookmarkTitle == null) {
        break missingId;
      }

      id = R.id.tv_file_name;
      TextView tvFileName = ViewBindings.findChildViewById(rootView, id);
      if (tvFileName == null) {
        break missingId;
      }

      return new ItemBookmarkBinding((MaterialCardView) rootView, btnMoreOptions, ivBookmarkIcon,
          tvBookmarkInfo, tvBookmarkTitle, tvFileName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
