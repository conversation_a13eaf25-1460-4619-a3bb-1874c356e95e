// Generated by view binder compiler. Do not edit!
package com.ccppdfreader.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.ccppdfreader.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemBookmarkBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView textViewBookmarkPage;

  @NonNull
  public final TextView textViewBookmarkTitle;

  private ItemBookmarkBinding(@NonNull LinearLayout rootView,
      @NonNull TextView textViewBookmarkPage, @NonNull TextView textViewBookmarkTitle) {
    this.rootView = rootView;
    this.textViewBookmarkPage = textViewBookmarkPage;
    this.textViewBookmarkTitle = textViewBookmarkTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemBookmarkBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemBookmarkBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_bookmark, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemBookmarkBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.textViewBookmarkPage;
      TextView textViewBookmarkPage = ViewBindings.findChildViewById(rootView, id);
      if (textViewBookmarkPage == null) {
        break missingId;
      }

      id = R.id.textViewBookmarkTitle;
      TextView textViewBookmarkTitle = ViewBindings.findChildViewById(rootView, id);
      if (textViewBookmarkTitle == null) {
        break missingId;
      }

      return new ItemBookmarkBinding((LinearLayout) rootView, textViewBookmarkPage,
          textViewBookmarkTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
