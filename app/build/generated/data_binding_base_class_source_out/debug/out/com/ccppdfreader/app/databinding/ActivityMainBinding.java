// Generated by view binder compiler. Do not edit!
package com.ccppdfreader.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.ccppdfreader.app.R;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final BottomNavigationView bottomNavigation;

  @NonNull
  public final MaterialButton btnBrowseFiles;

  @NonNull
  public final MaterialButton btnOpenPdf;

  @NonNull
  public final FloatingActionButton fabOpenPdf;

  @NonNull
  public final LinearLayout layoutEmptyRecent;

  @NonNull
  public final RecyclerView recyclerRecentFiles;

  @NonNull
  public final MaterialToolbar toolbar;

  private ActivityMainBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull BottomNavigationView bottomNavigation,
      @NonNull MaterialButton btnBrowseFiles, @NonNull MaterialButton btnOpenPdf,
      @NonNull FloatingActionButton fabOpenPdf, @NonNull LinearLayout layoutEmptyRecent,
      @NonNull RecyclerView recyclerRecentFiles, @NonNull MaterialToolbar toolbar) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.bottomNavigation = bottomNavigation;
    this.btnBrowseFiles = btnBrowseFiles;
    this.btnOpenPdf = btnOpenPdf;
    this.fabOpenPdf = fabOpenPdf;
    this.layoutEmptyRecent = layoutEmptyRecent;
    this.recyclerRecentFiles = recyclerRecentFiles;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_bar_layout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.bottom_navigation;
      BottomNavigationView bottomNavigation = ViewBindings.findChildViewById(rootView, id);
      if (bottomNavigation == null) {
        break missingId;
      }

      id = R.id.btn_browse_files;
      MaterialButton btnBrowseFiles = ViewBindings.findChildViewById(rootView, id);
      if (btnBrowseFiles == null) {
        break missingId;
      }

      id = R.id.btn_open_pdf;
      MaterialButton btnOpenPdf = ViewBindings.findChildViewById(rootView, id);
      if (btnOpenPdf == null) {
        break missingId;
      }

      id = R.id.fab_open_pdf;
      FloatingActionButton fabOpenPdf = ViewBindings.findChildViewById(rootView, id);
      if (fabOpenPdf == null) {
        break missingId;
      }

      id = R.id.layout_empty_recent;
      LinearLayout layoutEmptyRecent = ViewBindings.findChildViewById(rootView, id);
      if (layoutEmptyRecent == null) {
        break missingId;
      }

      id = R.id.recycler_recent_files;
      RecyclerView recyclerRecentFiles = ViewBindings.findChildViewById(rootView, id);
      if (recyclerRecentFiles == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityMainBinding((CoordinatorLayout) rootView, appBarLayout, bottomNavigation,
          btnBrowseFiles, btnOpenPdf, fabOpenPdf, layoutEmptyRecent, recyclerRecentFiles, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
