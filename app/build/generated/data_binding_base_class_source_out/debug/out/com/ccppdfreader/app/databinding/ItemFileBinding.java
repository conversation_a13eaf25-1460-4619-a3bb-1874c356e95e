// Generated by view binder compiler. Do not edit!
package com.ccppdfreader.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.ccppdfreader.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemFileBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageView imageViewFileIcon;

  @NonNull
  public final TextView textViewFileDate;

  @NonNull
  public final TextView textViewFileName;

  @NonNull
  public final TextView textViewFileSize;

  private ItemFileBinding(@NonNull LinearLayout rootView, @NonNull ImageView imageViewFileIcon,
      @NonNull TextView textViewFileDate, @NonNull TextView textViewFileName,
      @NonNull TextView textViewFileSize) {
    this.rootView = rootView;
    this.imageViewFileIcon = imageViewFileIcon;
    this.textViewFileDate = textViewFileDate;
    this.textViewFileName = textViewFileName;
    this.textViewFileSize = textViewFileSize;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemFileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemFileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_file, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemFileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.imageViewFileIcon;
      ImageView imageViewFileIcon = ViewBindings.findChildViewById(rootView, id);
      if (imageViewFileIcon == null) {
        break missingId;
      }

      id = R.id.textViewFileDate;
      TextView textViewFileDate = ViewBindings.findChildViewById(rootView, id);
      if (textViewFileDate == null) {
        break missingId;
      }

      id = R.id.textViewFileName;
      TextView textViewFileName = ViewBindings.findChildViewById(rootView, id);
      if (textViewFileName == null) {
        break missingId;
      }

      id = R.id.textViewFileSize;
      TextView textViewFileSize = ViewBindings.findChildViewById(rootView, id);
      if (textViewFileSize == null) {
        break missingId;
      }

      return new ItemFileBinding((LinearLayout) rootView, imageViewFileIcon, textViewFileDate,
          textViewFileName, textViewFileSize);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
