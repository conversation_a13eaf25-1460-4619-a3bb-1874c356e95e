// Generated by view binder compiler. Do not edit!
package com.ccppdfreader.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.ccppdfreader.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemFileBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnMoreOptions;

  @NonNull
  public final ImageView ivFileIcon;

  @NonNull
  public final TextView tvFileDetails;

  @NonNull
  public final TextView tvFileName;

  private ItemFileBinding(@NonNull LinearLayout rootView, @NonNull ImageButton btnMoreOptions,
      @NonNull ImageView ivFileIcon, @NonNull TextView tvFileDetails,
      @NonNull TextView tvFileName) {
    this.rootView = rootView;
    this.btnMoreOptions = btnMoreOptions;
    this.ivFileIcon = ivFileIcon;
    this.tvFileDetails = tvFileDetails;
    this.tvFileName = tvFileName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemFileBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemFileBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_file, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemFileBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_more_options;
      ImageButton btnMoreOptions = ViewBindings.findChildViewById(rootView, id);
      if (btnMoreOptions == null) {
        break missingId;
      }

      id = R.id.iv_file_icon;
      ImageView ivFileIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivFileIcon == null) {
        break missingId;
      }

      id = R.id.tv_file_details;
      TextView tvFileDetails = ViewBindings.findChildViewById(rootView, id);
      if (tvFileDetails == null) {
        break missingId;
      }

      id = R.id.tv_file_name;
      TextView tvFileName = ViewBindings.findChildViewById(rootView, id);
      if (tvFileName == null) {
        break missingId;
      }

      return new ItemFileBinding((LinearLayout) rootView, btnMoreOptions, ivFileIcon, tvFileDetails,
          tvFileName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
