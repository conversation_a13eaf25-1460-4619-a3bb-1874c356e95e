// Generated by view binder compiler. Do not edit!
package com.ccppdfreader.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.ccppdfreader.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPdfDocumentBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView textViewPdfPath;

  @NonNull
  public final TextView textViewPdfTitle;

  private ItemPdfDocumentBinding(@NonNull LinearLayout rootView, @NonNull TextView textViewPdfPath,
      @NonNull TextView textViewPdfTitle) {
    this.rootView = rootView;
    this.textViewPdfPath = textViewPdfPath;
    this.textViewPdfTitle = textViewPdfTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPdfDocumentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPdfDocumentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_pdf_document, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPdfDocumentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.textViewPdfPath;
      TextView textViewPdfPath = ViewBindings.findChildViewById(rootView, id);
      if (textViewPdfPath == null) {
        break missingId;
      }

      id = R.id.textViewPdfTitle;
      TextView textViewPdfTitle = ViewBindings.findChildViewById(rootView, id);
      if (textViewPdfTitle == null) {
        break missingId;
      }

      return new ItemPdfDocumentBinding((LinearLayout) rootView, textViewPdfPath, textViewPdfTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
