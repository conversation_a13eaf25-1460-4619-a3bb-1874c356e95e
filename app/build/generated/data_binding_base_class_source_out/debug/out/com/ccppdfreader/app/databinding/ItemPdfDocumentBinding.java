// Generated by view binder compiler. Do not edit!
package com.ccppdfreader.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.ccppdfreader.app.R;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.progressindicator.LinearProgressIndicator;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPdfDocumentBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final ImageButton btnMoreOptions;

  @NonNull
  public final ImageView ivFavorite;

  @NonNull
  public final ImageView ivPdfIcon;

  @NonNull
  public final LinearProgressIndicator progressReading;

  @NonNull
  public final TextView tvDocumentInfo;

  @NonNull
  public final TextView tvDocumentTitle;

  @NonNull
  public final TextView tvProgress;

  private ItemPdfDocumentBinding(@NonNull MaterialCardView rootView,
      @NonNull ImageButton btnMoreOptions, @NonNull ImageView ivFavorite,
      @NonNull ImageView ivPdfIcon, @NonNull LinearProgressIndicator progressReading,
      @NonNull TextView tvDocumentInfo, @NonNull TextView tvDocumentTitle,
      @NonNull TextView tvProgress) {
    this.rootView = rootView;
    this.btnMoreOptions = btnMoreOptions;
    this.ivFavorite = ivFavorite;
    this.ivPdfIcon = ivPdfIcon;
    this.progressReading = progressReading;
    this.tvDocumentInfo = tvDocumentInfo;
    this.tvDocumentTitle = tvDocumentTitle;
    this.tvProgress = tvProgress;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPdfDocumentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPdfDocumentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_pdf_document, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPdfDocumentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_more_options;
      ImageButton btnMoreOptions = ViewBindings.findChildViewById(rootView, id);
      if (btnMoreOptions == null) {
        break missingId;
      }

      id = R.id.iv_favorite;
      ImageView ivFavorite = ViewBindings.findChildViewById(rootView, id);
      if (ivFavorite == null) {
        break missingId;
      }

      id = R.id.iv_pdf_icon;
      ImageView ivPdfIcon = ViewBindings.findChildViewById(rootView, id);
      if (ivPdfIcon == null) {
        break missingId;
      }

      id = R.id.progress_reading;
      LinearProgressIndicator progressReading = ViewBindings.findChildViewById(rootView, id);
      if (progressReading == null) {
        break missingId;
      }

      id = R.id.tv_document_info;
      TextView tvDocumentInfo = ViewBindings.findChildViewById(rootView, id);
      if (tvDocumentInfo == null) {
        break missingId;
      }

      id = R.id.tv_document_title;
      TextView tvDocumentTitle = ViewBindings.findChildViewById(rootView, id);
      if (tvDocumentTitle == null) {
        break missingId;
      }

      id = R.id.tv_progress;
      TextView tvProgress = ViewBindings.findChildViewById(rootView, id);
      if (tvProgress == null) {
        break missingId;
      }

      return new ItemPdfDocumentBinding((MaterialCardView) rootView, btnMoreOptions, ivFavorite,
          ivPdfIcon, progressReading, tvDocumentInfo, tvDocumentTitle, tvProgress);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
