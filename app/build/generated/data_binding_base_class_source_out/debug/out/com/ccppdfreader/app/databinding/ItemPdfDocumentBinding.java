// Generated by view binder compiler. Do not edit!
package com.ccppdfreader.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.ccppdfreader.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemPdfDocumentBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView imageViewFavorite;

  @NonNull
  public final ProgressBar progressBarReading;

  @NonNull
  public final TextView textViewPdfPages;

  @NonNull
  public final TextView textViewPdfPath;

  @NonNull
  public final TextView textViewPdfProgress;

  @NonNull
  public final TextView textViewPdfSize;

  @NonNull
  public final TextView textViewPdfTitle;

  private ItemPdfDocumentBinding(@NonNull CardView rootView, @NonNull ImageView imageViewFavorite,
      @NonNull ProgressBar progressBarReading, @NonNull TextView textViewPdfPages,
      @NonNull TextView textViewPdfPath, @NonNull TextView textViewPdfProgress,
      @NonNull TextView textViewPdfSize, @NonNull TextView textViewPdfTitle) {
    this.rootView = rootView;
    this.imageViewFavorite = imageViewFavorite;
    this.progressBarReading = progressBarReading;
    this.textViewPdfPages = textViewPdfPages;
    this.textViewPdfPath = textViewPdfPath;
    this.textViewPdfProgress = textViewPdfProgress;
    this.textViewPdfSize = textViewPdfSize;
    this.textViewPdfTitle = textViewPdfTitle;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemPdfDocumentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemPdfDocumentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_pdf_document, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemPdfDocumentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.imageViewFavorite;
      ImageView imageViewFavorite = ViewBindings.findChildViewById(rootView, id);
      if (imageViewFavorite == null) {
        break missingId;
      }

      id = R.id.progressBarReading;
      ProgressBar progressBarReading = ViewBindings.findChildViewById(rootView, id);
      if (progressBarReading == null) {
        break missingId;
      }

      id = R.id.textViewPdfPages;
      TextView textViewPdfPages = ViewBindings.findChildViewById(rootView, id);
      if (textViewPdfPages == null) {
        break missingId;
      }

      id = R.id.textViewPdfPath;
      TextView textViewPdfPath = ViewBindings.findChildViewById(rootView, id);
      if (textViewPdfPath == null) {
        break missingId;
      }

      id = R.id.textViewPdfProgress;
      TextView textViewPdfProgress = ViewBindings.findChildViewById(rootView, id);
      if (textViewPdfProgress == null) {
        break missingId;
      }

      id = R.id.textViewPdfSize;
      TextView textViewPdfSize = ViewBindings.findChildViewById(rootView, id);
      if (textViewPdfSize == null) {
        break missingId;
      }

      id = R.id.textViewPdfTitle;
      TextView textViewPdfTitle = ViewBindings.findChildViewById(rootView, id);
      if (textViewPdfTitle == null) {
        break missingId;
      }

      return new ItemPdfDocumentBinding((CardView) rootView, imageViewFavorite, progressBarReading,
          textViewPdfPages, textViewPdfPath, textViewPdfProgress, textViewPdfSize,
          textViewPdfTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
