// Generated by view binder compiler. Do not edit!
package com.ccppdfreader.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.ccppdfreader.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ViewPdfBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ProgressBar progressBarPdf;

  @NonNull
  public final RecyclerView recyclerViewPages;

  @NonNull
  public final TextView textViewError;

  private ViewPdfBinding(@NonNull LinearLayout rootView, @NonNull ProgressBar progressBarPdf,
      @NonNull RecyclerView recyclerViewPages, @NonNull TextView textViewError) {
    this.rootView = rootView;
    this.progressBarPdf = progressBarPdf;
    this.recyclerViewPages = recyclerViewPages;
    this.textViewError = textViewError;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ViewPdfBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ViewPdfBinding inflate(@NonNull LayoutInflater inflater, @Nullable ViewGroup parent,
      boolean attachToParent) {
    View root = inflater.inflate(R.layout.view_pdf, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ViewPdfBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.progressBarPdf;
      ProgressBar progressBarPdf = ViewBindings.findChildViewById(rootView, id);
      if (progressBarPdf == null) {
        break missingId;
      }

      id = R.id.recyclerViewPages;
      RecyclerView recyclerViewPages = ViewBindings.findChildViewById(rootView, id);
      if (recyclerViewPages == null) {
        break missingId;
      }

      id = R.id.textViewError;
      TextView textViewError = ViewBindings.findChildViewById(rootView, id);
      if (textViewError == null) {
        break missingId;
      }

      return new ViewPdfBinding((LinearLayout) rootView, progressBarPdf, recyclerViewPages,
          textViewError);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
