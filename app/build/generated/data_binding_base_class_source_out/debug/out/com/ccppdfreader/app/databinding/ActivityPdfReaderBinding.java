// Generated by view binder compiler. Do not edit!
package com.ccppdfreader.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.ccppdfreader.app.R;
import com.ccppdfreader.app.views.PDFView;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPdfReaderBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout layoutPageInfo;

  @NonNull
  public final PDFView pdfView;

  @NonNull
  public final TextView textViewPageInfo;

  @NonNull
  public final TextView textViewPdfPlaceholder;

  @NonNull
  public final TextView textViewProgress;

  @NonNull
  public final Toolbar toolbar;

  private ActivityPdfReaderBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout layoutPageInfo, @NonNull PDFView pdfView,
      @NonNull TextView textViewPageInfo, @NonNull TextView textViewPdfPlaceholder,
      @NonNull TextView textViewProgress, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.layoutPageInfo = layoutPageInfo;
    this.pdfView = pdfView;
    this.textViewPageInfo = textViewPageInfo;
    this.textViewPdfPlaceholder = textViewPdfPlaceholder;
    this.textViewProgress = textViewProgress;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPdfReaderBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPdfReaderBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_pdf_reader, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPdfReaderBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.layoutPageInfo;
      LinearLayout layoutPageInfo = ViewBindings.findChildViewById(rootView, id);
      if (layoutPageInfo == null) {
        break missingId;
      }

      id = R.id.pdfView;
      PDFView pdfView = ViewBindings.findChildViewById(rootView, id);
      if (pdfView == null) {
        break missingId;
      }

      id = R.id.textViewPageInfo;
      TextView textViewPageInfo = ViewBindings.findChildViewById(rootView, id);
      if (textViewPageInfo == null) {
        break missingId;
      }

      id = R.id.textViewPdfPlaceholder;
      TextView textViewPdfPlaceholder = ViewBindings.findChildViewById(rootView, id);
      if (textViewPdfPlaceholder == null) {
        break missingId;
      }

      id = R.id.textViewProgress;
      TextView textViewProgress = ViewBindings.findChildViewById(rootView, id);
      if (textViewProgress == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityPdfReaderBinding((LinearLayout) rootView, layoutPageInfo, pdfView,
          textViewPageInfo, textViewPdfPlaceholder, textViewProgress, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
