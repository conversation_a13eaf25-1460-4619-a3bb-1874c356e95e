// Generated by view binder compiler. Do not edit!
package com.ccppdfreader.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.ccppdfreader.app.R;
import com.github.barteksc.pdfviewer.PDFView;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.progressindicator.CircularProgressIndicator;
import com.google.android.material.slider.Slider;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityPdfReaderBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final LinearLayout bottomControls;

  @NonNull
  public final ImageButton btnCloseSearch;

  @NonNull
  public final ImageButton btnNextPage;

  @NonNull
  public final ImageButton btnPreviousPage;

  @NonNull
  public final ImageButton btnSearchNext;

  @NonNull
  public final ImageButton btnSearchPrevious;

  @NonNull
  public final ImageButton btnZoomIn;

  @NonNull
  public final ImageButton btnZoomOut;

  @NonNull
  public final EditText etSearch;

  @NonNull
  public final PDFView pdfView;

  @NonNull
  public final CircularProgressIndicator progressLoading;

  @NonNull
  public final MaterialCardView searchContainer;

  @NonNull
  public final Slider sliderPage;

  @NonNull
  public final MaterialToolbar toolbar;

  @NonNull
  public final TextView tvPageIndicator;

  @NonNull
  public final TextView tvZoomLevel;

  private ActivityPdfReaderBinding(@NonNull CoordinatorLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull LinearLayout bottomControls,
      @NonNull ImageButton btnCloseSearch, @NonNull ImageButton btnNextPage,
      @NonNull ImageButton btnPreviousPage, @NonNull ImageButton btnSearchNext,
      @NonNull ImageButton btnSearchPrevious, @NonNull ImageButton btnZoomIn,
      @NonNull ImageButton btnZoomOut, @NonNull EditText etSearch, @NonNull PDFView pdfView,
      @NonNull CircularProgressIndicator progressLoading, @NonNull MaterialCardView searchContainer,
      @NonNull Slider sliderPage, @NonNull MaterialToolbar toolbar,
      @NonNull TextView tvPageIndicator, @NonNull TextView tvZoomLevel) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.bottomControls = bottomControls;
    this.btnCloseSearch = btnCloseSearch;
    this.btnNextPage = btnNextPage;
    this.btnPreviousPage = btnPreviousPage;
    this.btnSearchNext = btnSearchNext;
    this.btnSearchPrevious = btnSearchPrevious;
    this.btnZoomIn = btnZoomIn;
    this.btnZoomOut = btnZoomOut;
    this.etSearch = etSearch;
    this.pdfView = pdfView;
    this.progressLoading = progressLoading;
    this.searchContainer = searchContainer;
    this.sliderPage = sliderPage;
    this.toolbar = toolbar;
    this.tvPageIndicator = tvPageIndicator;
    this.tvZoomLevel = tvZoomLevel;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityPdfReaderBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityPdfReaderBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_pdf_reader, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityPdfReaderBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_bar_layout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.bottom_controls;
      LinearLayout bottomControls = ViewBindings.findChildViewById(rootView, id);
      if (bottomControls == null) {
        break missingId;
      }

      id = R.id.btn_close_search;
      ImageButton btnCloseSearch = ViewBindings.findChildViewById(rootView, id);
      if (btnCloseSearch == null) {
        break missingId;
      }

      id = R.id.btn_next_page;
      ImageButton btnNextPage = ViewBindings.findChildViewById(rootView, id);
      if (btnNextPage == null) {
        break missingId;
      }

      id = R.id.btn_previous_page;
      ImageButton btnPreviousPage = ViewBindings.findChildViewById(rootView, id);
      if (btnPreviousPage == null) {
        break missingId;
      }

      id = R.id.btn_search_next;
      ImageButton btnSearchNext = ViewBindings.findChildViewById(rootView, id);
      if (btnSearchNext == null) {
        break missingId;
      }

      id = R.id.btn_search_previous;
      ImageButton btnSearchPrevious = ViewBindings.findChildViewById(rootView, id);
      if (btnSearchPrevious == null) {
        break missingId;
      }

      id = R.id.btn_zoom_in;
      ImageButton btnZoomIn = ViewBindings.findChildViewById(rootView, id);
      if (btnZoomIn == null) {
        break missingId;
      }

      id = R.id.btn_zoom_out;
      ImageButton btnZoomOut = ViewBindings.findChildViewById(rootView, id);
      if (btnZoomOut == null) {
        break missingId;
      }

      id = R.id.et_search;
      EditText etSearch = ViewBindings.findChildViewById(rootView, id);
      if (etSearch == null) {
        break missingId;
      }

      id = R.id.pdf_view;
      PDFView pdfView = ViewBindings.findChildViewById(rootView, id);
      if (pdfView == null) {
        break missingId;
      }

      id = R.id.progress_loading;
      CircularProgressIndicator progressLoading = ViewBindings.findChildViewById(rootView, id);
      if (progressLoading == null) {
        break missingId;
      }

      id = R.id.search_container;
      MaterialCardView searchContainer = ViewBindings.findChildViewById(rootView, id);
      if (searchContainer == null) {
        break missingId;
      }

      id = R.id.slider_page;
      Slider sliderPage = ViewBindings.findChildViewById(rootView, id);
      if (sliderPage == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tv_page_indicator;
      TextView tvPageIndicator = ViewBindings.findChildViewById(rootView, id);
      if (tvPageIndicator == null) {
        break missingId;
      }

      id = R.id.tv_zoom_level;
      TextView tvZoomLevel = ViewBindings.findChildViewById(rootView, id);
      if (tvZoomLevel == null) {
        break missingId;
      }

      return new ActivityPdfReaderBinding((CoordinatorLayout) rootView, appBarLayout,
          bottomControls, btnCloseSearch, btnNextPage, btnPreviousPage, btnSearchNext,
          btnSearchPrevious, btnZoomIn, btnZoomOut, etSearch, pdfView, progressLoading,
          searchContainer, sliderPage, toolbar, tvPageIndicator, tvZoomLevel);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
