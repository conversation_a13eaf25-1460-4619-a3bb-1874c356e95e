com.ccppdfreader.app.debug:xml/file_paths = 0x7f110002
com.ccppdfreader.app.debug:xml/backup_rules = 0x7f110000
com.ccppdfreader.app.debug:styleable/include = 0x7f100054
com.ccppdfreader.app.debug:styleable/ViewStubCompat = 0x7f100052
com.ccppdfreader.app.debug:styleable/View = 0x7f100050
com.ccppdfreader.app.debug:styleable/Toolbar = 0x7f10004c
com.ccppdfreader.app.debug:styleable/TextEffects = 0x7f10004b
com.ccppdfreader.app.debug:styleable/SwitchCompat = 0x7f100049
com.ccppdfreader.app.debug:styleable/Spinner = 0x7f100044
com.ccppdfreader.app.debug:styleable/RecyclerView = 0x7f100042
com.ccppdfreader.app.debug:styleable/RecycleListView = 0x7f100041
com.ccppdfreader.app.debug:styleable/PropertySet = 0x7f100040
com.ccppdfreader.app.debug:styleable/PopupWindowBackgroundState = 0x7f10003f
com.ccppdfreader.app.debug:styleable/PopupWindow = 0x7f10003e
com.ccppdfreader.app.debug:styleable/OnSwipe = 0x7f10003d
com.ccppdfreader.app.debug:styleable/MotionTelltales = 0x7f10003b
com.ccppdfreader.app.debug:styleable/MotionScene = 0x7f10003a
com.ccppdfreader.app.debug:styleable/MotionLabel = 0x7f100038
com.ccppdfreader.app.debug:styleable/MotionHelper = 0x7f100037
com.ccppdfreader.app.debug:styleable/MotionEffect = 0x7f100036
com.ccppdfreader.app.debug:styleable/Motion = 0x7f100035
com.ccppdfreader.app.debug:styleable/MenuView = 0x7f100033
com.ccppdfreader.app.debug:styleable/MenuItem = 0x7f100032
com.ccppdfreader.app.debug:styleable/LinearLayoutCompat = 0x7f10002e
com.ccppdfreader.app.debug:styleable/Layout = 0x7f10002d
com.ccppdfreader.app.debug:styleable/KeyTrigger = 0x7f10002c
com.ccppdfreader.app.debug:styleable/KeyTimeCycle = 0x7f10002b
com.ccppdfreader.app.debug:styleable/KeyPosition = 0x7f10002a
com.ccppdfreader.app.debug:styleable/KeyFrame = 0x7f100027
com.ccppdfreader.app.debug:styleable/KeyCycle = 0x7f100026
com.ccppdfreader.app.debug:styleable/ImageFilterView = 0x7f100024
com.ccppdfreader.app.debug:styleable/GradientColor = 0x7f100022
com.ccppdfreader.app.debug:styleable/FragmentContainerView = 0x7f100021
com.ccppdfreader.app.debug:styleable/Fragment = 0x7f100020
com.ccppdfreader.app.debug:styleable/FontFamilyFont = 0x7f10001f
com.ccppdfreader.app.debug:styleable/CustomAttribute = 0x7f10001c
com.ccppdfreader.app.debug:styleable/ConstraintOverride = 0x7f10001a
com.ccppdfreader.app.debug:styleable/Constraint = 0x7f100016
com.ccppdfreader.app.debug:styleable/CheckedTextView = 0x7f100013
com.ccppdfreader.app.debug:styleable/Carousel = 0x7f100012
com.ccppdfreader.app.debug:styleable/ButtonBarLayout = 0x7f100010
com.ccppdfreader.app.debug:styleable/AppCompatTheme = 0x7f10000f
com.ccppdfreader.app.debug:styleable/SearchView = 0x7f100043
com.ccppdfreader.app.debug:styleable/AppCompatTextHelper = 0x7f10000d
com.ccppdfreader.app.debug:styleable/AlertDialog = 0x7f100006
com.ccppdfreader.app.debug:styleable/ActivityChooserView = 0x7f100005
com.ccppdfreader.app.debug:styleable/ActionMenuView = 0x7f100003
com.ccppdfreader.app.debug:style/Widget.Compat.NotificationActionText = 0x7f0f0160
com.ccppdfreader.app.debug:styleable/Transition = 0x7f10004e
com.ccppdfreader.app.debug:style/Widget.Compat.NotificationActionContainer = 0x7f0f015f
com.ccppdfreader.app.debug:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f0f015c
com.ccppdfreader.app.debug:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f0f0159
com.ccppdfreader.app.debug:style/Widget.AppCompat.Spinner.DropDown = 0x7f0f0158
com.ccppdfreader.app.debug:style/Widget.AppCompat.Spinner = 0x7f0f0157
com.ccppdfreader.app.debug:style/Widget.AppCompat.SeekBar.Discrete = 0x7f0f0156
com.ccppdfreader.app.debug:style/Widget.AppCompat.SearchView.ActionBar = 0x7f0f0154
com.ccppdfreader.app.debug:style/Widget.AppCompat.RatingBar.Small = 0x7f0f0152
com.ccppdfreader.app.debug:style/Widget.AppCompat.PopupMenu = 0x7f0f014b
com.ccppdfreader.app.debug:style/Widget.AppCompat.ListView.Menu = 0x7f0f014a
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.SearchView = 0x7f0f0144
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f0f013e
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f0f013d
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f0f013c
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f0f0138
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f0f0131
com.ccppdfreader.app.debug:style/Widget.AppCompat.ImageButton = 0x7f0f012f
com.ccppdfreader.app.debug:style/Widget.AppCompat.EditText = 0x7f0f012e
com.ccppdfreader.app.debug:style/Widget.AppCompat.CompoundButton.Switch = 0x7f0f012b
com.ccppdfreader.app.debug:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f0f012a
com.ccppdfreader.app.debug:style/Widget.AppCompat.Button.Colored = 0x7f0f0125
com.ccppdfreader.app.debug:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f0f0123
com.ccppdfreader.app.debug:style/Widget.AppCompat.Button.Borderless = 0x7f0f0122
com.ccppdfreader.app.debug:style/Widget.AppCompat.ActionButton.Overflow = 0x7f0f011d
com.ccppdfreader.app.debug:style/Widget.AppCompat.ActionButton = 0x7f0f011b
com.ccppdfreader.app.debug:style/Widget.AppCompat.ActionBar.TabText = 0x7f0f0119
com.ccppdfreader.app.debug:style/Widget.AppCompat.ActionBar.TabBar = 0x7f0f0118
com.ccppdfreader.app.debug:styleable/GradientColorItem = 0x7f100023
com.ccppdfreader.app.debug:style/Widget.AppCompat.ActionBar.Solid = 0x7f0f0117
com.ccppdfreader.app.debug:style/Widget.AppCompat.ActionBar = 0x7f0f0116
com.ccppdfreader.app.debug:style/ThemeOverlay.AppCompat.Light = 0x7f0f0115
com.ccppdfreader.app.debug:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f0f0112
com.ccppdfreader.app.debug:style/ThemeOverlay.AppCompat.ActionBar = 0x7f0f010e
com.ccppdfreader.app.debug:style/ThemeOverlay.AppCompat = 0x7f0f010d
com.ccppdfreader.app.debug:style/Theme.AppCompat.NoActionBar = 0x7f0f0109
com.ccppdfreader.app.debug:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f0f0105
com.ccppdfreader.app.debug:style/Theme.AppCompat.Light.Dialog = 0x7f0f0104
com.ccppdfreader.app.debug:style/Theme.AppCompat.Empty = 0x7f0f0101
com.ccppdfreader.app.debug:style/Theme.AppCompat.DialogWhenLarge = 0x7f0f0100
com.ccppdfreader.app.debug:style/Theme.AppCompat.Dialog.MinWidth = 0x7f0f00ff
com.ccppdfreader.app.debug:style/Theme.AppCompat.Dialog.Alert = 0x7f0f00fe
com.ccppdfreader.app.debug:style/Theme.AppCompat.Dialog = 0x7f0f00fd
com.ccppdfreader.app.debug:styleable/ActionBar = 0x7f100000
com.ccppdfreader.app.debug:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f0f00fc
com.ccppdfreader.app.debug:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f0f00fa
com.ccppdfreader.app.debug:style/Theme.AppCompat.DayNight.Dialog = 0x7f0f00f8
com.ccppdfreader.app.debug:style/Theme.AppCompat.CompactMenu = 0x7f0f00f5
com.ccppdfreader.app.debug:style/Theme.AppCompat = 0x7f0f00f4
com.ccppdfreader.app.debug:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0f0106
com.ccppdfreader.app.debug:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0f00f3
com.ccppdfreader.app.debug:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0f00f2
com.ccppdfreader.app.debug:style/TextAppearance.Compat.Notification.Time = 0x7f0f00ef
com.ccppdfreader.app.debug:style/TextAppearance.Compat.Notification.Line2 = 0x7f0f00ee
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.Switch = 0x7f0f00ea
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0f00e8
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0f00e7
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0f00e6
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0f00e5
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.Button = 0x7f0f00e2
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f0f00e1
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f0f00df
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0f00de
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0f00dd
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0f00dc
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0f00db
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0f00da
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Tooltip = 0x7f0f00d8
com.ccppdfreader.app.debug:styleable/AnimatedStateListDrawableTransition = 0x7f100009
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Title.Inverse = 0x7f0f00d7
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Title = 0x7f0f00d6
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Subhead = 0x7f0f00d4
com.ccppdfreader.app.debug:styleable/AppCompatSeekBar = 0x7f10000c
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Small.Inverse = 0x7f0f00d3
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Small = 0x7f0f00d2
com.ccppdfreader.app.debug:styleable/ListPopupWindow = 0x7f100030
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f0f00d1
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Menu = 0x7f0f00cf
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f0f00ce
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0f00cc
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f0f00c9
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Headline = 0x7f0f00c5
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Display4 = 0x7f0f00c4
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Display2 = 0x7f0f00c2
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Caption = 0x7f0f00c0
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Button = 0x7f0f00bf
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Body1 = 0x7f0f00bd
com.ccppdfreader.app.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f0f00b8
com.ccppdfreader.app.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f0f00b7
com.ccppdfreader.app.debug:styleable/AnimatedStateListDrawableItem = 0x7f100008
com.ccppdfreader.app.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f0f00b5
com.ccppdfreader.app.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f0f00b3
com.ccppdfreader.app.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f0f00b1
com.ccppdfreader.app.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f0f00b0
com.ccppdfreader.app.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f0f00af
com.ccppdfreader.app.debug:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f0f00ac
com.ccppdfreader.app.debug:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f0f00ab
com.ccppdfreader.app.debug:style/Platform.Widget.AppCompat.Spinner = 0x7f0f00aa
com.ccppdfreader.app.debug:style/Platform.V25.AppCompat.Light = 0x7f0f00a9
com.ccppdfreader.app.debug:style/Platform.V21.AppCompat = 0x7f0f00a6
com.ccppdfreader.app.debug:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f0f00a4
com.ccppdfreader.app.debug:style/Platform.AppCompat.Light = 0x7f0f00a2
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0f00a0
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Toolbar = 0x7f0f009f
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f0f009e
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f0f009c
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.SeekBar = 0x7f0f0099
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f0f0095
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ProgressBar = 0x7f0f0092
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.PopupWindow = 0x7f0f0091
com.ccppdfreader.app.debug:styleable/AppCompatTextView = 0x7f10000e
com.ccppdfreader.app.debug:style/Widget.AppCompat.AutoCompleteTextView = 0x7f0f0120
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f0f008b
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ListMenuView = 0x7f0f008a
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0f0089
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f0f0087
com.ccppdfreader.app.debug:style/TextAppearance.Compat.Notification.Title = 0x7f0f00f0
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f0f0085
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0f0084
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f0f0083
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.EditText = 0x7f0f0080
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f0f007d
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f0f007c
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0f0079
com.ccppdfreader.app.debug:style/Widget.AppCompat.ActivityChooserView = 0x7f0f011f
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Button.Colored = 0x7f0f0076
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f0f0074
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f0f0070
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ActionMode = 0x7f0f006f
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ActionButton = 0x7f0f006c
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f0f006a
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f0f0069
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f0f0068
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ActionBar = 0x7f0f0067
com.ccppdfreader.app.debug:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f0f0066
com.ccppdfreader.app.debug:style/Base.V7.Widget.AppCompat.EditText = 0x7f0f0065
com.ccppdfreader.app.debug:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f0f0064
com.ccppdfreader.app.debug:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f0f0063
com.ccppdfreader.app.debug:style/Base.V7.Theme.AppCompat.Light = 0x7f0f0061
com.ccppdfreader.app.debug:style/Base.V7.Theme.AppCompat.Dialog = 0x7f0f0060
com.ccppdfreader.app.debug:style/Base.V28.Theme.AppCompat.Light = 0x7f0f005e
com.ccppdfreader.app.debug:style/Base.V26.Theme.AppCompat.Light = 0x7f0f005b
com.ccppdfreader.app.debug:style/Base.V26.Theme.AppCompat = 0x7f0f005a
com.ccppdfreader.app.debug:style/Base.V23.Theme.AppCompat.Light = 0x7f0f0059
com.ccppdfreader.app.debug:style/Base.V23.Theme.AppCompat = 0x7f0f0058
com.ccppdfreader.app.debug:style/Base.V22.Theme.AppCompat.Light = 0x7f0f0057
com.ccppdfreader.app.debug:style/Base.V22.Theme.AppCompat = 0x7f0f0056
com.ccppdfreader.app.debug:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f0f0055
com.ccppdfreader.app.debug:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f0f0054
com.ccppdfreader.app.debug:style/Base.V21.Theme.AppCompat.Dialog = 0x7f0f0052
com.ccppdfreader.app.debug:style/Theme.CCPPDFReader = 0x7f0f010a
com.ccppdfreader.app.debug:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0f004f
com.ccppdfreader.app.debug:style/Widget.AppCompat.Toolbar = 0x7f0f015d
com.ccppdfreader.app.debug:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f0f004e
com.ccppdfreader.app.debug:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f0f004c
com.ccppdfreader.app.debug:styleable/Capability = 0x7f100011
com.ccppdfreader.app.debug:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f0f004b
com.ccppdfreader.app.debug:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0f0048
com.ccppdfreader.app.debug:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f0f0044
com.ccppdfreader.app.debug:style/Base.Theme.AppCompat.Light = 0x7f0f0043
com.ccppdfreader.app.debug:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f0f0041
com.ccppdfreader.app.debug:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f0f003f
com.ccppdfreader.app.debug:style/Base.Theme.AppCompat.CompactMenu = 0x7f0f003d
com.ccppdfreader.app.debug:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0f003b
com.ccppdfreader.app.debug:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0f003a
com.ccppdfreader.app.debug:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0f0039
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ActionBar = 0x7f0f0130
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0f0038
com.ccppdfreader.app.debug:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f0f00bb
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0f0036
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0f0035
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0f0034
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0f0033
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0f0032
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0f0031
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0f0030
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f0f002f
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0f002d
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0f002c
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0f0029
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0f0028
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f0f0026
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Title = 0x7f0f0025
com.ccppdfreader.app.debug:styleable/CompoundButton = 0x7f100015
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f0f0024
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Subhead = 0x7f0f0023
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f0f0022
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f0f0020
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0f001f
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Medium = 0x7f0f001b
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0f001a
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0f0019
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f0f0018
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Large = 0x7f0f0017
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Inverse = 0x7f0f0016
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Display4 = 0x7f0f0014
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Display3 = 0x7f0f0013
com.ccppdfreader.app.debug:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f0f0049
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Display1 = 0x7f0f0011
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Caption = 0x7f0f0010
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Body2 = 0x7f0f000e
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Body1 = 0x7f0f000d
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Medium = 0x7f0f00cd
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat = 0x7f0f000c
com.ccppdfreader.app.debug:style/Base.Animation.AppCompat.Tooltip = 0x7f0f0009
com.ccppdfreader.app.debug:style/Base.Animation.AppCompat.DropDownUp = 0x7f0f0008
com.ccppdfreader.app.debug:style/Base.AlertDialog.AppCompat.Light = 0x7f0f0006
com.ccppdfreader.app.debug:style/Base.AlertDialog.AppCompat = 0x7f0f0005
com.ccppdfreader.app.debug:style/Animation.AppCompat.Dialog = 0x7f0f0002
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f0f0134
com.ccppdfreader.app.debug:style/AlertDialog.AppCompat = 0x7f0f0000
com.ccppdfreader.app.debug:string/zoom_level = 0x7f0e005f
com.ccppdfreader.app.debug:string/yes = 0x7f0e005e
com.ccppdfreader.app.debug:string/welcome_title = 0x7f0e005d
com.ccppdfreader.app.debug:string/welcome_message = 0x7f0e005b
com.ccppdfreader.app.debug:string/theme_system = 0x7f0e005a
com.ccppdfreader.app.debug:attr/actionBarTabStyle = 0x7f030009
com.ccppdfreader.app.debug:string/status_bar_notification_info_overflow = 0x7f0e0056
com.ccppdfreader.app.debug:string/settings_theme = 0x7f0e0054
com.ccppdfreader.app.debug:string/settings_reading = 0x7f0e0053
com.ccppdfreader.app.debug:string/search_hint = 0x7f0e004e
com.ccppdfreader.app.debug:string/retry = 0x7f0e004d
com.ccppdfreader.app.debug:id/notification_main_column = 0x7f0800d8
com.ccppdfreader.app.debug:string/page_indicator = 0x7f0e0046
com.ccppdfreader.app.debug:styleable/ViewTransition = 0x7f100053
com.ccppdfreader.app.debug:string/open_pdf = 0x7f0e0045
com.ccppdfreader.app.debug:string/ok = 0x7f0e0044
com.ccppdfreader.app.debug:id/neverCompleteToStart = 0x7f0800d2
com.ccppdfreader.app.debug:string/no_recent_files = 0x7f0e0041
com.ccppdfreader.app.debug:color/md_theme_dark_surface = 0x7f050058
com.ccppdfreader.app.debug:attr/motionEffect_end = 0x7f030165
com.ccppdfreader.app.debug:color/status_bar_dark = 0x7f05008f
com.ccppdfreader.app.debug:string/no_bookmarks = 0x7f0e003f
com.ccppdfreader.app.debug:string/nav_settings = 0x7f0e003d
com.ccppdfreader.app.debug:attr/buttonBarButtonStyle = 0x7f03004b
com.ccppdfreader.app.debug:attr/textOutlineThickness = 0x7f0301f3
com.ccppdfreader.app.debug:string/nav_home = 0x7f0e003b
com.ccppdfreader.app.debug:string/loading = 0x7f0e0038
com.ccppdfreader.app.debug:attr/drawerArrowStyle = 0x7f0300b9
com.ccppdfreader.app.debug:string/file_cannot_open = 0x7f0e0034
com.ccppdfreader.app.debug:string/error_generic = 0x7f0e0031
com.ccppdfreader.app.debug:string/call_notification_hang_up_action = 0x7f0e002a
com.ccppdfreader.app.debug:attr/indeterminateProgressStyle = 0x7f0300fb
com.ccppdfreader.app.debug:attr/layout_constraintBaseline_toBaselineOf = 0x7f030109
com.ccppdfreader.app.debug:string/error_file_access = 0x7f0e0030
com.ccppdfreader.app.debug:color/md_theme_dark_tertiary = 0x7f05005a
com.ccppdfreader.app.debug:string/error = 0x7f0e002f
com.ccppdfreader.app.debug:string/permission_storage_message = 0x7f0e0049
com.ccppdfreader.app.debug:attr/layout_constraintTop_toBottomOf = 0x7f03012a
com.ccppdfreader.app.debug:string/cancel = 0x7f0e002e
com.ccppdfreader.app.debug:string/call_notification_screening_text = 0x7f0e002d
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f0f007b
com.ccppdfreader.app.debug:attr/commitIcon = 0x7f03007f
com.ccppdfreader.app.debug:string/call_notification_ongoing_text = 0x7f0e002c
com.ccppdfreader.app.debug:string/call_notification_decline_action = 0x7f0e0029
com.ccppdfreader.app.debug:string/app_description = 0x7f0e0023
com.ccppdfreader.app.debug:color/md_theme_dark_secondaryContainer = 0x7f050057
com.ccppdfreader.app.debug:string/androidx_startup = 0x7f0e0022
com.ccppdfreader.app.debug:color/splash_icon_tint = 0x7f05008e
com.ccppdfreader.app.debug:string/action_search = 0x7f0e001e
com.ccppdfreader.app.debug:string/action_bookmark = 0x7f0e001b
com.ccppdfreader.app.debug:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0f0114
com.ccppdfreader.app.debug:string/abc_shareactionprovider_share_with = 0x7f0e0018
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0f00e9
com.ccppdfreader.app.debug:string/abc_searchview_description_clear = 0x7f0e0013
com.ccppdfreader.app.debug:style/Base.V21.Theme.AppCompat = 0x7f0f0051
com.ccppdfreader.app.debug:string/abc_search_hint = 0x7f0e0012
com.ccppdfreader.app.debug:color/md_theme_dark_onErrorContainer = 0x7f05004a
com.ccppdfreader.app.debug:string/abc_prepend_shortcut_label = 0x7f0e0011
com.ccppdfreader.app.debug:string/call_notification_answer_video_action = 0x7f0e0028
com.ccppdfreader.app.debug:string/abc_menu_shift_shortcut_label = 0x7f0e000e
com.ccppdfreader.app.debug:string/abc_menu_alt_shortcut_label = 0x7f0e0008
com.ccppdfreader.app.debug:string/abc_action_mode_done = 0x7f0e0003
com.ccppdfreader.app.debug:attr/flow_horizontalBias = 0x7f0300ce
com.ccppdfreader.app.debug:string/abc_action_bar_up_description = 0x7f0e0001
com.ccppdfreader.app.debug:menu/pdf_reader_menu = 0x7f0c0004
com.ccppdfreader.app.debug:style/Widget.AppCompat.ListView.DropDown = 0x7f0f0149
com.ccppdfreader.app.debug:attr/editTextStyle = 0x7f0300bf
com.ccppdfreader.app.debug:menu/file_explorer_menu = 0x7f0c0002
com.ccppdfreader.app.debug:layout/notification_action = 0x7f0b0029
com.ccppdfreader.app.debug:layout/select_dialog_multichoice_material = 0x7f0b0030
com.ccppdfreader.app.debug:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f0f005c
com.ccppdfreader.app.debug:string/settings_about = 0x7f0e0051
com.ccppdfreader.app.debug:layout/notification_template_icon_group = 0x7f0b002c
com.ccppdfreader.app.debug:style/Widget.AppCompat.SeekBar = 0x7f0f0155
com.ccppdfreader.app.debug:string/permission_grant = 0x7f0e0048
com.ccppdfreader.app.debug:drawable/ic_call_answer_video_low = 0x7f07005c
com.ccppdfreader.app.debug:drawable/notification_bg_normal = 0x7f070083
com.ccppdfreader.app.debug:layout/notification_action_tombstone = 0x7f0b002a
com.ccppdfreader.app.debug:layout/item_file = 0x7f0b0026
com.ccppdfreader.app.debug:layout/item_bookmark = 0x7f0b0025
com.ccppdfreader.app.debug:layout/ime_secondary_split_test_activity = 0x7f0b0024
com.ccppdfreader.app.debug:layout/activity_splash = 0x7f0b0021
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f0f0135
com.ccppdfreader.app.debug:layout/activity_pdf_reader = 0x7f0b001f
com.ccppdfreader.app.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f0f00b4
com.ccppdfreader.app.debug:layout/abc_tooltip = 0x7f0b001b
com.ccppdfreader.app.debug:attr/layout_constraintBottom_toBottomOf = 0x7f03010d
com.ccppdfreader.app.debug:layout/abc_screen_toolbar = 0x7f0b0017
com.ccppdfreader.app.debug:layout/abc_screen_simple = 0x7f0b0015
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f0f0098
com.ccppdfreader.app.debug:id/view_tree_saved_state_registry_owner = 0x7f080152
com.ccppdfreader.app.debug:layout/abc_screen_content_include = 0x7f0b0014
com.ccppdfreader.app.debug:layout/abc_list_menu_item_checkbox = 0x7f0b000e
com.ccppdfreader.app.debug:layout/abc_expanded_menu_layout = 0x7f0b000d
com.ccppdfreader.app.debug:layout/abc_alert_dialog_title_material = 0x7f0b000a
com.ccppdfreader.app.debug:layout/abc_list_menu_item_layout = 0x7f0b0010
com.ccppdfreader.app.debug:drawable/abc_ic_ab_back_material = 0x7f070015
com.ccppdfreader.app.debug:layout/abc_alert_dialog_material = 0x7f0b0009
com.ccppdfreader.app.debug:color/material_blue_grey_900 = 0x7f050037
com.ccppdfreader.app.debug:layout/abc_activity_chooser_view = 0x7f0b0006
com.ccppdfreader.app.debug:animator/fragment_close_enter = 0x7f020000
com.ccppdfreader.app.debug:layout/abc_action_mode_bar = 0x7f0b0004
com.ccppdfreader.app.debug:id/ifRoom = 0x7f0800b1
com.ccppdfreader.app.debug:layout/abc_action_menu_layout = 0x7f0b0003
com.ccppdfreader.app.debug:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
com.ccppdfreader.app.debug:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
com.ccppdfreader.app.debug:integer/status_bar_notification_info_maxnum = 0x7f090004
com.ccppdfreader.app.debug:id/east = 0x7f080099
com.ccppdfreader.app.debug:integer/config_tooltipAnimTime = 0x7f090003
com.ccppdfreader.app.debug:attr/listPreferredItemHeightLarge = 0x7f03014b
com.ccppdfreader.app.debug:id/x_right = 0x7f08015c
com.ccppdfreader.app.debug:dimen/pdf_toolbar_height = 0x7f060092
com.ccppdfreader.app.debug:id/search_close_btn = 0x7f0800fe
com.ccppdfreader.app.debug:layout/notification_template_part_time = 0x7f0b002e
com.ccppdfreader.app.debug:id/withText = 0x7f080157
com.ccppdfreader.app.debug:id/west = 0x7f080156
com.ccppdfreader.app.debug:string/abc_activity_chooser_view_see_all = 0x7f0e0004
com.ccppdfreader.app.debug:style/Widget.AppCompat.ActionMode = 0x7f0f011e
com.ccppdfreader.app.debug:layout/abc_dialog_title_material = 0x7f0b000c
com.ccppdfreader.app.debug:id/visible = 0x7f080154
com.ccppdfreader.app.debug:string/add_to_favorites = 0x7f0e0021
com.ccppdfreader.app.debug:id/up = 0x7f08014c
com.ccppdfreader.app.debug:styleable/LinearLayoutCompat_Layout = 0x7f10002f
com.ccppdfreader.app.debug:id/checkbox = 0x7f080073
com.ccppdfreader.app.debug:id/triangle = 0x7f080148
com.ccppdfreader.app.debug:drawable/ic_bookmark = 0x7f070058
com.ccppdfreader.app.debug:id/chronometer = 0x7f080075
com.ccppdfreader.app.debug:id/transitionToStart = 0x7f080147
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0f0133
com.ccppdfreader.app.debug:layout/activity_main = 0x7f0b001e
com.ccppdfreader.app.debug:id/transitionToEnd = 0x7f080146
com.ccppdfreader.app.debug:color/bright_foreground_inverse_material_light = 0x7f050025
com.ccppdfreader.app.debug:id/topPanel = 0x7f080145
com.ccppdfreader.app.debug:id/toolbar = 0x7f080143
com.ccppdfreader.app.debug:dimen/spacing_md = 0x7f060095
com.ccppdfreader.app.debug:id/showHome = 0x7f08010a
com.ccppdfreader.app.debug:layout/support_simple_spinner_dropdown_item = 0x7f0b0032
com.ccppdfreader.app.debug:id/textViewFileSize = 0x7f08013a
com.ccppdfreader.app.debug:id/textViewCurrentPath = 0x7f080138
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0f002e
com.ccppdfreader.app.debug:attr/waveDecay = 0x7f03022b
com.ccppdfreader.app.debug:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
com.ccppdfreader.app.debug:id/textViewBookmarkTitle = 0x7f080137
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Large.Inverse = 0x7f0f00c8
com.ccppdfreader.app.debug:id/textViewBookmarkPage = 0x7f080136
com.ccppdfreader.app.debug:attr/customStringValue = 0x7f03009d
com.ccppdfreader.app.debug:id/textSpacerNoTitle = 0x7f080135
com.ccppdfreader.app.debug:color/abc_search_url_text = 0x7f05000d
com.ccppdfreader.app.debug:id/tag_unhandled_key_listeners = 0x7f080130
com.ccppdfreader.app.debug:styleable/OnClick = 0x7f10003c
com.ccppdfreader.app.debug:drawable/abc_edit_text_material = 0x7f070014
com.ccppdfreader.app.debug:id/tag_state_description = 0x7f08012d
com.ccppdfreader.app.debug:id/tag_screen_reader_focusable = 0x7f08012c
com.ccppdfreader.app.debug:id/tag_accessibility_pane_title = 0x7f080128
com.ccppdfreader.app.debug:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0018
com.ccppdfreader.app.debug:id/tabMode = 0x7f080124
com.ccppdfreader.app.debug:id/tag_window_insets_animation_callback = 0x7f080131
com.ccppdfreader.app.debug:id/staticPostLayout = 0x7f08011f
com.ccppdfreader.app.debug:anim/fade_out = 0x7f010019
com.ccppdfreader.app.debug:id/standard = 0x7f08011a
com.ccppdfreader.app.debug:layout/select_dialog_item_material = 0x7f0b002f
com.ccppdfreader.app.debug:layout/activity_file_explorer = 0x7f0b001d
com.ccppdfreader.app.debug:id/toggle = 0x7f080142
com.ccppdfreader.app.debug:id/src_atop = 0x7f080117
com.ccppdfreader.app.debug:string/abc_searchview_description_voice = 0x7f0e0017
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0f0086
com.ccppdfreader.app.debug:color/notification_action_color_filter = 0x7f050076
com.ccppdfreader.app.debug:id/spring = 0x7f080115
com.ccppdfreader.app.debug:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.ccppdfreader.app.debug:id/split_action_bar = 0x7f080112
com.ccppdfreader.app.debug:attr/submitBackground = 0x7f0301d2
com.ccppdfreader.app.debug:id/south = 0x7f08010e
com.ccppdfreader.app.debug:string/call_notification_incoming_text = 0x7f0e002b
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.RatingBar = 0x7f0f0094
com.ccppdfreader.app.debug:style/Base.Theme.AppCompat.Dialog = 0x7f0f003e
com.ccppdfreader.app.debug:id/skipped = 0x7f08010d
com.ccppdfreader.app.debug:styleable/ConstraintLayout_ReactiveGuide = 0x7f100018
com.ccppdfreader.app.debug:color/md_theme_light_primaryContainer = 0x7f05006f
com.ccppdfreader.app.debug:string/file_not_found = 0x7f0e0037
com.ccppdfreader.app.debug:attr/actionBarDivider = 0x7f030002
com.ccppdfreader.app.debug:string/loading_document = 0x7f0e0039
com.ccppdfreader.app.debug:id/sin = 0x7f08010c
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f0f0132
com.ccppdfreader.app.debug:attr/textPanX = 0x7f0301f4
com.ccppdfreader.app.debug:dimen/text_size_headline_small = 0x7f0600a1
com.ccppdfreader.app.debug:id/showTitle = 0x7f08010b
com.ccppdfreader.app.debug:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f080151
com.ccppdfreader.app.debug:id/search_voice_btn = 0x7f080104
com.ccppdfreader.app.debug:id/search_plate = 0x7f080102
com.ccppdfreader.app.debug:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
com.ccppdfreader.app.debug:attr/SharedValueId = 0x7f030001
com.ccppdfreader.app.debug:id/ratio = 0x7f0800ed
com.ccppdfreader.app.debug:id/wrap = 0x7f080158
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Button.Small = 0x7f0f0077
com.ccppdfreader.app.debug:id/search_mag_icon = 0x7f080101
com.ccppdfreader.app.debug:drawable/ic_storage = 0x7f07007a
com.ccppdfreader.app.debug:id/search_edit_frame = 0x7f0800ff
com.ccppdfreader.app.debug:id/scrollIndicatorDown = 0x7f0800f8
com.ccppdfreader.app.debug:id/screen = 0x7f0800f7
com.ccppdfreader.app.debug:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
com.ccppdfreader.app.debug:dimen/spacing_lg = 0x7f060094
com.ccppdfreader.app.debug:id/scrollIndicatorUp = 0x7f0800f9
com.ccppdfreader.app.debug:id/right_side = 0x7f0800f5
com.ccppdfreader.app.debug:layout/activity_bookmarks = 0x7f0b001c
com.ccppdfreader.app.debug:id/recyclerViewRecentFiles = 0x7f0800f1
com.ccppdfreader.app.debug:id/progress_horizontal = 0x7f0800ea
com.ccppdfreader.app.debug:id/position = 0x7f0800e7
com.ccppdfreader.app.debug:attr/colorPrimary = 0x7f03007c
com.ccppdfreader.app.debug:id/percent = 0x7f0800e5
com.ccppdfreader.app.debug:style/Base.Theme.AppCompat = 0x7f0f003c
com.ccppdfreader.app.debug:id/path = 0x7f0800e2
com.ccppdfreader.app.debug:id/parentRelative = 0x7f0800e1
com.ccppdfreader.app.debug:string/abc_searchview_description_submit = 0x7f0e0016
com.ccppdfreader.app.debug:id/packed = 0x7f0800de
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0f0143
com.ccppdfreader.app.debug:id/overshoot = 0x7f0800dd
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Spinner = 0x7f0f009b
com.ccppdfreader.app.debug:attr/listPopupWindowStyle = 0x7f030149
com.ccppdfreader.app.debug:color/md_theme_dark_onError = 0x7f050049
com.ccppdfreader.app.debug:color/material_grey_800 = 0x7f05003f
com.ccppdfreader.app.debug:id/on = 0x7f0800db
com.ccppdfreader.app.debug:styleable/MenuGroup = 0x7f100031
com.ccppdfreader.app.debug:attr/touchAnchorSide = 0x7f030214
com.ccppdfreader.app.debug:layout/select_dialog_singlechoice_material = 0x7f0b0031
com.ccppdfreader.app.debug:id/none = 0x7f0800d4
com.ccppdfreader.app.debug:attr/initialActivityCount = 0x7f0300fc
com.ccppdfreader.app.debug:string/welcome_subtitle = 0x7f0e005c
com.ccppdfreader.app.debug:attr/dialogTheme = 0x7f0300a6
com.ccppdfreader.app.debug:layout/abc_action_bar_title_item = 0x7f0b0000
com.ccppdfreader.app.debug:id/noState = 0x7f0800d3
com.ccppdfreader.app.debug:id/nav_settings = 0x7f0800cf
com.ccppdfreader.app.debug:id/nav_favorites = 0x7f0800cc
com.ccppdfreader.app.debug:id/motion_base = 0x7f0800ca
com.ccppdfreader.app.debug:dimen/button_corner_radius = 0x7f060053
com.ccppdfreader.app.debug:id/middle = 0x7f0800c9
com.ccppdfreader.app.debug:id/text = 0x7f080132
com.ccppdfreader.app.debug:mipmap/ic_launcher = 0x7f0d0000
com.ccppdfreader.app.debug:id/match_parent = 0x7f0800c7
com.ccppdfreader.app.debug:id/listMode = 0x7f0800c4
com.ccppdfreader.app.debug:id/left = 0x7f0800bf
com.ccppdfreader.app.debug:id/action_bar_subtitle = 0x7f080035
com.ccppdfreader.app.debug:id/jumpToEnd = 0x7f0800bc
com.ccppdfreader.app.debug:attr/imageRotate = 0x7f0300f9
com.ccppdfreader.app.debug:color/ripple_material_dark = 0x7f050085
com.ccppdfreader.app.debug:id/item_touch_helper_previous_elevation = 0x7f0800bb
com.ccppdfreader.app.debug:id/onInterceptTouchReturnSwipe = 0x7f0800dc
com.ccppdfreader.app.debug:attr/viewTransitionOnCross = 0x7f030225
com.ccppdfreader.app.debug:id/italic = 0x7f0800ba
com.ccppdfreader.app.debug:id/invisible = 0x7f0800b8
com.ccppdfreader.app.debug:id/included = 0x7f0800b6
com.ccppdfreader.app.debug:style/ThemeOverlay.AppCompat.Dark = 0x7f0f010f
com.ccppdfreader.app.debug:id/ignoreRequest = 0x7f0800b3
com.ccppdfreader.app.debug:id/unchecked = 0x7f08014a
com.ccppdfreader.app.debug:id/ignore = 0x7f0800b2
com.ccppdfreader.app.debug:styleable/MockView = 0x7f100034
com.ccppdfreader.app.debug:dimen/abc_text_size_caption_material = 0x7f060042
com.ccppdfreader.app.debug:id/icon = 0x7f0800af
com.ccppdfreader.app.debug:attr/waveOffset = 0x7f03022c
com.ccppdfreader.app.debug:id/horizontal_only = 0x7f0800ae
com.ccppdfreader.app.debug:id/hide_ime_id = 0x7f0800aa
com.ccppdfreader.app.debug:id/legacy = 0x7f0800c0
com.ccppdfreader.app.debug:id/graph_wrap = 0x7f0800a6
com.ccppdfreader.app.debug:attr/flow_maxElementsWrap = 0x7f0300d5
com.ccppdfreader.app.debug:id/gone = 0x7f0800a4
com.ccppdfreader.app.debug:attr/circularflow_viewCenter = 0x7f03006d
com.ccppdfreader.app.debug:id/forever = 0x7f0800a1
com.ccppdfreader.app.debug:id/flip = 0x7f0800a0
com.ccppdfreader.app.debug:id/expand_activities_button = 0x7f08009e
com.ccppdfreader.app.debug:dimen/splash_logo_size = 0x7f06009a
com.ccppdfreader.app.debug:id/emptyStateLayout = 0x7f08009c
com.ccppdfreader.app.debug:id/edit_text_id = 0x7f08009b
com.ccppdfreader.app.debug:attr/buttonBarStyle = 0x7f03004f
com.ccppdfreader.app.debug:id/edit_query = 0x7f08009a
com.ccppdfreader.app.debug:drawable/abc_vector_test = 0x7f07004e
com.ccppdfreader.app.debug:id/easeIn = 0x7f080096
com.ccppdfreader.app.debug:string/abc_menu_delete_shortcut_label = 0x7f0e000a
com.ccppdfreader.app.debug:id/dragUp = 0x7f080095
com.ccppdfreader.app.debug:id/dragStart = 0x7f080094
com.ccppdfreader.app.debug:id/pooling_container_listener_holder_tag = 0x7f0800e6
com.ccppdfreader.app.debug:id/dragRight = 0x7f080093
com.ccppdfreader.app.debug:style/Widget.AppCompat.ButtonBar = 0x7f0f0127
com.ccppdfreader.app.debug:id/dragClockwise = 0x7f08008f
com.ccppdfreader.app.debug:id/disableScroll = 0x7f08008d
com.ccppdfreader.app.debug:style/Theme.AppCompat.Light = 0x7f0f0102
com.ccppdfreader.app.debug:id/disableIntraAutoTransition = 0x7f08008b
com.ccppdfreader.app.debug:id/dependency_ordering = 0x7f080086
com.ccppdfreader.app.debug:attr/customColorDrawableValue = 0x7f030095
com.ccppdfreader.app.debug:id/deltaRelative = 0x7f080085
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0f00eb
com.ccppdfreader.app.debug:id/title = 0x7f08013f
com.ccppdfreader.app.debug:color/switch_thumb_normal_material_light = 0x7f050096
com.ccppdfreader.app.debug:id/default_activity_button = 0x7f080084
com.ccppdfreader.app.debug:id/vertical_only = 0x7f08014e
com.ccppdfreader.app.debug:id/decor_content_parent = 0x7f080083
com.ccppdfreader.app.debug:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f0f0062
com.ccppdfreader.app.debug:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.ccppdfreader.app.debug:id/decelerateAndComplete = 0x7f080082
com.ccppdfreader.app.debug:id/decelerate = 0x7f080081
com.ccppdfreader.app.debug:id/time = 0x7f08013e
com.ccppdfreader.app.debug:id/custom = 0x7f08007f
com.ccppdfreader.app.debug:id/currentState = 0x7f08007e
com.ccppdfreader.app.debug:id/continuousVelocity = 0x7f08007c
com.ccppdfreader.app.debug:id/content = 0x7f08007a
com.ccppdfreader.app.debug:layout/abc_list_menu_item_radio = 0x7f0b0011
com.ccppdfreader.app.debug:id/constraint = 0x7f080079
com.ccppdfreader.app.debug:id/collapseActionView = 0x7f080078
com.ccppdfreader.app.debug:id/closest = 0x7f080077
com.ccppdfreader.app.debug:color/splash_background = 0x7f05008d
com.ccppdfreader.app.debug:id/clockwise = 0x7f080076
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f0f0088
com.ccppdfreader.app.debug:id/center_vertical = 0x7f08006f
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f0f007a
com.ccppdfreader.app.debug:id/action_bar = 0x7f080030
com.ccppdfreader.app.debug:id/center = 0x7f08006e
com.ccppdfreader.app.debug:id/cache_measures = 0x7f08006b
com.ccppdfreader.app.debug:id/buttonPanel = 0x7f08006a
com.ccppdfreader.app.debug:id/bounceBoth = 0x7f080067
com.ccppdfreader.app.debug:id/bottom = 0x7f080065
com.ccppdfreader.app.debug:id/search_src_text = 0x7f080103
com.ccppdfreader.app.debug:attr/divider = 0x7f0300a8
com.ccppdfreader.app.debug:attr/thumbTintMode = 0x7f0301fe
com.ccppdfreader.app.debug:id/bestChoice = 0x7f080063
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f0f0096
com.ccppdfreader.app.debug:id/line3 = 0x7f0800c2
com.ccppdfreader.app.debug:id/beginning = 0x7f080062
com.ccppdfreader.app.debug:string/action_refresh = 0x7f0e001d
com.ccppdfreader.app.debug:layout/notification_template_part_chronometer = 0x7f0b002d
com.ccppdfreader.app.debug:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
com.ccppdfreader.app.debug:id/bounceStart = 0x7f080069
com.ccppdfreader.app.debug:color/md_theme_dark_errorContainer = 0x7f050044
com.ccppdfreader.app.debug:id/beginOnFirstDraw = 0x7f080061
com.ccppdfreader.app.debug:layout/notification_template_custom_big = 0x7f0b002b
com.ccppdfreader.app.debug:id/textViewFileName = 0x7f080139
com.ccppdfreader.app.debug:attr/circleRadius = 0x7f030068
com.ccppdfreader.app.debug:id/baseline = 0x7f080060
com.ccppdfreader.app.debug:id/autoComplete = 0x7f08005c
com.ccppdfreader.app.debug:drawable/ic_error = 0x7f070066
com.ccppdfreader.app.debug:id/auto = 0x7f08005b
com.ccppdfreader.app.debug:drawable/abc_dialog_material_background = 0x7f070013
com.ccppdfreader.app.debug:id/chains = 0x7f080072
com.ccppdfreader.app.debug:dimen/padding_extra_large = 0x7f06008c
com.ccppdfreader.app.debug:id/async = 0x7f08005a
com.ccppdfreader.app.debug:id/asConfigured = 0x7f080059
com.ccppdfreader.app.debug:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f060072
com.ccppdfreader.app.debug:id/fragment_container_view_tag = 0x7f0800a2
com.ccppdfreader.app.debug:id/antiClockwise = 0x7f080057
com.ccppdfreader.app.debug:id/aligned = 0x7f080052
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Button = 0x7f0f0072
com.ccppdfreader.app.debug:id/add = 0x7f080050
com.ccppdfreader.app.debug:id/linear = 0x7f0800c3
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Button = 0x7f0f000f
com.ccppdfreader.app.debug:id/SYM = 0x7f080009
com.ccppdfreader.app.debug:id/activity_chooser_view_content = 0x7f08004f
com.ccppdfreader.app.debug:id/actions = 0x7f08004e
com.ccppdfreader.app.debug:id/action_share = 0x7f08004b
com.ccppdfreader.app.debug:id/action_rotate = 0x7f080048
com.ccppdfreader.app.debug:style/Widget.AppCompat.ListPopupWindow = 0x7f0f0147
com.ccppdfreader.app.debug:layout/abc_alert_dialog_button_bar_material = 0x7f0b0008
com.ccppdfreader.app.debug:styleable/MotionLayout = 0x7f100039
com.ccppdfreader.app.debug:id/stop = 0x7f080120
com.ccppdfreader.app.debug:id/action_refresh = 0x7f080047
com.ccppdfreader.app.debug:layout/abc_cascading_menu_item_layout = 0x7f0b000b
com.ccppdfreader.app.debug:id/action_night_mode = 0x7f080046
com.ccppdfreader.app.debug:attr/actionMenuTextColor = 0x7f030011
com.ccppdfreader.app.debug:id/action_menu_presenter = 0x7f080042
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0f002a
com.ccppdfreader.app.debug:id/action_menu_divider = 0x7f080041
com.ccppdfreader.app.debug:id/action_image = 0x7f080040
com.ccppdfreader.app.debug:style/Base.V7.Theme.AppCompat = 0x7f0f005f
com.ccppdfreader.app.debug:attr/titleTextAppearance = 0x7f03020b
com.ccppdfreader.app.debug:id/action_home = 0x7f08003f
com.ccppdfreader.app.debug:style/Base.Animation.AppCompat.Dialog = 0x7f0f0007
com.ccppdfreader.app.debug:id/action_fit_width = 0x7f08003e
com.ccppdfreader.app.debug:color/md_theme_dark_background = 0x7f050042
com.ccppdfreader.app.debug:id/action_container = 0x7f08003a
com.ccppdfreader.app.debug:drawable/abc_ic_voice_search_api_material = 0x7f070021
com.ccppdfreader.app.debug:id/action_clear_all = 0x7f080039
com.ccppdfreader.app.debug:attr/height = 0x7f0300ec
com.ccppdfreader.app.debug:id/action_bookmarks = 0x7f080038
com.ccppdfreader.app.debug:id/action_bookmark = 0x7f080037
com.ccppdfreader.app.debug:id/action_bar_spinner = 0x7f080034
com.ccppdfreader.app.debug:dimen/drawer_width = 0x7f060062
com.ccppdfreader.app.debug:id/action_bar_root = 0x7f080033
com.ccppdfreader.app.debug:id/action_bar_container = 0x7f080032
com.ccppdfreader.app.debug:id/dragLeft = 0x7f080092
com.ccppdfreader.app.debug:attr/layout_editor_absoluteY = 0x7f030135
com.ccppdfreader.app.debug:id/actionDownUp = 0x7f08002d
com.ccppdfreader.app.debug:id/accessibility_custom_action_9 = 0x7f08002b
com.ccppdfreader.app.debug:id/accessibility_custom_action_6 = 0x7f080028
com.ccppdfreader.app.debug:id/accessibility_custom_action_28 = 0x7f080021
com.ccppdfreader.app.debug:id/accessibility_custom_action_27 = 0x7f080020
com.ccppdfreader.app.debug:style/Widget.AppCompat.RatingBar = 0x7f0f0150
com.ccppdfreader.app.debug:attr/fastScrollHorizontalThumbDrawable = 0x7f0300c4
com.ccppdfreader.app.debug:id/accessibility_custom_action_26 = 0x7f08001f
com.ccppdfreader.app.debug:attr/layout_constrainedHeight = 0x7f030106
com.ccppdfreader.app.debug:string/success = 0x7f0e0057
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f0f013f
com.ccppdfreader.app.debug:drawable/abc_btn_check_material_anim = 0x7f070004
com.ccppdfreader.app.debug:id/accessibility_custom_action_25 = 0x7f08001e
com.ccppdfreader.app.debug:id/direct = 0x7f080089
com.ccppdfreader.app.debug:id/accessibility_custom_action_24 = 0x7f08001d
com.ccppdfreader.app.debug:id/useLogo = 0x7f08014d
com.ccppdfreader.app.debug:id/accessibility_custom_action_23 = 0x7f08001c
com.ccppdfreader.app.debug:id/off = 0x7f0800da
com.ccppdfreader.app.debug:attr/pivotAnchor = 0x7f030192
com.ccppdfreader.app.debug:id/accessibility_custom_action_21 = 0x7f08001a
com.ccppdfreader.app.debug:id/view_tree_lifecycle_owner = 0x7f080150
com.ccppdfreader.app.debug:style/Theme.AppCompat.DayNight = 0x7f0f00f6
com.ccppdfreader.app.debug:id/search_badge = 0x7f0800fb
com.ccppdfreader.app.debug:id/grouping = 0x7f0800a8
com.ccppdfreader.app.debug:id/accessibility_custom_action_20 = 0x7f080019
com.ccppdfreader.app.debug:id/accessibility_custom_action_2 = 0x7f080018
com.ccppdfreader.app.debug:style/Platform.ThemeOverlay.AppCompat = 0x7f0f00a3
com.ccppdfreader.app.debug:id/accessibility_custom_action_19 = 0x7f080017
com.ccppdfreader.app.debug:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.ccppdfreader.app.debug:id/accessibility_custom_action_17 = 0x7f080015
com.ccppdfreader.app.debug:id/accessibility_custom_action_16 = 0x7f080014
com.ccppdfreader.app.debug:id/accessibility_custom_action_14 = 0x7f080012
com.ccppdfreader.app.debug:id/accessibility_custom_action_13 = 0x7f080011
com.ccppdfreader.app.debug:string/theme_light = 0x7f0e0059
com.ccppdfreader.app.debug:id/accessibility_custom_action_1 = 0x7f08000d
com.ccppdfreader.app.debug:id/accessibility_custom_action_0 = 0x7f08000c
com.ccppdfreader.app.debug:attr/buttonIconDimen = 0x7f030052
com.ccppdfreader.app.debug:string/abc_menu_enter_shortcut_label = 0x7f0e000b
com.ccppdfreader.app.debug:id/accelerate = 0x7f08000a
com.ccppdfreader.app.debug:id/SHOW_PROGRESS = 0x7f080008
com.ccppdfreader.app.debug:id/SHIFT = 0x7f080005
com.ccppdfreader.app.debug:drawable/abc_list_pressed_holo_dark = 0x7f070028
com.ccppdfreader.app.debug:id/NO_DEBUG = 0x7f080004
com.ccppdfreader.app.debug:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
com.ccppdfreader.app.debug:id/FUNCTION = 0x7f080002
com.ccppdfreader.app.debug:dimen/tooltip_y_offset_touch = 0x7f0600b0
com.ccppdfreader.app.debug:id/CTRL = 0x7f080001
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f0f0037
com.ccppdfreader.app.debug:color/abc_background_cache_hint_selector_material_light = 0x7f050001
com.ccppdfreader.app.debug:id/ALT = 0x7f080000
com.ccppdfreader.app.debug:dimen/abc_edit_text_inset_top_material = 0x7f06002e
com.ccppdfreader.app.debug:color/md_theme_light_surfaceVariant = 0x7f050073
com.ccppdfreader.app.debug:drawable/tooltip_frame_light = 0x7f07008d
com.ccppdfreader.app.debug:drawable/tooltip_frame_dark = 0x7f07008c
com.ccppdfreader.app.debug:attr/layout_constraintGuide_percent = 0x7f030117
com.ccppdfreader.app.debug:drawable/ic_menu = 0x7f07006f
com.ccppdfreader.app.debug:drawable/notification_template_icon_low_bg = 0x7f070088
com.ccppdfreader.app.debug:drawable/notification_icon_background = 0x7f070085
com.ccppdfreader.app.debug:attr/layout_goneMarginLeft = 0x7f030139
com.ccppdfreader.app.debug:id/sawtooth = 0x7f0800f6
com.ccppdfreader.app.debug:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
com.ccppdfreader.app.debug:dimen/notification_small_icon_background_padding = 0x7f060087
com.ccppdfreader.app.debug:drawable/notification_bg_low_normal = 0x7f070081
com.ccppdfreader.app.debug:drawable/notify_panel_notification_icon_bg = 0x7f07008a
com.ccppdfreader.app.debug:attr/constraintSet = 0x7f030081
com.ccppdfreader.app.debug:drawable/notification_bg_low = 0x7f070080
com.ccppdfreader.app.debug:color/abc_tint_edittext = 0x7f050015
com.ccppdfreader.app.debug:id/disablePostScroll = 0x7f08008c
com.ccppdfreader.app.debug:drawable/notification_bg = 0x7f07007f
com.ccppdfreader.app.debug:string/no_recent_files_desc = 0x7f0e0042
com.ccppdfreader.app.debug:drawable/notification_action_background = 0x7f07007e
com.ccppdfreader.app.debug:drawable/test_level_drawable = 0x7f07008b
com.ccppdfreader.app.debug:drawable/ic_zoom_out = 0x7f07007d
com.ccppdfreader.app.debug:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0f0124
com.ccppdfreader.app.debug:drawable/ic_zoom_in = 0x7f07007c
com.ccppdfreader.app.debug:drawable/abc_text_select_handle_left_mtrl = 0x7f070046
com.ccppdfreader.app.debug:drawable/ic_wifi_off = 0x7f07007b
com.ccppdfreader.app.debug:drawable/ic_share = 0x7f070078
com.ccppdfreader.app.debug:drawable/ic_security = 0x7f070076
com.ccppdfreader.app.debug:drawable/ic_search = 0x7f070075
com.ccppdfreader.app.debug:attr/flow_lastVerticalBias = 0x7f0300d3
com.ccppdfreader.app.debug:drawable/ic_pdf_document = 0x7f070072
com.ccppdfreader.app.debug:drawable/ic_night_mode = 0x7f070071
com.ccppdfreader.app.debug:drawable/ic_document_empty = 0x7f070065
com.ccppdfreader.app.debug:drawable/ic_info = 0x7f07006d
com.ccppdfreader.app.debug:drawable/ic_home = 0x7f07006c
com.ccppdfreader.app.debug:drawable/ic_folder_open = 0x7f07006a
com.ccppdfreader.app.debug:id/wrap_content_constrained = 0x7f08015a
com.ccppdfreader.app.debug:drawable/ic_folder_empty = 0x7f070069
com.ccppdfreader.app.debug:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f0f00f7
com.ccppdfreader.app.debug:id/icon_group = 0x7f0800b0
com.ccppdfreader.app.debug:attr/paddingTopNoTitle = 0x7f030187
com.ccppdfreader.app.debug:drawable/ic_favorite = 0x7f070067
com.ccppdfreader.app.debug:id/image = 0x7f0800b4
com.ccppdfreader.app.debug:dimen/text_size_label_large = 0x7f0600a2
com.ccppdfreader.app.debug:drawable/ic_close = 0x7f070063
com.ccppdfreader.app.debug:id/accessibility_custom_action_15 = 0x7f080013
com.ccppdfreader.app.debug:id/postLayout = 0x7f0800e8
com.ccppdfreader.app.debug:drawable/ic_chevron_right = 0x7f070061
com.ccppdfreader.app.debug:drawable/ic_chevron_left = 0x7f070060
com.ccppdfreader.app.debug:drawable/ic_call_decline_low = 0x7f07005e
com.ccppdfreader.app.debug:id/accessibility_action_clickable_span = 0x7f08000b
com.ccppdfreader.app.debug:drawable/ic_call_decline = 0x7f07005d
com.ccppdfreader.app.debug:drawable/ic_call_answer_low = 0x7f07005a
com.ccppdfreader.app.debug:drawable/ic_call_answer = 0x7f070059
com.ccppdfreader.app.debug:styleable/StateSet = 0x7f100048
com.ccppdfreader.app.debug:drawable/ic_add = 0x7f070057
com.ccppdfreader.app.debug:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f070056
com.ccppdfreader.app.debug:drawable/btn_radio_on_mtrl = 0x7f070055
com.ccppdfreader.app.debug:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f070054
com.ccppdfreader.app.debug:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f070052
com.ccppdfreader.app.debug:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f070050
com.ccppdfreader.app.debug:attr/autoCompleteTextViewStyle = 0x7f030036
com.ccppdfreader.app.debug:drawable/btn_checkbox_checked_mtrl = 0x7f07004f
com.ccppdfreader.app.debug:drawable/abc_textfield_activated_mtrl_alpha = 0x7f070049
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f0f0145
com.ccppdfreader.app.debug:attr/showDividers = 0x7f0301bd
com.ccppdfreader.app.debug:drawable/abc_text_select_handle_right_mtrl = 0x7f070048
com.ccppdfreader.app.debug:color/abc_hint_foreground_material_dark = 0x7f050007
com.ccppdfreader.app.debug:drawable/abc_text_cursor_material = 0x7f070045
com.ccppdfreader.app.debug:drawable/abc_tab_indicator_material = 0x7f070043
com.ccppdfreader.app.debug:drawable/abc_star_half_black_48dp = 0x7f070040
com.ccppdfreader.app.debug:string/no_files_found = 0x7f0e0040
com.ccppdfreader.app.debug:attr/contentInsetStartWithNavigation = 0x7f03008e
com.ccppdfreader.app.debug:drawable/abc_spinner_textfield_background_material = 0x7f07003e
com.ccppdfreader.app.debug:drawable/abc_spinner_mtrl_am_alpha = 0x7f07003d
com.ccppdfreader.app.debug:id/src_over = 0x7f080119
com.ccppdfreader.app.debug:drawable/abc_seekbar_track_material = 0x7f07003c
com.ccppdfreader.app.debug:attr/crossfade = 0x7f030091
com.ccppdfreader.app.debug:drawable/abc_scrubber_track_mtrl_alpha = 0x7f070039
com.ccppdfreader.app.debug:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f070038
com.ccppdfreader.app.debug:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f070037
com.ccppdfreader.app.debug:id/supportScrollUp = 0x7f080123
com.ccppdfreader.app.debug:attr/rotationCenterId = 0x7f0301af
com.ccppdfreader.app.debug:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f070036
com.ccppdfreader.app.debug:styleable/ConstraintLayout_placeholder = 0x7f100019
com.ccppdfreader.app.debug:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
com.ccppdfreader.app.debug:string/abc_activitychooserview_choose_application = 0x7f0e0005
com.ccppdfreader.app.debug:attr/tickMarkTintMode = 0x7f030201
com.ccppdfreader.app.debug:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f070035
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Inverse = 0x7f0f00c6
com.ccppdfreader.app.debug:dimen/list_item_height = 0x7f060075
com.ccppdfreader.app.debug:drawable/notification_template_icon_bg = 0x7f070087
com.ccppdfreader.app.debug:attr/reverseLayout = 0x7f0301ae
com.ccppdfreader.app.debug:drawable/abc_ratingbar_material = 0x7f070033
com.ccppdfreader.app.debug:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f0f0107
com.ccppdfreader.app.debug:string/app_name = 0x7f0e0024
com.ccppdfreader.app.debug:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070030
com.ccppdfreader.app.debug:drawable/abc_list_selector_holo_dark = 0x7f07002e
com.ccppdfreader.app.debug:style/Widget.AppCompat.ActionBar.TabView = 0x7f0f011a
com.ccppdfreader.app.debug:dimen/padding_large = 0x7f06008d
com.ccppdfreader.app.debug:dimen/abc_dialog_corner_radius_material = 0x7f06001b
com.ccppdfreader.app.debug:dimen/abc_search_view_preferred_height = 0x7f060036
com.ccppdfreader.app.debug:drawable/abc_list_selector_disabled_holo_dark = 0x7f07002c
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f0f006d
com.ccppdfreader.app.debug:attr/framePosition = 0x7f0300e8
com.ccppdfreader.app.debug:drawable/abc_list_selector_background_transition_holo_light = 0x7f07002b
com.ccppdfreader.app.debug:attr/layout_constraintHeight = 0x7f030118
com.ccppdfreader.app.debug:drawable/abc_list_selector_background_transition_holo_dark = 0x7f07002a
com.ccppdfreader.app.debug:styleable/ColorStateListItem = 0x7f100014
com.ccppdfreader.app.debug:id/nav_recent = 0x7f0800ce
com.ccppdfreader.app.debug:attr/listItemLayout = 0x7f030146
com.ccppdfreader.app.debug:drawable/abc_list_pressed_holo_light = 0x7f070029
com.ccppdfreader.app.debug:id/action_settings = 0x7f08004a
com.ccppdfreader.app.debug:drawable/abc_list_longpressed_holo = 0x7f070027
com.ccppdfreader.app.debug:string/abc_capital_on = 0x7f0e0007
com.ccppdfreader.app.debug:drawable/abc_list_focused_holo = 0x7f070026
com.ccppdfreader.app.debug:drawable/abc_list_divider_mtrl_alpha = 0x7f070025
com.ccppdfreader.app.debug:attr/motionDebug = 0x7f030163
com.ccppdfreader.app.debug:drawable/abc_item_background_holo_light = 0x7f070023
com.ccppdfreader.app.debug:attr/borderlessButtonStyle = 0x7f030049
com.ccppdfreader.app.debug:drawable/abc_ic_search_api_material = 0x7f070020
com.ccppdfreader.app.debug:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f07001f
com.ccppdfreader.app.debug:attr/barLength = 0x7f030042
com.ccppdfreader.app.debug:id/accessibility_custom_action_10 = 0x7f08000e
com.ccppdfreader.app.debug:attr/actionBarTabTextStyle = 0x7f03000a
com.ccppdfreader.app.debug:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f07001d
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f0f00ca
com.ccppdfreader.app.debug:drawable/abc_ic_clear_material = 0x7f070017
com.ccppdfreader.app.debug:drawable/notification_bg_normal_pressed = 0x7f070084
com.ccppdfreader.app.debug:id/action_sort = 0x7f08004c
com.ccppdfreader.app.debug:color/button_material_dark = 0x7f050028
com.ccppdfreader.app.debug:color/call_notification_decline_color = 0x7f05002b
com.ccppdfreader.app.debug:drawable/abc_control_background_material = 0x7f070012
com.ccppdfreader.app.debug:style/Widget.AppCompat.Button = 0x7f0f0121
com.ccppdfreader.app.debug:attr/switchPadding = 0x7f0301d9
com.ccppdfreader.app.debug:id/disableHome = 0x7f08008a
com.ccppdfreader.app.debug:drawable/btn_checkbox_unchecked_mtrl = 0x7f070051
com.ccppdfreader.app.debug:drawable/abc_cab_background_top_material = 0x7f070010
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f0f007f
com.ccppdfreader.app.debug:drawable/abc_cab_background_internal_bg = 0x7f07000f
com.ccppdfreader.app.debug:styleable/ActionMode = 0x7f100004
com.ccppdfreader.app.debug:attr/limitBoundsTo = 0x7f030140
com.ccppdfreader.app.debug:string/file_corrupted = 0x7f0e0035
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f0f00d5
com.ccppdfreader.app.debug:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f07000e
com.ccppdfreader.app.debug:drawable/abc_textfield_search_material = 0x7f07004d
com.ccppdfreader.app.debug:styleable/KeyFramesVelocity = 0x7f100029
com.ccppdfreader.app.debug:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f07000d
com.ccppdfreader.app.debug:anim/slide_out_left = 0x7f01001d
com.ccppdfreader.app.debug:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f07000b
com.ccppdfreader.app.debug:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f0f011c
com.ccppdfreader.app.debug:drawable/abc_btn_radio_material_anim = 0x7f07000a
com.ccppdfreader.app.debug:drawable/abc_btn_radio_material = 0x7f070009
com.ccppdfreader.app.debug:drawable/abc_btn_default_mtrl_shape = 0x7f070008
com.ccppdfreader.app.debug:anim/abc_popup_exit = 0x7f010004
com.ccppdfreader.app.debug:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f070006
com.ccppdfreader.app.debug:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f070005
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f0f001e
com.ccppdfreader.app.debug:id/bounceEnd = 0x7f080068
com.ccppdfreader.app.debug:drawable/abc_action_bar_item_background_material = 0x7f070001
com.ccppdfreader.app.debug:string/abc_action_menu_overflow_description = 0x7f0e0002
com.ccppdfreader.app.debug:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070000
com.ccppdfreader.app.debug:dimen/tooltip_y_offset_non_touch = 0x7f0600af
com.ccppdfreader.app.debug:id/cos = 0x7f08007d
com.ccppdfreader.app.debug:dimen/tooltip_precise_anchor_threshold = 0x7f0600ad
com.ccppdfreader.app.debug:attr/alpha = 0x7f03002b
com.ccppdfreader.app.debug:dimen/tooltip_horizontal_padding = 0x7f0600aa
com.ccppdfreader.app.debug:dimen/tooltip_corner_radius = 0x7f0600a9
com.ccppdfreader.app.debug:dimen/toolbar_elevation = 0x7f0600a8
com.ccppdfreader.app.debug:dimen/text_size_title_medium = 0x7f0600a6
com.ccppdfreader.app.debug:dimen/text_size_label_medium = 0x7f0600a3
com.ccppdfreader.app.debug:layout/abc_popup_menu_item_layout = 0x7f0b0013
com.ccppdfreader.app.debug:id/tag_accessibility_heading = 0x7f080127
com.ccppdfreader.app.debug:anim/abc_fade_in = 0x7f010000
com.ccppdfreader.app.debug:dimen/text_size_headline_large = 0x7f06009f
com.ccppdfreader.app.debug:attr/ratingBarStyle = 0x7f0301a2
com.ccppdfreader.app.debug:dimen/text_size_body_large = 0x7f06009c
com.ccppdfreader.app.debug:id/easeInOut = 0x7f080097
com.ccppdfreader.app.debug:dimen/spacing_xxl = 0x7f060099
com.ccppdfreader.app.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f0f00ae
com.ccppdfreader.app.debug:style/Base.V28.Theme.AppCompat = 0x7f0f005d
com.ccppdfreader.app.debug:attr/layout_editor_absoluteX = 0x7f030134
com.ccppdfreader.app.debug:dimen/spacing_xs = 0x7f060098
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f0f001c
com.ccppdfreader.app.debug:drawable/ic_delete = 0x7f070064
com.ccppdfreader.app.debug:dimen/spacing_xl = 0x7f060097
com.ccppdfreader.app.debug:attr/textAppearanceListItemSecondary = 0x7f0301e3
com.ccppdfreader.app.debug:attr/layout_constraintHeight_max = 0x7f03011a
com.ccppdfreader.app.debug:id/sharedValueSet = 0x7f080106
com.ccppdfreader.app.debug:id/multiply = 0x7f0800cb
com.ccppdfreader.app.debug:dimen/spacing_sm = 0x7f060096
com.ccppdfreader.app.debug:id/always = 0x7f080054
com.ccppdfreader.app.debug:dimen/search_bar_height = 0x7f060093
com.ccppdfreader.app.debug:style/Widget.AppCompat.Spinner.Underlined = 0x7f0f015a
com.ccppdfreader.app.debug:string/settings_appearance = 0x7f0e0052
com.ccppdfreader.app.debug:dimen/pdf_bottom_bar_height = 0x7f060090
com.ccppdfreader.app.debug:attr/textFillColor = 0x7f0301f0
com.ccppdfreader.app.debug:dimen/padding_small = 0x7f06008f
com.ccppdfreader.app.debug:id/action_mode_bar_stub = 0x7f080044
com.ccppdfreader.app.debug:dimen/notification_media_narrow_margin = 0x7f060084
com.ccppdfreader.app.debug:dimen/notification_content_margin_start = 0x7f060080
com.ccppdfreader.app.debug:drawable/ic_chevron_up = 0x7f070062
com.ccppdfreader.app.debug:dimen/notification_big_circle_margin = 0x7f06007f
com.ccppdfreader.app.debug:id/SHOW_ALL = 0x7f080006
com.ccppdfreader.app.debug:dimen/margin_extra_large = 0x7f060079
com.ccppdfreader.app.debug:id/sharedValueUnset = 0x7f080107
com.ccppdfreader.app.debug:attr/windowFixedHeightMinor = 0x7f030235
com.ccppdfreader.app.debug:dimen/list_item_padding_vertical = 0x7f060078
com.ccppdfreader.app.debug:id/action_about = 0x7f08002f
com.ccppdfreader.app.debug:dimen/list_item_padding_horizontal = 0x7f060077
com.ccppdfreader.app.debug:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f060073
com.ccppdfreader.app.debug:dimen/icon_size_small = 0x7f060071
com.ccppdfreader.app.debug:dimen/icon_size_extra_large = 0x7f06006e
com.ccppdfreader.app.debug:id/tag_accessibility_actions = 0x7f080125
com.ccppdfreader.app.debug:attr/layout_constraintTag = 0x7f030128
com.ccppdfreader.app.debug:dimen/hint_pressed_alpha_material_light = 0x7f06006d
com.ccppdfreader.app.debug:menu/bottom_navigation_menu = 0x7f0c0001
com.ccppdfreader.app.debug:dimen/hint_pressed_alpha_material_dark = 0x7f06006c
com.ccppdfreader.app.debug:dimen/hint_alpha_material_dark = 0x7f06006a
com.ccppdfreader.app.debug:color/md_theme_light_tertiaryContainer = 0x7f050075
com.ccppdfreader.app.debug:dimen/highlight_alpha_material_dark = 0x7f060068
com.ccppdfreader.app.debug:styleable/AppCompatEmojiHelper = 0x7f10000a
com.ccppdfreader.app.debug:dimen/highlight_alpha_material_colored = 0x7f060067
com.ccppdfreader.app.debug:id/titleDividerNoCustom = 0x7f080140
com.ccppdfreader.app.debug:id/chain = 0x7f080070
com.ccppdfreader.app.debug:dimen/fastscroll_minimum_range = 0x7f060066
com.ccppdfreader.app.debug:string/share_pdf = 0x7f0e0055
com.ccppdfreader.app.debug:dimen/fastscroll_margin = 0x7f060065
com.ccppdfreader.app.debug:dimen/fastscroll_default_thickness = 0x7f060064
com.ccppdfreader.app.debug:dimen/disabled_alpha_material_light = 0x7f060061
com.ccppdfreader.app.debug:dimen/disabled_alpha_material_dark = 0x7f060060
com.ccppdfreader.app.debug:dimen/compat_notification_large_icon_max_width = 0x7f06005d
com.ccppdfreader.app.debug:dimen/compat_button_padding_vertical_material = 0x7f06005a
com.ccppdfreader.app.debug:styleable/ConstraintSet = 0x7f10001b
com.ccppdfreader.app.debug:dimen/compat_button_padding_horizontal_material = 0x7f060059
com.ccppdfreader.app.debug:color/md_theme_dark_primaryContainer = 0x7f050055
com.ccppdfreader.app.debug:id/normal = 0x7f0800d5
com.ccppdfreader.app.debug:style/Widget.AppCompat.ProgressBar = 0x7f0f014e
com.ccppdfreader.app.debug:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f07000c
com.ccppdfreader.app.debug:dimen/compat_button_inset_vertical_material = 0x7f060058
com.ccppdfreader.app.debug:color/search_highlight_current = 0x7f050088
com.ccppdfreader.app.debug:dimen/card_corner_radius = 0x7f060055
com.ccppdfreader.app.debug:dimen/button_height = 0x7f060054
com.ccppdfreader.app.debug:dimen/bottom_nav_height = 0x7f060052
com.ccppdfreader.app.debug:dimen/app_bar_height = 0x7f060051
com.ccppdfreader.app.debug:drawable/abc_textfield_default_mtrl_alpha = 0x7f07004a
com.ccppdfreader.app.debug:dimen/abc_text_size_title_material = 0x7f06004f
com.ccppdfreader.app.debug:attr/windowActionModeOverlay = 0x7f030233
com.ccppdfreader.app.debug:dimen/abc_text_size_subhead_material = 0x7f06004d
com.ccppdfreader.app.debug:dimen/abc_text_size_small_material = 0x7f06004c
com.ccppdfreader.app.debug:dimen/abc_text_size_medium_material = 0x7f060049
com.ccppdfreader.app.debug:style/Animation.AppCompat.Tooltip = 0x7f0f0004
com.ccppdfreader.app.debug:dimen/abc_text_size_headline_material = 0x7f060047
com.ccppdfreader.app.debug:string/abc_searchview_description_query = 0x7f0e0014
com.ccppdfreader.app.debug:dimen/container_padding = 0x7f06005e
com.ccppdfreader.app.debug:dimen/abc_text_size_display_3_material = 0x7f060045
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f0f0071
com.ccppdfreader.app.debug:id/recyclerViewFiles = 0x7f0800f0
com.ccppdfreader.app.debug:dimen/abc_text_size_body_1_material = 0x7f06003f
com.ccppdfreader.app.debug:dimen/abc_switch_padding = 0x7f06003e
com.ccppdfreader.app.debug:dimen/abc_star_small = 0x7f06003d
com.ccppdfreader.app.debug:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
com.ccppdfreader.app.debug:attr/flow_horizontalStyle = 0x7f0300d0
com.ccppdfreader.app.debug:id/actionDown = 0x7f08002c
com.ccppdfreader.app.debug:dimen/abc_star_big = 0x7f06003b
com.ccppdfreader.app.debug:dimen/text_size_title_large = 0x7f0600a5
com.ccppdfreader.app.debug:style/Widget.AppCompat.Button.Small = 0x7f0f0126
com.ccppdfreader.app.debug:dimen/abc_panel_menu_list_width = 0x7f060034
com.ccppdfreader.app.debug:dimen/abc_list_item_height_small_material = 0x7f060032
com.ccppdfreader.app.debug:styleable/StateListDrawableItem = 0x7f100047
com.ccppdfreader.app.debug:layout/abc_select_dialog_material = 0x7f0b001a
com.ccppdfreader.app.debug:id/home = 0x7f0800ab
com.ccppdfreader.app.debug:attr/drawableTopCompat = 0x7f0300b8
com.ccppdfreader.app.debug:dimen/abc_list_item_height_material = 0x7f060031
com.ccppdfreader.app.debug:dimen/abc_floating_window_z = 0x7f06002f
com.ccppdfreader.app.debug:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
com.ccppdfreader.app.debug:id/action_bar_activity_content = 0x7f080031
com.ccppdfreader.app.debug:id/start = 0x7f08011b
com.ccppdfreader.app.debug:color/abc_search_url_text_selected = 0x7f050010
com.ccppdfreader.app.debug:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
com.ccppdfreader.app.debug:string/abc_capital_off = 0x7f0e0006
com.ccppdfreader.app.debug:drawable/notification_tile_bg = 0x7f070089
com.ccppdfreader.app.debug:dimen/abc_disabled_alpha_material_light = 0x7f060028
com.ccppdfreader.app.debug:dimen/abc_disabled_alpha_material_dark = 0x7f060027
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Headline = 0x7f0f0015
com.ccppdfreader.app.debug:id/accessibility_custom_action_3 = 0x7f080023
com.ccppdfreader.app.debug:dimen/compat_button_inset_horizontal_material = 0x7f060057
com.ccppdfreader.app.debug:dimen/abc_dialog_padding_top_material = 0x7f060025
com.ccppdfreader.app.debug:dimen/abc_dialog_padding_material = 0x7f060024
com.ccppdfreader.app.debug:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f0f00b2
com.ccppdfreader.app.debug:string/no = 0x7f0e003e
com.ccppdfreader.app.debug:styleable/AppCompatImageView = 0x7f10000b
com.ccppdfreader.app.debug:dimen/abc_dialog_min_width_minor = 0x7f060023
com.ccppdfreader.app.debug:dimen/abc_dialog_min_width_major = 0x7f060022
com.ccppdfreader.app.debug:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
com.ccppdfreader.app.debug:id/action_mode_bar = 0x7f080043
com.ccppdfreader.app.debug:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Button.Borderless = 0x7f0f0073
com.ccppdfreader.app.debug:dimen/abc_control_inset_material = 0x7f060019
com.ccppdfreader.app.debug:dimen/abc_control_padding_material = 0x7f06001a
com.ccppdfreader.app.debug:attr/defaultState = 0x7f0300a0
com.ccppdfreader.app.debug:dimen/abc_button_inset_vertical_material = 0x7f060013
com.ccppdfreader.app.debug:dimen/abc_button_padding_horizontal_material = 0x7f060014
com.ccppdfreader.app.debug:dimen/abc_progress_bar_height_material = 0x7f060035
com.ccppdfreader.app.debug:attr/maxHeight = 0x7f030155
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f0f006e
com.ccppdfreader.app.debug:attr/flow_verticalAlign = 0x7f0300d7
com.ccppdfreader.app.debug:dimen/abc_button_inset_horizontal_material = 0x7f060012
com.ccppdfreader.app.debug:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0f004d
com.ccppdfreader.app.debug:id/honorRequest = 0x7f0800ad
com.ccppdfreader.app.debug:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
com.ccppdfreader.app.debug:color/md_theme_dark_tertiaryContainer = 0x7f05005b
com.ccppdfreader.app.debug:dimen/abc_action_button_min_height_material = 0x7f06000d
com.ccppdfreader.app.debug:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f0f014c
com.ccppdfreader.app.debug:drawable/notification_bg_low_pressed = 0x7f070082
com.ccppdfreader.app.debug:attr/layout_goneMarginBottom = 0x7f030137
com.ccppdfreader.app.debug:color/md_theme_light_background = 0x7f05005c
com.ccppdfreader.app.debug:attr/reactiveGuide_valueId = 0x7f0301a8
com.ccppdfreader.app.debug:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
com.ccppdfreader.app.debug:string/remove_from_favorites = 0x7f0e004c
com.ccppdfreader.app.debug:attr/homeAsUpIndicator = 0x7f0300ee
com.ccppdfreader.app.debug:attr/textAllCaps = 0x7f0301e0
com.ccppdfreader.app.debug:styleable/KeyFramesAcceleration = 0x7f100028
com.ccppdfreader.app.debug:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0f0110
com.ccppdfreader.app.debug:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
com.ccppdfreader.app.debug:dimen/abc_action_bar_stacked_max_height = 0x7f060009
com.ccppdfreader.app.debug:attr/layout = 0x7f030102
com.ccppdfreader.app.debug:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
com.ccppdfreader.app.debug:attr/region_widthLessThan = 0x7f0301ac
com.ccppdfreader.app.debug:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
com.ccppdfreader.app.debug:color/bright_foreground_disabled_material_dark = 0x7f050022
com.ccppdfreader.app.debug:dimen/abc_action_bar_content_inset_material = 0x7f060000
com.ccppdfreader.app.debug:attr/emojiCompatEnabled = 0x7f0300c1
com.ccppdfreader.app.debug:attr/subtitleTextStyle = 0x7f0301d6
com.ccppdfreader.app.debug:color/teal_700 = 0x7f050098
com.ccppdfreader.app.debug:attr/dragDirection = 0x7f0300ac
com.ccppdfreader.app.debug:dimen/abc_text_size_menu_material = 0x7f06004b
com.ccppdfreader.app.debug:color/teal_200 = 0x7f050097
com.ccppdfreader.app.debug:color/abc_tint_seek_thumb = 0x7f050016
com.ccppdfreader.app.debug:color/primary_text_disabled_material_light = 0x7f050081
com.ccppdfreader.app.debug:color/switch_thumb_material_light = 0x7f050094
com.ccppdfreader.app.debug:attr/measureWithLargestChild = 0x7f030158
com.ccppdfreader.app.debug:color/status_bar_light = 0x7f050090
com.ccppdfreader.app.debug:integer/cancel_button_image_alpha = 0x7f090002
com.ccppdfreader.app.debug:color/secondary_text_default_material_light = 0x7f05008a
com.ccppdfreader.app.debug:color/md_theme_light_onPrimary = 0x7f050065
com.ccppdfreader.app.debug:color/secondary_text_default_material_dark = 0x7f050089
com.ccppdfreader.app.debug:dimen/hint_alpha_material_light = 0x7f06006b
com.ccppdfreader.app.debug:color/search_highlight = 0x7f050087
com.ccppdfreader.app.debug:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
com.ccppdfreader.app.debug:attr/fontProviderSystemFontFamily = 0x7f0300e4
com.ccppdfreader.app.debug:attr/layout_constraintVertical_chainStyle = 0x7f03012d
com.ccppdfreader.app.debug:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
com.ccppdfreader.app.debug:color/purple_500 = 0x7f050083
com.ccppdfreader.app.debug:attr/attributeName = 0x7f030034
com.ccppdfreader.app.debug:layout/abc_action_menu_item_layout = 0x7f0b0002
com.ccppdfreader.app.debug:color/purple_200 = 0x7f050082
com.ccppdfreader.app.debug:color/primary_text_default_material_dark = 0x7f05007e
com.ccppdfreader.app.debug:styleable/State = 0x7f100045
com.ccppdfreader.app.debug:color/primary_dark_material_light = 0x7f05007b
com.ccppdfreader.app.debug:color/primary_dark_material_dark = 0x7f05007a
com.ccppdfreader.app.debug:drawable/abc_switch_track_mtrl_alpha = 0x7f070042
com.ccppdfreader.app.debug:attr/fontWeight = 0x7f0300e7
com.ccppdfreader.app.debug:color/pdf_background_dark = 0x7f050079
com.ccppdfreader.app.debug:dimen/abc_search_view_preferred_width = 0x7f060037
com.ccppdfreader.app.debug:color/md_theme_light_secondary = 0x7f050070
com.ccppdfreader.app.debug:id/nav_home = 0x7f0800cd
com.ccppdfreader.app.debug:styleable/Variant = 0x7f10004f
com.ccppdfreader.app.debug:color/md_theme_light_primary = 0x7f05006e
com.ccppdfreader.app.debug:attr/colorControlNormal = 0x7f03007a
com.ccppdfreader.app.debug:attr/mock_labelColor = 0x7f030160
com.ccppdfreader.app.debug:color/md_theme_dark_onSecondary = 0x7f05004d
com.ccppdfreader.app.debug:id/actionUp = 0x7f08002e
com.ccppdfreader.app.debug:attr/searchIcon = 0x7f0301b5
com.ccppdfreader.app.debug:color/md_theme_light_onSurface = 0x7f050069
com.ccppdfreader.app.debug:drawable/abc_ratingbar_small_material = 0x7f070034
com.ccppdfreader.app.debug:color/md_theme_light_onSecondary = 0x7f050067
com.ccppdfreader.app.debug:style/Base.DialogWindowTitle.AppCompat = 0x7f0f000a
com.ccppdfreader.app.debug:id/select_dialog_listview = 0x7f080105
com.ccppdfreader.app.debug:styleable/ViewBackgroundHelper = 0x7f100051
com.ccppdfreader.app.debug:color/md_theme_light_onBackground = 0x7f050062
com.ccppdfreader.app.debug:id/square = 0x7f080116
com.ccppdfreader.app.debug:attr/layout_constraintHorizontal_bias = 0x7f03011d
com.ccppdfreader.app.debug:id/textViewPdfPlaceholder = 0x7f08013c
com.ccppdfreader.app.debug:attr/buttonBarNegativeButtonStyle = 0x7f03004c
com.ccppdfreader.app.debug:color/md_theme_light_errorContainer = 0x7f05005e
com.ccppdfreader.app.debug:attr/drawableBottomCompat = 0x7f0300b0
com.ccppdfreader.app.debug:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
com.ccppdfreader.app.debug:color/md_theme_light_surface = 0x7f050072
com.ccppdfreader.app.debug:id/group_divider = 0x7f0800a7
com.ccppdfreader.app.debug:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f0f000b
com.ccppdfreader.app.debug:attr/actionModeWebSearchDrawable = 0x7f030020
com.ccppdfreader.app.debug:attr/subMenuArrow = 0x7f0301d1
com.ccppdfreader.app.debug:color/md_theme_dark_primary = 0x7f050054
com.ccppdfreader.app.debug:attr/mock_showDiagonals = 0x7f030161
com.ccppdfreader.app.debug:attr/arrowHeadLength = 0x7f030032
com.ccppdfreader.app.debug:attr/queryHint = 0x7f03019f
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Menu = 0x7f0f001d
com.ccppdfreader.app.debug:color/md_theme_dark_outline = 0x7f050053
com.ccppdfreader.app.debug:color/md_theme_dark_onTertiaryContainer = 0x7f050052
com.ccppdfreader.app.debug:dimen/notification_main_column_padding_top = 0x7f060083
com.ccppdfreader.app.debug:dimen/compat_control_corner_material = 0x7f06005b
com.ccppdfreader.app.debug:styleable/TextAppearance = 0x7f10004a
com.ccppdfreader.app.debug:attr/menu = 0x7f030159
com.ccppdfreader.app.debug:color/md_theme_dark_onPrimaryContainer = 0x7f05004c
com.ccppdfreader.app.debug:string/action_close = 0x7f0e001c
com.ccppdfreader.app.debug:color/md_theme_dark_onBackground = 0x7f050048
com.ccppdfreader.app.debug:id/right_icon = 0x7f0800f4
com.ccppdfreader.app.debug:color/md_theme_dark_onSurfaceVariant = 0x7f050050
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.PopupMenu = 0x7f0f008f
com.ccppdfreader.app.debug:attr/SharedValue = 0x7f030000
com.ccppdfreader.app.debug:attr/listDividerAlertDialog = 0x7f030145
com.ccppdfreader.app.debug:drawable/abc_btn_check_material = 0x7f070003
com.ccppdfreader.app.debug:color/material_grey_900 = 0x7f050041
com.ccppdfreader.app.debug:dimen/abc_text_size_large_material = 0x7f060048
com.ccppdfreader.app.debug:attr/layout_goneMarginRight = 0x7f03013a
com.ccppdfreader.app.debug:color/material_grey_850 = 0x7f050040
com.ccppdfreader.app.debug:id/text2 = 0x7f080133
com.ccppdfreader.app.debug:attr/onCross = 0x7f03017b
com.ccppdfreader.app.debug:color/material_grey_50 = 0x7f05003d
com.ccppdfreader.app.debug:layout/abc_activity_chooser_view_list_item = 0x7f0b0007
com.ccppdfreader.app.debug:attr/layout_constraintHeight_default = 0x7f030119
com.ccppdfreader.app.debug:color/material_grey_100 = 0x7f05003b
com.ccppdfreader.app.debug:color/material_deep_teal_500 = 0x7f05003a
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0f00d0
com.ccppdfreader.app.debug:color/material_deep_teal_200 = 0x7f050039
com.ccppdfreader.app.debug:layout/abc_action_bar_up_container = 0x7f0b0001
com.ccppdfreader.app.debug:style/Theme.AppCompat.Light.DarkActionBar = 0x7f0f0103
com.ccppdfreader.app.debug:color/highlighted_text_material_light = 0x7f050035
com.ccppdfreader.app.debug:id/checked = 0x7f080074
com.ccppdfreader.app.debug:attr/layoutDuringTransition = 0x7f030104
com.ccppdfreader.app.debug:attr/flow_lastVerticalStyle = 0x7f0300d4
com.ccppdfreader.app.debug:color/foreground_material_dark = 0x7f050032
com.ccppdfreader.app.debug:id/visible_removing_fragment_view_tag = 0x7f080155
com.ccppdfreader.app.debug:id/alertTitle = 0x7f080051
com.ccppdfreader.app.debug:style/Base.ThemeOverlay.AppCompat = 0x7f0f004a
com.ccppdfreader.app.debug:color/error_color_material_dark = 0x7f050030
com.ccppdfreader.app.debug:color/dim_foreground_material_dark = 0x7f05002e
com.ccppdfreader.app.debug:attr/layout_constraintBaseline_creator = 0x7f030108
com.ccppdfreader.app.debug:color/md_theme_light_outline = 0x7f05006d
com.ccppdfreader.app.debug:color/dim_foreground_disabled_material_dark = 0x7f05002c
com.ccppdfreader.app.debug:attr/flow_firstVerticalBias = 0x7f0300cb
com.ccppdfreader.app.debug:id/dragEnd = 0x7f080091
com.ccppdfreader.app.debug:dimen/margin_large = 0x7f06007a
com.ccppdfreader.app.debug:attr/alertDialogTheme = 0x7f030029
com.ccppdfreader.app.debug:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f07001e
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Display2 = 0x7f0f0012
com.ccppdfreader.app.debug:attr/actionMenuTextAppearance = 0x7f030010
com.ccppdfreader.app.debug:anim/abc_tooltip_exit = 0x7f01000b
com.ccppdfreader.app.debug:color/bright_foreground_inverse_material_dark = 0x7f050024
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.SearchView = 0x7f0f0097
com.ccppdfreader.app.debug:dimen/abc_action_bar_elevation_material = 0x7f060005
com.ccppdfreader.app.debug:anim/abc_fade_out = 0x7f010001
com.ccppdfreader.app.debug:color/background_floating_material_light = 0x7f05001e
com.ccppdfreader.app.debug:attr/contentInsetEnd = 0x7f030089
com.ccppdfreader.app.debug:color/switch_thumb_material_dark = 0x7f050093
com.ccppdfreader.app.debug:id/progress_indicator = 0x7f0800eb
com.ccppdfreader.app.debug:color/background_floating_material_dark = 0x7f05001d
com.ccppdfreader.app.debug:id/accessibility_custom_action_8 = 0x7f08002a
com.ccppdfreader.app.debug:attr/colorControlActivated = 0x7f030078
com.ccppdfreader.app.debug:color/tooltip_background_dark = 0x7f05009a
com.ccppdfreader.app.debug:attr/layout_constraintLeft_toLeftOf = 0x7f030121
com.ccppdfreader.app.debug:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
com.ccppdfreader.app.debug:color/abc_tint_default = 0x7f050014
com.ccppdfreader.app.debug:attr/titleTextStyle = 0x7f03020d
com.ccppdfreader.app.debug:color/abc_tint_btn_checkable = 0x7f050013
com.ccppdfreader.app.debug:id/action_bar_title = 0x7f080036
com.ccppdfreader.app.debug:color/md_theme_light_tertiary = 0x7f050074
com.ccppdfreader.app.debug:color/abc_secondary_text_material_light = 0x7f050012
com.ccppdfreader.app.debug:attr/goIcon = 0x7f0300ea
com.ccppdfreader.app.debug:attr/navigationContentDescription = 0x7f030175
com.ccppdfreader.app.debug:color/abc_search_url_text_pressed = 0x7f05000f
com.ccppdfreader.app.debug:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070018
com.ccppdfreader.app.debug:color/abc_primary_text_material_light = 0x7f05000c
com.ccppdfreader.app.debug:string/action_share = 0x7f0e0020
com.ccppdfreader.app.debug:color/abc_primary_text_material_dark = 0x7f05000b
com.ccppdfreader.app.debug:attr/actionModeCloseDrawable = 0x7f030015
com.ccppdfreader.app.debug:color/abc_primary_text_disable_only_material_light = 0x7f05000a
com.ccppdfreader.app.debug:dimen/card_elevation = 0x7f060056
com.ccppdfreader.app.debug:color/toolbar_overlay = 0x7f050099
com.ccppdfreader.app.debug:color/abc_primary_text_disable_only_material_dark = 0x7f050009
com.ccppdfreader.app.debug:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
com.ccppdfreader.app.debug:color/abc_hint_foreground_material_light = 0x7f050008
com.ccppdfreader.app.debug:color/abc_decor_view_status_guard_light = 0x7f050006
com.ccppdfreader.app.debug:color/accent_material_dark = 0x7f050019
com.ccppdfreader.app.debug:id/accessibility_custom_action_5 = 0x7f080027
com.ccppdfreader.app.debug:styleable/ActionBarLayout = 0x7f100001
com.ccppdfreader.app.debug:attr/maxButtonHeight = 0x7f030154
com.ccppdfreader.app.debug:attr/dragScale = 0x7f0300ad
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f0f0140
com.ccppdfreader.app.debug:color/primary_text_disabled_material_dark = 0x7f050080
com.ccppdfreader.app.debug:color/abc_color_highlight_material = 0x7f050004
com.ccppdfreader.app.debug:color/abc_btn_colored_text_material = 0x7f050003
com.ccppdfreader.app.debug:color/abc_btn_colored_borderless_text_material = 0x7f050002
com.ccppdfreader.app.debug:id/SHOW_PATH = 0x7f080007
com.ccppdfreader.app.debug:id/bounce = 0x7f080066
com.ccppdfreader.app.debug:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
com.ccppdfreader.app.debug:id/blocking = 0x7f080064
com.ccppdfreader.app.debug:attr/textOutlineColor = 0x7f0301f2
com.ccppdfreader.app.debug:id/homeAsUp = 0x7f0800ac
com.ccppdfreader.app.debug:bool/abc_action_bar_embed_tabs = 0x7f040000
com.ccppdfreader.app.debug:attr/windowMinWidthMinor = 0x7f030239
com.ccppdfreader.app.debug:attr/windowMinWidthMajor = 0x7f030238
com.ccppdfreader.app.debug:attr/motionEffect_strict = 0x7f030168
com.ccppdfreader.app.debug:attr/windowFixedWidthMinor = 0x7f030237
com.ccppdfreader.app.debug:color/secondary_text_disabled_material_dark = 0x7f05008b
com.ccppdfreader.app.debug:dimen/list_item_height_small = 0x7f060076
com.ccppdfreader.app.debug:string/abc_menu_meta_shortcut_label = 0x7f0e000d
com.ccppdfreader.app.debug:attr/viewTransitionOnNegativeCross = 0x7f030226
com.ccppdfreader.app.debug:attr/motionEffect_move = 0x7f030166
com.ccppdfreader.app.debug:dimen/abc_text_size_title_material_toolbar = 0x7f060050
com.ccppdfreader.app.debug:style/Widget.AppCompat.ListMenuView = 0x7f0f0146
com.ccppdfreader.app.debug:attr/windowFixedHeightMajor = 0x7f030234
com.ccppdfreader.app.debug:id/action_fit_height = 0x7f08003d
com.ccppdfreader.app.debug:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f07001b
com.ccppdfreader.app.debug:attr/actionOverflowMenuStyle = 0x7f030022
com.ccppdfreader.app.debug:attr/lineHeight = 0x7f030141
com.ccppdfreader.app.debug:attr/windowActionBarOverlay = 0x7f030232
com.ccppdfreader.app.debug:id/groups = 0x7f0800a9
com.ccppdfreader.app.debug:dimen/item_touch_helper_swipe_escape_velocity = 0x7f060074
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Small = 0x7f0f0021
com.ccppdfreader.app.debug:attr/waveShape = 0x7f03022f
com.ccppdfreader.app.debug:id/tag_on_receive_content_listener = 0x7f08012a
com.ccppdfreader.app.debug:attr/wavePhase = 0x7f03022e
com.ccppdfreader.app.debug:id/staticLayout = 0x7f08011e
com.ccppdfreader.app.debug:color/md_theme_light_error = 0x7f05005d
com.ccppdfreader.app.debug:style/TextAppearance.Compat.Notification.Info = 0x7f0f00ed
com.ccppdfreader.app.debug:drawable/abc_list_selector_disabled_holo_light = 0x7f07002d
com.ccppdfreader.app.debug:attr/singleChoiceItemLayout = 0x7f0301c1
com.ccppdfreader.app.debug:style/Widget.AppCompat.ListView = 0x7f0f0148
com.ccppdfreader.app.debug:color/tooltip_background_light = 0x7f05009b
com.ccppdfreader.app.debug:attr/tint = 0x7f030202
com.ccppdfreader.app.debug:attr/viewTransitionMode = 0x7f030224
com.ccppdfreader.app.debug:attr/transitionFlags = 0x7f03021c
com.ccppdfreader.app.debug:id/submit_area = 0x7f080122
com.ccppdfreader.app.debug:attr/transitionDisable = 0x7f03021a
com.ccppdfreader.app.debug:color/background_material_dark = 0x7f05001f
com.ccppdfreader.app.debug:dimen/highlight_alpha_material_light = 0x7f060069
com.ccppdfreader.app.debug:attr/numericModifiers = 0x7f03017a
com.ccppdfreader.app.debug:attr/transformPivotTarget = 0x7f030219
com.ccppdfreader.app.debug:id/accessibility_custom_action_12 = 0x7f080010
com.ccppdfreader.app.debug:drawable/abc_btn_borderless_material = 0x7f070002
com.ccppdfreader.app.debug:attr/springStiffness = 0x7f0301cb
com.ccppdfreader.app.debug:attr/trackTint = 0x7f030217
com.ccppdfreader.app.debug:attr/touchAnchorId = 0x7f030213
com.ccppdfreader.app.debug:attr/tooltipText = 0x7f030212
com.ccppdfreader.app.debug:string/no_search_results = 0x7f0e0043
com.ccppdfreader.app.debug:attr/tooltipFrameBackground = 0x7f030211
com.ccppdfreader.app.debug:dimen/abc_star_medium = 0x7f06003c
com.ccppdfreader.app.debug:attr/tooltipForegroundColor = 0x7f030210
com.ccppdfreader.app.debug:id/search_go_btn = 0x7f080100
com.ccppdfreader.app.debug:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f0f00f9
com.ccppdfreader.app.debug:attr/carousel_touchUp_velocityThreshold = 0x7f030061
com.ccppdfreader.app.debug:attr/spanCount = 0x7f0301c3
com.ccppdfreader.app.debug:attr/transitionPathRotate = 0x7f03021d
com.ccppdfreader.app.debug:attr/toolbarStyle = 0x7f03020f
com.ccppdfreader.app.debug:layout/abc_popup_menu_header_item_layout = 0x7f0b0012
com.ccppdfreader.app.debug:id/tag_on_receive_content_mime_types = 0x7f08012b
com.ccppdfreader.app.debug:drawable/ic_chevron_down = 0x7f07005f
com.ccppdfreader.app.debug:color/androidx_core_ripple_material_light = 0x7f05001b
com.ccppdfreader.app.debug:attr/titleTextColor = 0x7f03020c
com.ccppdfreader.app.debug:attr/backgroundTint = 0x7f030040
com.ccppdfreader.app.debug:color/primary_material_dark = 0x7f05007c
com.ccppdfreader.app.debug:attr/titleMargins = 0x7f03020a
com.ccppdfreader.app.debug:attr/titleMarginStart = 0x7f030208
com.ccppdfreader.app.debug:attr/titleMarginBottom = 0x7f030206
com.ccppdfreader.app.debug:color/background_material_light = 0x7f050020
com.ccppdfreader.app.debug:attr/tickMarkTint = 0x7f030200
com.ccppdfreader.app.debug:attr/tickMark = 0x7f0301ff
com.ccppdfreader.app.debug:attr/thumbTextPadding = 0x7f0301fc
com.ccppdfreader.app.debug:color/md_theme_light_inverseSurface = 0x7f050061
com.ccppdfreader.app.debug:attr/barrierDirection = 0x7f030044
com.ccppdfreader.app.debug:attr/layout_constraintRight_creator = 0x7f030123
com.ccppdfreader.app.debug:attr/titleMarginEnd = 0x7f030207
com.ccppdfreader.app.debug:id/scrollView = 0x7f0800fa
com.ccppdfreader.app.debug:attr/thickness = 0x7f0301fb
com.ccppdfreader.app.debug:attr/textureWidth = 0x7f0301f9
com.ccppdfreader.app.debug:string/nav_favorites = 0x7f0e003a
com.ccppdfreader.app.debug:attr/textureHeight = 0x7f0301f8
com.ccppdfreader.app.debug:id/radio = 0x7f0800ec
com.ccppdfreader.app.debug:attr/textureBlurFactor = 0x7f0301f6
com.ccppdfreader.app.debug:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.ccppdfreader.app.debug:attr/theme = 0x7f0301fa
com.ccppdfreader.app.debug:attr/textPanY = 0x7f0301f5
com.ccppdfreader.app.debug:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0f015e
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f0f0090
com.ccppdfreader.app.debug:id/pathRelative = 0x7f0800e3
com.ccppdfreader.app.debug:attr/flow_horizontalGap = 0x7f0300cf
com.ccppdfreader.app.debug:attr/onStateTransition = 0x7f030180
com.ccppdfreader.app.debug:attr/layout_constraintBaseline_toTopOf = 0x7f03010b
com.ccppdfreader.app.debug:layout/custom_dialog = 0x7f0b0022
com.ccppdfreader.app.debug:attr/layout_constraintEnd_toStartOf = 0x7f030114
com.ccppdfreader.app.debug:attr/textLocale = 0x7f0301f1
com.ccppdfreader.app.debug:id/META = 0x7f080003
com.ccppdfreader.app.debug:attr/textColorSearchUrl = 0x7f0301ef
com.ccppdfreader.app.debug:attr/textColorAlertDialogListItem = 0x7f0301ee
com.ccppdfreader.app.debug:attr/customColorValue = 0x7f030096
com.ccppdfreader.app.debug:attr/popupTheme = 0x7f030196
com.ccppdfreader.app.debug:attr/textBackgroundZoom = 0x7f0301ed
com.ccppdfreader.app.debug:color/md_theme_dark_inversePrimary = 0x7f050046
com.ccppdfreader.app.debug:attr/textBackgroundPanY = 0x7f0301eb
com.ccppdfreader.app.debug:attr/motionProgress = 0x7f03016e
com.ccppdfreader.app.debug:color/secondary_text_disabled_material_light = 0x7f05008c
com.ccppdfreader.app.debug:string/abc_searchview_description_search = 0x7f0e0015
com.ccppdfreader.app.debug:attr/textBackgroundPanX = 0x7f0301ea
com.ccppdfreader.app.debug:style/Base.V21.Theme.AppCompat.Light = 0x7f0f0053
com.ccppdfreader.app.debug:attr/round = 0x7f0301b0
com.ccppdfreader.app.debug:attr/textAppearanceSearchResultTitle = 0x7f0301e7
com.ccppdfreader.app.debug:attr/constraint_referenced_ids = 0x7f030084
com.ccppdfreader.app.debug:color/ripple_material_light = 0x7f050086
com.ccppdfreader.app.debug:attr/textAppearanceSearchResultSubtitle = 0x7f0301e6
com.ccppdfreader.app.debug:styleable/Transform = 0x7f10004d
com.ccppdfreader.app.debug:attr/colorControlHighlight = 0x7f030079
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0f00e3
com.ccppdfreader.app.debug:attr/checkMarkTintMode = 0x7f030065
com.ccppdfreader.app.debug:attr/multiChoiceItemLayout = 0x7f030174
com.ccppdfreader.app.debug:style/ThemeOverlay.AppCompat.Dialog = 0x7f0f0113
com.ccppdfreader.app.debug:string/search_menu_title = 0x7f0e004f
com.ccppdfreader.app.debug:attr/telltales_tailScale = 0x7f0301de
com.ccppdfreader.app.debug:string/error_permission_denied = 0x7f0e0033
com.ccppdfreader.app.debug:anim/scale_in = 0x7f01001b
com.ccppdfreader.app.debug:layout/abc_list_menu_item_icon = 0x7f0b000f
com.ccppdfreader.app.debug:attr/region_heightLessThan = 0x7f0301aa
com.ccppdfreader.app.debug:attr/telltales_tailColor = 0x7f0301dd
com.ccppdfreader.app.debug:style/Theme.CCPPDFReader.Splash = 0x7f0f010c
com.ccppdfreader.app.debug:attr/springDamping = 0x7f0301c9
com.ccppdfreader.app.debug:attr/switchStyle = 0x7f0301da
com.ccppdfreader.app.debug:attr/switchMinWidth = 0x7f0301d8
com.ccppdfreader.app.debug:id/list_item = 0x7f0800c5
com.ccppdfreader.app.debug:attr/srcCompat = 0x7f0301cd
com.ccppdfreader.app.debug:attr/constraints = 0x7f030086
com.ccppdfreader.app.debug:attr/springStopThreshold = 0x7f0301cc
com.ccppdfreader.app.debug:attr/layout_constraintWidth_min = 0x7f030132
com.ccppdfreader.app.debug:attr/textBackgroundRotate = 0x7f0301ec
com.ccppdfreader.app.debug:attr/drawableEndCompat = 0x7f0300b1
com.ccppdfreader.app.debug:attr/spinBars = 0x7f0301c4
com.ccppdfreader.app.debug:attr/sizePercent = 0x7f0301c2
com.ccppdfreader.app.debug:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
com.ccppdfreader.app.debug:attr/color = 0x7f030074
com.ccppdfreader.app.debug:attr/showText = 0x7f0301bf
com.ccppdfreader.app.debug:dimen/abc_text_size_body_2_material = 0x7f060040
com.ccppdfreader.app.debug:layout/layout_loading = 0x7f0b0028
com.ccppdfreader.app.debug:dimen/abc_dialog_fixed_height_major = 0x7f06001c
com.ccppdfreader.app.debug:color/md_theme_dark_inverseOnSurface = 0x7f050045
com.ccppdfreader.app.debug:attr/listPreferredItemHeightSmall = 0x7f03014c
com.ccppdfreader.app.debug:attr/checkMarkCompat = 0x7f030063
com.ccppdfreader.app.debug:string/abc_menu_ctrl_shortcut_label = 0x7f0e0009
com.ccppdfreader.app.debug:attr/showAsAction = 0x7f0301bc
com.ccppdfreader.app.debug:styleable/ActionMenuItemView = 0x7f100002
com.ccppdfreader.app.debug:id/dimensions = 0x7f080088
com.ccppdfreader.app.debug:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f0f0042
com.ccppdfreader.app.debug:attr/title = 0x7f030204
com.ccppdfreader.app.debug:attr/setsTag = 0x7f0301ba
com.ccppdfreader.app.debug:color/md_theme_light_secondaryContainer = 0x7f050071
com.ccppdfreader.app.debug:attr/selectableItemBackgroundBorderless = 0x7f0301b9
com.ccppdfreader.app.debug:attr/toolbarNavigationButtonStyle = 0x7f03020e
com.ccppdfreader.app.debug:attr/selectableItemBackground = 0x7f0301b8
com.ccppdfreader.app.debug:attr/layout_constraintCircleRadius = 0x7f030111
com.ccppdfreader.app.debug:attr/searchViewStyle = 0x7f0301b6
com.ccppdfreader.app.debug:id/accessibility_custom_action_22 = 0x7f08001b
com.ccppdfreader.app.debug:attr/searchHintIcon = 0x7f0301b4
com.ccppdfreader.app.debug:dimen/notification_subtext_size = 0x7f060089
com.ccppdfreader.app.debug:color/switch_thumb_disabled_material_light = 0x7f050092
com.ccppdfreader.app.debug:attr/thumbTint = 0x7f0301fd
com.ccppdfreader.app.debug:dimen/abc_dialog_fixed_width_major = 0x7f06001e
com.ccppdfreader.app.debug:attr/actionModePopupWindowStyle = 0x7f03001a
com.ccppdfreader.app.debug:attr/scaleFromTextSize = 0x7f0301b3
com.ccppdfreader.app.debug:attr/saturation = 0x7f0301b2
com.ccppdfreader.app.debug:attr/contentInsetRight = 0x7f03008c
com.ccppdfreader.app.debug:attr/fontProviderAuthority = 0x7f0300de
com.ccppdfreader.app.debug:attr/textAppearancePopupMenuHeader = 0x7f0301e5
com.ccppdfreader.app.debug:color/switch_thumb_disabled_material_dark = 0x7f050091
com.ccppdfreader.app.debug:color/md_theme_dark_onPrimary = 0x7f05004b
com.ccppdfreader.app.debug:attr/region_widthMoreThan = 0x7f0301ad
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ButtonBar = 0x7f0f0078
com.ccppdfreader.app.debug:id/action_mode_close_button = 0x7f080045
com.ccppdfreader.app.debug:id/dialog_button = 0x7f080087
com.ccppdfreader.app.debug:color/md_theme_light_onTertiary = 0x7f05006b
com.ccppdfreader.app.debug:attr/region_heightMoreThan = 0x7f0301ab
com.ccppdfreader.app.debug:attr/recyclerViewStyle = 0x7f0301a9
com.ccppdfreader.app.debug:drawable/abc_list_divider_material = 0x7f070024
com.ccppdfreader.app.debug:color/notification_icon_bg_color = 0x7f050077
com.ccppdfreader.app.debug:attr/ratingBarStyleIndicator = 0x7f0301a3
com.ccppdfreader.app.debug:id/submenuarrow = 0x7f080121
com.ccppdfreader.app.debug:attr/radioButtonStyle = 0x7f0301a1
com.ccppdfreader.app.debug:color/md_theme_light_onErrorContainer = 0x7f050064
com.ccppdfreader.app.debug:integer/abc_config_activityShortDur = 0x7f090001
com.ccppdfreader.app.debug:attr/queryPatterns = 0x7f0301a0
com.ccppdfreader.app.debug:attr/track = 0x7f030216
com.ccppdfreader.app.debug:attr/quantizeMotionSteps = 0x7f03019d
com.ccppdfreader.app.debug:attr/buttonGravity = 0x7f030051
com.ccppdfreader.app.debug:attr/barrierMargin = 0x7f030045
com.ccppdfreader.app.debug:id/anticipate = 0x7f080058
com.ccppdfreader.app.debug:attr/layout_constraintBottom_toTopOf = 0x7f03010e
com.ccppdfreader.app.debug:attr/quantizeMotionInterpolator = 0x7f03019b
com.ccppdfreader.app.debug:style/Platform.AppCompat = 0x7f0f00a1
com.ccppdfreader.app.debug:id/src_in = 0x7f080118
com.ccppdfreader.app.debug:attr/progressBarPadding = 0x7f030199
com.ccppdfreader.app.debug:attr/subtitleTextAppearance = 0x7f0301d4
com.ccppdfreader.app.debug:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f0f00b9
com.ccppdfreader.app.debug:layout/abc_action_mode_close_item_material = 0x7f0b0005
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0f0136
com.ccppdfreader.app.debug:attr/polarRelativeTo = 0x7f030194
com.ccppdfreader.app.debug:id/jumpToStart = 0x7f0800bd
com.ccppdfreader.app.debug:attr/fontStyle = 0x7f0300e5
com.ccppdfreader.app.debug:color/md_theme_light_onPrimaryContainer = 0x7f050066
com.ccppdfreader.app.debug:id/view_tree_view_model_store_owner = 0x7f080153
com.ccppdfreader.app.debug:attr/placeholder_emptyVisibility = 0x7f030193
com.ccppdfreader.app.debug:dimen/abc_action_button_min_width_material = 0x7f06000e
com.ccppdfreader.app.debug:id/action_context_bar = 0x7f08003b
com.ccppdfreader.app.debug:attr/pathMotionArc = 0x7f03018b
com.ccppdfreader.app.debug:string/nav_recent = 0x7f0e003c
com.ccppdfreader.app.debug:styleable/KeyAttribute = 0x7f100025
com.ccppdfreader.app.debug:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0f0128
com.ccppdfreader.app.debug:dimen/desktop_breakpoint = 0x7f06005f
com.ccppdfreader.app.debug:attr/percentWidth = 0x7f03018e
com.ccppdfreader.app.debug:attr/percentHeight = 0x7f03018d
com.ccppdfreader.app.debug:id/tag_on_apply_window_listener = 0x7f080129
com.ccppdfreader.app.debug:string/abc_action_bar_home_description = 0x7f0e0000
com.ccppdfreader.app.debug:dimen/text_size_label_small = 0x7f0600a4
com.ccppdfreader.app.debug:attr/triggerReceiver = 0x7f03021f
com.ccppdfreader.app.debug:id/action_search = 0x7f080049
com.ccppdfreader.app.debug:attr/path_percent = 0x7f03018c
com.ccppdfreader.app.debug:color/material_grey_300 = 0x7f05003c
com.ccppdfreader.app.debug:id/search_button = 0x7f0800fd
com.ccppdfreader.app.debug:attr/panelBackground = 0x7f030188
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f0f009a
com.ccppdfreader.app.debug:attr/trackTintMode = 0x7f030218
com.ccppdfreader.app.debug:attr/spinnerStyle = 0x7f0301c6
com.ccppdfreader.app.debug:attr/paddingEnd = 0x7f030185
com.ccppdfreader.app.debug:attr/percentX = 0x7f03018f
com.ccppdfreader.app.debug:attr/minWidth = 0x7f03015c
com.ccppdfreader.app.debug:attr/overlay = 0x7f030183
com.ccppdfreader.app.debug:attr/overlapAnchor = 0x7f030182
com.ccppdfreader.app.debug:attr/onTouchUp = 0x7f030181
com.ccppdfreader.app.debug:layout/activity_settings = 0x7f0b0020
com.ccppdfreader.app.debug:attr/contrast = 0x7f03008f
com.ccppdfreader.app.debug:attr/onShow = 0x7f03017f
com.ccppdfreader.app.debug:attr/layout_constraintRight_toLeftOf = 0x7f030124
com.ccppdfreader.app.debug:id/frost = 0x7f0800a3
com.ccppdfreader.app.debug:dimen/abc_text_size_menu_header_material = 0x7f06004a
com.ccppdfreader.app.debug:attr/buttonBarNeutralButtonStyle = 0x7f03004d
com.ccppdfreader.app.debug:attr/onPositiveCross = 0x7f03017e
com.ccppdfreader.app.debug:attr/onNegativeCross = 0x7f03017d
com.ccppdfreader.app.debug:attr/quantizeMotionPhase = 0x7f03019c
com.ccppdfreader.app.debug:attr/onHide = 0x7f03017c
com.ccppdfreader.app.debug:attr/nestedScrollViewStyle = 0x7f030179
com.ccppdfreader.app.debug:anim/abc_slide_in_bottom = 0x7f010006
com.ccppdfreader.app.debug:attr/windowFixedWidthMajor = 0x7f030236
com.ccppdfreader.app.debug:attr/nestedScrollFlags = 0x7f030178
com.ccppdfreader.app.debug:drawable/btn_radio_off_mtrl = 0x7f070053
com.ccppdfreader.app.debug:color/md_theme_dark_inverseSurface = 0x7f050047
com.ccppdfreader.app.debug:attr/navigationIcon = 0x7f030176
com.ccppdfreader.app.debug:drawable/ic_folder = 0x7f070068
com.ccppdfreader.app.debug:dimen/notification_action_text_size = 0x7f06007e
com.ccppdfreader.app.debug:attr/actionModeCloseContentDescription = 0x7f030014
com.ccppdfreader.app.debug:id/accessibility_custom_action_4 = 0x7f080026
com.ccppdfreader.app.debug:color/md_theme_light_onSurfaceVariant = 0x7f05006a
com.ccppdfreader.app.debug:color/md_theme_light_onSecondaryContainer = 0x7f050068
com.ccppdfreader.app.debug:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
com.ccppdfreader.app.debug:attr/layout_constraintWidth = 0x7f03012f
com.ccppdfreader.app.debug:attr/motionTarget = 0x7f030170
com.ccppdfreader.app.debug:attr/visibilityMode = 0x7f030228
com.ccppdfreader.app.debug:attr/motionEffect_start = 0x7f030167
com.ccppdfreader.app.debug:attr/motionStagger = 0x7f03016f
com.ccppdfreader.app.debug:attr/curveFit = 0x7f030093
com.ccppdfreader.app.debug:attr/staggered = 0x7f0301cf
com.ccppdfreader.app.debug:attr/motionInterpolator = 0x7f03016c
com.ccppdfreader.app.debug:color/bright_foreground_material_dark = 0x7f050026
com.ccppdfreader.app.debug:attr/listPreferredItemPaddingLeft = 0x7f03014e
com.ccppdfreader.app.debug:attr/motionEffect_viewTransition = 0x7f03016b
com.ccppdfreader.app.debug:attr/touchRegionId = 0x7f030215
com.ccppdfreader.app.debug:attr/textAppearanceListItemSmall = 0x7f0301e4
com.ccppdfreader.app.debug:attr/motionEffect_translationX = 0x7f030169
com.ccppdfreader.app.debug:style/Widget.AppCompat.TextView = 0x7f0f015b
com.ccppdfreader.app.debug:attr/flow_lastHorizontalBias = 0x7f0300d1
com.ccppdfreader.app.debug:attr/expandActivityOverflowButtonDrawable = 0x7f0300c2
com.ccppdfreader.app.debug:string/abc_toolbar_collapse_description = 0x7f0e001a
com.ccppdfreader.app.debug:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.ccppdfreader.app.debug:id/startVertical = 0x7f08011d
com.ccppdfreader.app.debug:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f0f00ad
com.ccppdfreader.app.debug:string/error_network = 0x7f0e0032
com.ccppdfreader.app.debug:drawable/ic_call_answer_video = 0x7f07005b
com.ccppdfreader.app.debug:attr/actionModeSplitBackground = 0x7f03001d
com.ccppdfreader.app.debug:id/accessibility_custom_action_18 = 0x7f080016
com.ccppdfreader.app.debug:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f0f014f
com.ccppdfreader.app.debug:attr/layout_constraintEnd_toEndOf = 0x7f030113
com.ccppdfreader.app.debug:color/abc_tint_switch_track = 0x7f050018
com.ccppdfreader.app.debug:attr/methodName = 0x7f03015a
com.ccppdfreader.app.debug:attr/flow_firstHorizontalStyle = 0x7f0300ca
com.ccppdfreader.app.debug:color/abc_search_url_text_normal = 0x7f05000e
com.ccppdfreader.app.debug:attr/autoTransition = 0x7f03003c
com.ccppdfreader.app.debug:attr/customReference = 0x7f03009c
com.ccppdfreader.app.debug:attr/circularflow_radiusInDP = 0x7f03006c
com.ccppdfreader.app.debug:attr/listChoiceIndicatorSingleAnimated = 0x7f030144
com.ccppdfreader.app.debug:styleable/ConstraintLayout_Layout = 0x7f100017
com.ccppdfreader.app.debug:attr/maxWidth = 0x7f030157
com.ccppdfreader.app.debug:attr/maxAcceleration = 0x7f030153
com.ccppdfreader.app.debug:color/material_grey_600 = 0x7f05003e
com.ccppdfreader.app.debug:id/message = 0x7f0800c8
com.ccppdfreader.app.debug:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f0f0046
com.ccppdfreader.app.debug:attr/dividerPadding = 0x7f0300aa
com.ccppdfreader.app.debug:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f07001a
com.ccppdfreader.app.debug:attr/logo = 0x7f030151
com.ccppdfreader.app.debug:attr/listPreferredItemPaddingRight = 0x7f03014f
com.ccppdfreader.app.debug:id/carryVelocity = 0x7f08006d
com.ccppdfreader.app.debug:attr/drawableTint = 0x7f0300b6
com.ccppdfreader.app.debug:style/Platform.V25.AppCompat = 0x7f0f00a8
com.ccppdfreader.app.debug:animator/fragment_close_exit = 0x7f020001
com.ccppdfreader.app.debug:attr/buttonStyleSmall = 0x7f030055
com.ccppdfreader.app.debug:attr/chainUseRtl = 0x7f030062
com.ccppdfreader.app.debug:attr/layout_constraintHorizontal_weight = 0x7f03011f
com.ccppdfreader.app.debug:style/Base.ThemeOverlay.AppCompat.Light = 0x7f0f0050
com.ccppdfreader.app.debug:id/easeOut = 0x7f080098
com.ccppdfreader.app.debug:attr/windowNoTitle = 0x7f03023a
com.ccppdfreader.app.debug:string/recent_files = 0x7f0e004b
com.ccppdfreader.app.debug:attr/layout_constraintBottom_creator = 0x7f03010c
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ListView = 0x7f0f008c
com.ccppdfreader.app.debug:attr/listLayout = 0x7f030147
com.ccppdfreader.app.debug:dimen/margin_medium = 0x7f06007b
com.ccppdfreader.app.debug:attr/actionModeBackground = 0x7f030012
com.ccppdfreader.app.debug:attr/listChoiceBackgroundIndicator = 0x7f030142
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f0f0137
com.ccppdfreader.app.debug:attr/layout_marginBaseline = 0x7f03013d
com.ccppdfreader.app.debug:attr/fastScrollEnabled = 0x7f0300c3
com.ccppdfreader.app.debug:attr/logoDescription = 0x7f030152
com.ccppdfreader.app.debug:color/switch_thumb_normal_material_dark = 0x7f050095
com.ccppdfreader.app.debug:attr/layout_goneMarginStart = 0x7f03013b
com.ccppdfreader.app.debug:menu/bookmarks_menu = 0x7f0c0000
com.ccppdfreader.app.debug:attr/layout_wrapBehaviorInParent = 0x7f03013f
com.ccppdfreader.app.debug:id/north = 0x7f0800d6
com.ccppdfreader.app.debug:drawable/abc_ic_menu_overflow_material = 0x7f07001c
com.ccppdfreader.app.debug:attr/imagePanX = 0x7f0300f7
com.ccppdfreader.app.debug:attr/animateCircleAngleTo = 0x7f03002e
com.ccppdfreader.app.debug:id/line1 = 0x7f0800c1
com.ccppdfreader.app.debug:styleable/AnimatedStateListDrawableCompat = 0x7f100007
com.ccppdfreader.app.debug:attr/layout_goneMarginBaseline = 0x7f030136
com.ccppdfreader.app.debug:layout/ime_base_split_test_activity = 0x7f0b0023
com.ccppdfreader.app.debug:id/title_template = 0x7f080141
com.ccppdfreader.app.debug:attr/editTextColor = 0x7f0300be
com.ccppdfreader.app.debug:attr/fontProviderFetchStrategy = 0x7f0300e0
com.ccppdfreader.app.debug:attr/layout_constraintWidth_default = 0x7f030130
com.ccppdfreader.app.debug:attr/actionModeShareDrawable = 0x7f03001c
com.ccppdfreader.app.debug:drawable/abc_switch_thumb_material = 0x7f070041
com.ccppdfreader.app.debug:attr/listChoiceIndicatorMultipleAnimated = 0x7f030143
com.ccppdfreader.app.debug:attr/layout_constraintTop_creator = 0x7f030129
com.ccppdfreader.app.debug:id/right = 0x7f0800f3
com.ccppdfreader.app.debug:color/foreground_material_light = 0x7f050033
com.ccppdfreader.app.debug:attr/textAppearanceLargePopupMenu = 0x7f0301e1
com.ccppdfreader.app.debug:id/top = 0x7f080144
com.ccppdfreader.app.debug:attr/preserveIconSpacing = 0x7f030198
com.ccppdfreader.app.debug:drawable/abc_star_black_48dp = 0x7f07003f
com.ccppdfreader.app.debug:dimen/icon_size_large = 0x7f06006f
com.ccppdfreader.app.debug:attr/flow_lastHorizontalStyle = 0x7f0300d2
com.ccppdfreader.app.debug:attr/ttcIndex = 0x7f030221
com.ccppdfreader.app.debug:id/animateToEnd = 0x7f080055
com.ccppdfreader.app.debug:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.ccppdfreader.app.debug:attr/layout_constraintStart_toStartOf = 0x7f030127
com.ccppdfreader.app.debug:color/call_notification_answer_color = 0x7f05002a
com.ccppdfreader.app.debug:attr/actionModeSelectAllDrawable = 0x7f03001b
com.ccppdfreader.app.debug:dimen/abc_config_prefDialogWidth = 0x7f060017
com.ccppdfreader.app.debug:attr/progressBarStyle = 0x7f03019a
com.ccppdfreader.app.debug:attr/layout_constraintRight_toRightOf = 0x7f030125
com.ccppdfreader.app.debug:attr/popupWindowStyle = 0x7f030197
com.ccppdfreader.app.debug:id/tag_unhandled_key_event_manager = 0x7f08012f
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f0f0082
com.ccppdfreader.app.debug:attr/layout_constraintHeight_percent = 0x7f03011c
com.ccppdfreader.app.debug:attr/triggerSlack = 0x7f030220
com.ccppdfreader.app.debug:mipmap/ic_launcher_round = 0x7f0d0001
com.ccppdfreader.app.debug:id/accessibility_custom_action_30 = 0x7f080024
com.ccppdfreader.app.debug:attr/circularflow_defaultAngle = 0x7f03006a
com.ccppdfreader.app.debug:attr/layout_constraintHeight_min = 0x7f03011b
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Body2 = 0x7f0f00be
com.ccppdfreader.app.debug:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
com.ccppdfreader.app.debug:attr/layoutDescription = 0x7f030103
com.ccppdfreader.app.debug:drawable/abc_ic_go_search_api_material = 0x7f070019
com.ccppdfreader.app.debug:attr/buttonCompat = 0x7f030050
com.ccppdfreader.app.debug:dimen/compat_notification_large_icon_max_height = 0x7f06005c
com.ccppdfreader.app.debug:attr/layout_constraintGuide_end = 0x7f030116
com.ccppdfreader.app.debug:layout/item_pdf_document = 0x7f0b0027
com.ccppdfreader.app.debug:dimen/notification_large_icon_height = 0x7f060081
com.ccppdfreader.app.debug:attr/mock_labelBackgroundColor = 0x7f03015f
com.ccppdfreader.app.debug:color/white = 0x7f05009c
com.ccppdfreader.app.debug:attr/dialogCornerRadius = 0x7f0300a4
com.ccppdfreader.app.debug:color/bright_foreground_material_light = 0x7f050027
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Large = 0x7f0f00c7
com.ccppdfreader.app.debug:attr/layout_constraintGuide_begin = 0x7f030115
com.ccppdfreader.app.debug:attr/layout_constraintDimensionRatio = 0x7f030112
com.ccppdfreader.app.debug:id/allStates = 0x7f080053
com.ccppdfreader.app.debug:style/Base.Theme.AppCompat.Light.Dialog = 0x7f0f0045
com.ccppdfreader.app.debug:attr/panelMenuListWidth = 0x7f03018a
com.ccppdfreader.app.debug:attr/contentInsetEndWithActions = 0x7f03008a
com.ccppdfreader.app.debug:id/action_text = 0x7f08004d
com.ccppdfreader.app.debug:drawable/abc_seekbar_tick_mark_material = 0x7f07003b
com.ccppdfreader.app.debug:attr/layout_constraintCircleAngle = 0x7f030110
com.ccppdfreader.app.debug:id/reverseSawtooth = 0x7f0800f2
com.ccppdfreader.app.debug:attr/paddingStart = 0x7f030186
com.ccppdfreader.app.debug:attr/fontFamily = 0x7f0300dd
com.ccppdfreader.app.debug:dimen/margin_small = 0x7f06007c
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0f00e4
com.ccppdfreader.app.debug:attr/deltaPolarRadius = 0x7f0300a2
com.ccppdfreader.app.debug:color/material_blue_grey_950 = 0x7f050038
com.ccppdfreader.app.debug:id/special_effects_controller_view_tag = 0x7f080110
com.ccppdfreader.app.debug:attr/waveVariesBy = 0x7f030230
com.ccppdfreader.app.debug:color/highlighted_text_material_dark = 0x7f050034
com.ccppdfreader.app.debug:attr/deriveConstraintsFrom = 0x7f0300a3
com.ccppdfreader.app.debug:attr/layout_constrainedWidth = 0x7f030107
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Display1 = 0x7f0f00c1
com.ccppdfreader.app.debug:color/accent_material_light = 0x7f05001a
com.ccppdfreader.app.debug:attr/layoutManager = 0x7f030105
com.ccppdfreader.app.debug:attr/actionDropDownStyle = 0x7f03000e
com.ccppdfreader.app.debug:attr/maxVelocity = 0x7f030156
com.ccppdfreader.app.debug:id/textViewPdfPath = 0x7f08013b
com.ccppdfreader.app.debug:attr/layout_constraintStart_toEndOf = 0x7f030126
com.ccppdfreader.app.debug:attr/layout_constraintHorizontal_chainStyle = 0x7f03011e
com.ccppdfreader.app.debug:attr/backgroundStacked = 0x7f03003f
com.ccppdfreader.app.debug:attr/applyMotionScene = 0x7f030030
com.ccppdfreader.app.debug:attr/constraintSetStart = 0x7f030083
com.ccppdfreader.app.debug:attr/lStar = 0x7f030100
com.ccppdfreader.app.debug:attr/keyPositionType = 0x7f0300ff
com.ccppdfreader.app.debug:color/pdf_background = 0x7f050078
com.ccppdfreader.app.debug:id/autoCompleteToStart = 0x7f08005e
com.ccppdfreader.app.debug:attr/actionModePasteDrawable = 0x7f030019
com.ccppdfreader.app.debug:attr/autoSizeTextType = 0x7f03003b
com.ccppdfreader.app.debug:attr/isLightTheme = 0x7f0300fd
com.ccppdfreader.app.debug:attr/ifTagSet = 0x7f0300f5
com.ccppdfreader.app.debug:attr/ifTagNotSet = 0x7f0300f4
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ImageButton = 0x7f0f0081
com.ccppdfreader.app.debug:attr/iconifiedByDefault = 0x7f0300f3
com.ccppdfreader.app.debug:attr/iconTintMode = 0x7f0300f2
com.ccppdfreader.app.debug:attr/textAppearanceSmallPopupMenu = 0x7f0301e8
com.ccppdfreader.app.debug:attr/homeLayout = 0x7f0300ef
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0f00cb
com.ccppdfreader.app.debug:attr/hideOnContentScroll = 0x7f0300ed
com.ccppdfreader.app.debug:attr/queryBackground = 0x7f03019e
com.ccppdfreader.app.debug:attr/buttonTintMode = 0x7f030057
com.ccppdfreader.app.debug:attr/actionViewClass = 0x7f030024
com.ccppdfreader.app.debug:attr/fastScrollHorizontalTrackDrawable = 0x7f0300c5
com.ccppdfreader.app.debug:attr/state_above_anchor = 0x7f0301d0
com.ccppdfreader.app.debug:dimen/text_size_body_medium = 0x7f06009d
com.ccppdfreader.app.debug:attr/motion_triggerOnCollision = 0x7f030172
com.ccppdfreader.app.debug:id/recyclerViewBookmarks = 0x7f0800ef
com.ccppdfreader.app.debug:id/graph = 0x7f0800a5
com.ccppdfreader.app.debug:attr/flow_horizontalAlign = 0x7f0300cd
com.ccppdfreader.app.debug:attr/gapBetweenBars = 0x7f0300e9
com.ccppdfreader.app.debug:id/info = 0x7f0800b7
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.TextView = 0x7f0f009d
com.ccppdfreader.app.debug:attr/reactiveGuide_applyToConstraintSet = 0x7f0301a7
com.ccppdfreader.app.debug:attr/fontVariationSettings = 0x7f0300e6
com.ccppdfreader.app.debug:attr/alertDialogCenterButtons = 0x7f030027
com.ccppdfreader.app.debug:attr/warmth = 0x7f03022a
com.ccppdfreader.app.debug:styleable/DrawerArrowToggle = 0x7f10001d
com.ccppdfreader.app.debug:attr/background = 0x7f03003d
com.ccppdfreader.app.debug:style/Platform.V21.AppCompat.Light = 0x7f0f00a7
com.ccppdfreader.app.debug:drawable/abc_popup_background_mtrl_mult = 0x7f070031
com.ccppdfreader.app.debug:attr/fontProviderPackage = 0x7f0300e2
com.ccppdfreader.app.debug:attr/fontProviderCerts = 0x7f0300df
com.ccppdfreader.app.debug:attr/font = 0x7f0300dc
com.ccppdfreader.app.debug:attr/navigationMode = 0x7f030177
com.ccppdfreader.app.debug:dimen/abc_button_padding_vertical_material = 0x7f060015
com.ccppdfreader.app.debug:attr/flow_wrapMode = 0x7f0300db
com.ccppdfreader.app.debug:attr/spinnerDropDownItemStyle = 0x7f0301c5
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f0f006b
com.ccppdfreader.app.debug:attr/flow_verticalStyle = 0x7f0300da
com.ccppdfreader.app.debug:dimen/abc_control_corner_material = 0x7f060018
com.ccppdfreader.app.debug:attr/listMenuViewStyle = 0x7f030148
com.ccppdfreader.app.debug:attr/flow_verticalGap = 0x7f0300d9
com.ccppdfreader.app.debug:color/dim_foreground_material_light = 0x7f05002f
com.ccppdfreader.app.debug:id/shortcut = 0x7f080108
com.ccppdfreader.app.debug:id/parent = 0x7f0800df
com.ccppdfreader.app.debug:color/md_theme_dark_onSecondaryContainer = 0x7f05004e
com.ccppdfreader.app.debug:attr/stackFromEnd = 0x7f0301ce
com.ccppdfreader.app.debug:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
com.ccppdfreader.app.debug:attr/flow_padding = 0x7f0300d6
com.ccppdfreader.app.debug:id/spacer = 0x7f08010f
com.ccppdfreader.app.debug:id/chain2 = 0x7f080071
com.ccppdfreader.app.debug:attr/switchTextAppearance = 0x7f0301db
com.ccppdfreader.app.debug:attr/springMass = 0x7f0301ca
com.ccppdfreader.app.debug:styleable/FontFamily = 0x7f10001e
com.ccppdfreader.app.debug:color/bright_foreground_disabled_material_light = 0x7f050023
com.ccppdfreader.app.debug:attr/dialogPreferredPadding = 0x7f0300a5
com.ccppdfreader.app.debug:attr/flow_firstVerticalStyle = 0x7f0300cc
com.ccppdfreader.app.debug:attr/minHeight = 0x7f03015b
com.ccppdfreader.app.debug:id/startHorizontal = 0x7f08011c
com.ccppdfreader.app.debug:color/md_theme_dark_error = 0x7f050043
com.ccppdfreader.app.debug:attr/layout_constraintCircle = 0x7f03010f
com.ccppdfreader.app.debug:attr/flow_firstHorizontalBias = 0x7f0300c9
com.ccppdfreader.app.debug:id/spread = 0x7f080113
com.ccppdfreader.app.debug:id/action_divider = 0x7f08003c
com.ccppdfreader.app.debug:attr/clickAction = 0x7f03006f
com.ccppdfreader.app.debug:attr/colorError = 0x7f03007b
com.ccppdfreader.app.debug:attr/layout_constraintWidth_max = 0x7f030131
com.ccppdfreader.app.debug:attr/dividerVertical = 0x7f0300ab
com.ccppdfreader.app.debug:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f0f00b6
com.ccppdfreader.app.debug:attr/actionBarPopupTheme = 0x7f030004
com.ccppdfreader.app.debug:attr/autoSizeMaxTextSize = 0x7f030037
com.ccppdfreader.app.debug:attr/fastScrollVerticalTrackDrawable = 0x7f0300c7
com.ccppdfreader.app.debug:attr/popupMenuStyle = 0x7f030195
com.ccppdfreader.app.debug:dimen/abc_text_size_display_2_material = 0x7f060044
com.ccppdfreader.app.debug:attr/layout_constraintVertical_bias = 0x7f03012c
com.ccppdfreader.app.debug:attr/lastBaselineToBottomHeight = 0x7f030101
com.ccppdfreader.app.debug:attr/fastScrollVerticalThumbDrawable = 0x7f0300c6
com.ccppdfreader.app.debug:id/expanded_menu = 0x7f08009f
com.ccppdfreader.app.debug:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.ccppdfreader.app.debug:color/md_theme_light_inverseOnSurface = 0x7f05005f
com.ccppdfreader.app.debug:color/black = 0x7f050021
com.ccppdfreader.app.debug:string/file_explorer = 0x7f0e0036
com.ccppdfreader.app.debug:attr/autoCompleteMode = 0x7f030035
com.ccppdfreader.app.debug:attr/drawableRightCompat = 0x7f0300b3
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f0f007e
com.ccppdfreader.app.debug:color/purple_700 = 0x7f050084
com.ccppdfreader.app.debug:drawable/ic_history = 0x7f07006b
com.ccppdfreader.app.debug:color/error_color_material_light = 0x7f050031
com.ccppdfreader.app.debug:string/abc_menu_function_shortcut_label = 0x7f0e000c
com.ccppdfreader.app.debug:attr/deltaPolarAngle = 0x7f0300a1
com.ccppdfreader.app.debug:attr/actionBarSize = 0x7f030005
com.ccppdfreader.app.debug:attr/actionBarSplitStyle = 0x7f030006
com.ccppdfreader.app.debug:id/accessibility_custom_action_31 = 0x7f080025
com.ccppdfreader.app.debug:attr/elevation = 0x7f0300c0
com.ccppdfreader.app.debug:dimen/notification_large_icon_width = 0x7f060082
com.ccppdfreader.app.debug:attr/editTextBackground = 0x7f0300bd
com.ccppdfreader.app.debug:attr/collapseContentDescription = 0x7f030072
com.ccppdfreader.app.debug:attr/dropdownListPreferredItemHeight = 0x7f0300bb
com.ccppdfreader.app.debug:attr/percentY = 0x7f030190
com.ccppdfreader.app.debug:id/search_bar = 0x7f0800fc
com.ccppdfreader.app.debug:style/Widget.AppCompat.SearchView = 0x7f0f0153
com.ccppdfreader.app.debug:attr/closeItemLayout = 0x7f030071
com.ccppdfreader.app.debug:id/notification_main_column_container = 0x7f0800d9
com.ccppdfreader.app.debug:id/pdfContainer = 0x7f0800e4
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.PopupMenu = 0x7f0f0142
com.ccppdfreader.app.debug:dimen/notification_right_side_padding_top = 0x7f060086
com.ccppdfreader.app.debug:attr/drawableTintMode = 0x7f0300b7
com.ccppdfreader.app.debug:attr/actionBarTabBarStyle = 0x7f030008
com.ccppdfreader.app.debug:attr/alertDialogStyle = 0x7f030028
com.ccppdfreader.app.debug:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
com.ccppdfreader.app.debug:color/md_theme_light_inversePrimary = 0x7f050060
com.ccppdfreader.app.debug:id/showCustom = 0x7f080109
com.ccppdfreader.app.debug:attr/actionLayout = 0x7f03000f
com.ccppdfreader.app.debug:attr/drawPath = 0x7f0300af
com.ccppdfreader.app.debug:attr/dragThreshold = 0x7f0300ae
com.ccppdfreader.app.debug:attr/paddingBottomNoButtons = 0x7f030184
com.ccppdfreader.app.debug:id/immediateStop = 0x7f0800b5
com.ccppdfreader.app.debug:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f0f00ba
com.ccppdfreader.app.debug:dimen/tooltip_precise_anchor_extra_offset = 0x7f0600ac
com.ccppdfreader.app.debug:attr/actionModeStyle = 0x7f03001e
com.ccppdfreader.app.debug:attr/titleMarginTop = 0x7f030209
com.ccppdfreader.app.debug:id/dragDown = 0x7f080090
com.ccppdfreader.app.debug:id/barrier = 0x7f08005f
com.ccppdfreader.app.debug:id/customPanel = 0x7f080080
com.ccppdfreader.app.debug:attr/imageZoom = 0x7f0300fa
com.ccppdfreader.app.debug:attr/iconTint = 0x7f0300f1
com.ccppdfreader.app.debug:dimen/tooltip_margin = 0x7f0600ab
com.ccppdfreader.app.debug:dimen/notification_small_icon_size_as_large = 0x7f060088
com.ccppdfreader.app.debug:dimen/icon_size_medium = 0x7f060070
com.ccppdfreader.app.debug:attr/actionModeCloseButtonStyle = 0x7f030013
com.ccppdfreader.app.debug:attr/dividerHorizontal = 0x7f0300a9
com.ccppdfreader.app.debug:attr/autoSizePresetSizes = 0x7f030039
com.ccppdfreader.app.debug:dimen/notification_top_pad_large_text = 0x7f06008b
com.ccppdfreader.app.debug:attr/layout_constraintVertical_weight = 0x7f03012e
com.ccppdfreader.app.debug:color/abc_tint_spinner = 0x7f050017
com.ccppdfreader.app.debug:integer/abc_config_activityDefaultDur = 0x7f090000
com.ccppdfreader.app.debug:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f0f012d
com.ccppdfreader.app.debug:attr/actionModeCutDrawable = 0x7f030017
com.ccppdfreader.app.debug:drawable/ic_more_vert = 0x7f070070
com.ccppdfreader.app.debug:drawable/abc_tab_indicator_mtrl_alpha = 0x7f070044
com.ccppdfreader.app.debug:attr/transitionEasing = 0x7f03021b
com.ccppdfreader.app.debug:attr/customNavigationLayout = 0x7f03009a
com.ccppdfreader.app.debug:attr/circularflow_defaultRadius = 0x7f03006b
com.ccppdfreader.app.debug:attr/customFloatValue = 0x7f030098
com.ccppdfreader.app.debug:attr/fontProviderQuery = 0x7f0300e3
com.ccppdfreader.app.debug:color/md_theme_light_onTertiaryContainer = 0x7f05006c
com.ccppdfreader.app.debug:attr/itemPadding = 0x7f0300fe
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ListView.Menu = 0x7f0f008e
com.ccppdfreader.app.debug:id/accessibility_custom_action_11 = 0x7f08000f
com.ccppdfreader.app.debug:attr/drawableStartCompat = 0x7f0300b5
com.ccppdfreader.app.debug:anim/abc_slide_out_bottom = 0x7f010008
com.ccppdfreader.app.debug:string/bookmarks = 0x7f0e0025
com.ccppdfreader.app.debug:attr/mock_diagonalsColor = 0x7f03015d
com.ccppdfreader.app.debug:attr/displayOptions = 0x7f0300a7
com.ccppdfreader.app.debug:id/parentPanel = 0x7f0800e0
com.ccppdfreader.app.debug:menu/main_menu = 0x7f0c0003
com.ccppdfreader.app.debug:style/AlertDialog.AppCompat.Light = 0x7f0f0001
com.ccppdfreader.app.debug:attr/collapseIcon = 0x7f030073
com.ccppdfreader.app.debug:string/permission_deny = 0x7f0e0047
com.ccppdfreader.app.debug:attr/customIntegerValue = 0x7f030099
com.ccppdfreader.app.debug:attr/carousel_touchUp_dampeningFactor = 0x7f030060
com.ccppdfreader.app.debug:attr/layout_optimizationLevel = 0x7f03013e
com.ccppdfreader.app.debug:dimen/text_size_body_small = 0x7f06009e
com.ccppdfreader.app.debug:attr/dropDownListViewStyle = 0x7f0300ba
com.ccppdfreader.app.debug:id/tv_loading_message = 0x7f080149
com.ccppdfreader.app.debug:style/Widget.AppCompat.RatingBar.Indicator = 0x7f0f0151
com.ccppdfreader.app.debug:drawable/ic_rotate = 0x7f070074
com.ccppdfreader.app.debug:attr/contentInsetStart = 0x7f03008d
com.ccppdfreader.app.debug:dimen/abc_action_bar_default_height_material = 0x7f060002
com.ccppdfreader.app.debug:attr/wavePeriod = 0x7f03022d
com.ccppdfreader.app.debug:attr/content = 0x7f030087
com.ccppdfreader.app.debug:attr/layout_constraintTop_toTopOf = 0x7f03012b
com.ccppdfreader.app.debug:color/abc_secondary_text_material_dark = 0x7f050011
com.ccppdfreader.app.debug:style/Theme.AppCompat.Light.NoActionBar = 0x7f0f0108
com.ccppdfreader.app.debug:id/notification_background = 0x7f0800d7
com.ccppdfreader.app.debug:dimen/abc_text_size_display_1_material = 0x7f060043
com.ccppdfreader.app.debug:id/spline = 0x7f080111
com.ccppdfreader.app.debug:attr/constraintRotate = 0x7f030080
com.ccppdfreader.app.debug:color/dim_foreground_disabled_material_light = 0x7f05002d
com.ccppdfreader.app.debug:string/search_results = 0x7f0e0050
com.ccppdfreader.app.debug:id/layout = 0x7f0800be
com.ccppdfreader.app.debug:anim/fade_in = 0x7f010018
com.ccppdfreader.app.debug:styleable/StateListDrawable = 0x7f100046
com.ccppdfreader.app.debug:drawable/abc_btn_colored_material = 0x7f070007
com.ccppdfreader.app.debug:dimen/padding_medium = 0x7f06008e
com.ccppdfreader.app.debug:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f07004c
com.ccppdfreader.app.debug:attr/colorSwitchThumbNormal = 0x7f03007e
com.ccppdfreader.app.debug:attr/colorPrimaryDark = 0x7f03007d
com.ccppdfreader.app.debug:attr/telltales_velocityMode = 0x7f0301df
com.ccppdfreader.app.debug:attr/seekBarStyle = 0x7f0301b7
com.ccppdfreader.app.debug:attr/carousel_emptyViewsBehavior = 0x7f030059
com.ccppdfreader.app.debug:id/textSpacerNoButtons = 0x7f080134
com.ccppdfreader.app.debug:attr/actionBarTheme = 0x7f03000b
com.ccppdfreader.app.debug:attr/showPaths = 0x7f0301be
com.ccppdfreader.app.debug:attr/actionButtonStyle = 0x7f03000d
com.ccppdfreader.app.debug:xml/data_extraction_rules = 0x7f110001
com.ccppdfreader.app.debug:attr/colorAccent = 0x7f030075
com.ccppdfreader.app.debug:attr/customPixelDimension = 0x7f03009b
com.ccppdfreader.app.debug:string/abc_menu_space_shortcut_label = 0x7f0e000f
com.ccppdfreader.app.debug:attr/activityChooserViewStyle = 0x7f030025
com.ccppdfreader.app.debug:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
com.ccppdfreader.app.debug:dimen/abc_text_size_display_4_material = 0x7f060046
com.ccppdfreader.app.debug:attr/actionProviderClass = 0x7f030023
com.ccppdfreader.app.debug:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f0f00fb
com.ccppdfreader.app.debug:id/animateToStart = 0x7f080056
com.ccppdfreader.app.debug:attr/motionEffect_translationY = 0x7f03016a
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f0f0093
com.ccppdfreader.app.debug:id/dragAnticlockwise = 0x7f08008e
com.ccppdfreader.app.debug:drawable/ic_launcher_foreground = 0x7f07006e
com.ccppdfreader.app.debug:dimen/text_size_title_small = 0x7f0600a7
com.ccppdfreader.app.debug:attr/clearsTag = 0x7f03006e
com.ccppdfreader.app.debug:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
com.ccppdfreader.app.debug:attr/firstBaselineToTopHeight = 0x7f0300c8
com.ccppdfreader.app.debug:attr/viewInflaterClass = 0x7f030223
com.ccppdfreader.app.debug:style/Widget.AppCompat.DrawerArrowToggle = 0x7f0f012c
com.ccppdfreader.app.debug:color/md_theme_dark_surfaceVariant = 0x7f050059
com.ccppdfreader.app.debug:attr/buttonStyle = 0x7f030054
com.ccppdfreader.app.debug:attr/drawableSize = 0x7f0300b4
com.ccppdfreader.app.debug:style/Animation.AppCompat.DropDownUp = 0x7f0f0003
com.ccppdfreader.app.debug:attr/circularflow_angles = 0x7f030069
com.ccppdfreader.app.debug:attr/drawableLeftCompat = 0x7f0300b2
com.ccppdfreader.app.debug:color/abc_decor_view_status_guard = 0x7f050005
com.ccppdfreader.app.debug:attr/checkedTextViewStyle = 0x7f030067
com.ccppdfreader.app.debug:attr/checkMarkTint = 0x7f030064
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0f00e0
com.ccppdfreader.app.debug:attr/listPreferredItemPaddingEnd = 0x7f03014d
com.ccppdfreader.app.debug:attr/ratingBarStyleSmall = 0x7f0301a4
com.ccppdfreader.app.debug:attr/constraint_referenced_tags = 0x7f030085
com.ccppdfreader.app.debug:attr/carousel_touchUpMode = 0x7f03005f
com.ccppdfreader.app.debug:dimen/abc_dropdownitem_icon_width = 0x7f060029
com.ccppdfreader.app.debug:attr/carousel_previousState = 0x7f03005e
com.ccppdfreader.app.debug:id/autoCompleteToEnd = 0x7f08005d
com.ccppdfreader.app.debug:attr/carousel_nextState = 0x7f03005d
com.ccppdfreader.app.debug:string/abc_menu_sym_shortcut_label = 0x7f0e0010
com.ccppdfreader.app.debug:attr/imagePanY = 0x7f0300f8
com.ccppdfreader.app.debug:attr/carousel_infinite = 0x7f03005c
com.ccppdfreader.app.debug:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.ccppdfreader.app.debug:attr/autoSizeMinTextSize = 0x7f030038
com.ccppdfreader.app.debug:id/uniform = 0x7f08014b
com.ccppdfreader.app.debug:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0016
com.ccppdfreader.app.debug:attr/defaultQueryHint = 0x7f03009f
com.ccppdfreader.app.debug:attr/customBoolean = 0x7f030094
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f0f013b
com.ccppdfreader.app.debug:attr/reactiveGuide_applyToAllConstraintSets = 0x7f0301a6
com.ccppdfreader.app.debug:attr/buttonTint = 0x7f030056
com.ccppdfreader.app.debug:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.ccppdfreader.app.debug:dimen/text_size_headline_medium = 0x7f0600a0
com.ccppdfreader.app.debug:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.ccppdfreader.app.debug:attr/guidelineUseRtl = 0x7f0300eb
com.ccppdfreader.app.debug:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0f00f1
com.ccppdfreader.app.debug:attr/duration = 0x7f0300bc
com.ccppdfreader.app.debug:dimen/tablet_breakpoint = 0x7f06009b
com.ccppdfreader.app.debug:attr/customDimension = 0x7f030097
com.ccppdfreader.app.debug:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f0f0040
com.ccppdfreader.app.debug:id/wrap_content = 0x7f080159
com.ccppdfreader.app.debug:id/view_transition = 0x7f08014f
com.ccppdfreader.app.debug:dimen/abc_list_item_height_large_material = 0x7f060030
com.ccppdfreader.app.debug:attr/carousel_forwardTransition = 0x7f03005b
com.ccppdfreader.app.debug:attr/buttonBarPositiveButtonStyle = 0x7f03004e
com.ccppdfreader.app.debug:attr/mock_label = 0x7f03015e
com.ccppdfreader.app.debug:attr/textAppearanceListItem = 0x7f0301e2
com.ccppdfreader.app.debug:attr/springBoundary = 0x7f0301c8
com.ccppdfreader.app.debug:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.ccppdfreader.app.debug:attr/textBackground = 0x7f0301e9
com.ccppdfreader.app.debug:attr/listPreferredItemHeight = 0x7f03014a
com.ccppdfreader.app.debug:attr/controlBackground = 0x7f030090
com.ccppdfreader.app.debug:attr/layout_constraintBaseline_toBottomOf = 0x7f03010a
com.ccppdfreader.app.debug:attr/colorButtonNormal = 0x7f030077
com.ccppdfreader.app.debug:color/button_material_light = 0x7f050029
com.ccppdfreader.app.debug:attr/mock_showLabel = 0x7f030162
com.ccppdfreader.app.debug:attr/borderRoundPercent = 0x7f030048
com.ccppdfreader.app.debug:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f0f00a5
com.ccppdfreader.app.debug:drawable/ic_sort = 0x7f070079
com.ccppdfreader.app.debug:attr/closeIcon = 0x7f030070
com.ccppdfreader.app.debug:attr/carousel_firstView = 0x7f03005a
com.ccppdfreader.app.debug:string/abc_shareactionprovider_share_with_application = 0x7f0e0019
com.ccppdfreader.app.debug:color/md_theme_dark_onSurface = 0x7f05004f
com.ccppdfreader.app.debug:attr/blendSrc = 0x7f030046
com.ccppdfreader.app.debug:attr/barrierAllowsGoneWidgets = 0x7f030043
com.ccppdfreader.app.debug:attr/backgroundTintMode = 0x7f030041
com.ccppdfreader.app.debug:style/ThemeOverlay.AppCompat.DayNight = 0x7f0f0111
com.ccppdfreader.app.debug:id/callMeasure = 0x7f08006c
com.ccppdfreader.app.debug:drawable/ic_refresh = 0x7f070073
com.ccppdfreader.app.debug:color/material_blue_grey_800 = 0x7f050036
com.ccppdfreader.app.debug:attr/layout_goneMarginEnd = 0x7f030138
com.ccppdfreader.app.debug:string/permission_storage_title = 0x7f0e004a
com.ccppdfreader.app.debug:attr/tintMode = 0x7f030203
com.ccppdfreader.app.debug:attr/backgroundSplit = 0x7f03003e
com.ccppdfreader.app.debug:attr/layout_goneMarginTop = 0x7f03013c
com.ccppdfreader.app.debug:dimen/notification_action_icon_size = 0x7f06007d
com.ccppdfreader.app.debug:animator/fragment_fade_exit = 0x7f020003
com.ccppdfreader.app.debug:attr/splitTrack = 0x7f0301c7
com.ccppdfreader.app.debug:id/tag_accessibility_clickable_spans = 0x7f080126
com.ccppdfreader.app.debug:anim/abc_tooltip_enter = 0x7f01000a
com.ccppdfreader.app.debug:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.ccppdfreader.app.debug:attr/layout_constraintLeft_creator = 0x7f030120
com.ccppdfreader.app.debug:color/primary_text_default_material_light = 0x7f05007f
com.ccppdfreader.app.debug:attr/layout_constraintLeft_toRightOf = 0x7f030122
com.ccppdfreader.app.debug:attr/alertDialogButtonGroupStyle = 0x7f030026
com.ccppdfreader.app.debug:attr/autoSizeStepGranularity = 0x7f03003a
com.ccppdfreader.app.debug:attr/showTitle = 0x7f0301c0
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Display3 = 0x7f0f00c3
com.ccppdfreader.app.debug:id/x_left = 0x7f08015b
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0f00d9
com.ccppdfreader.app.debug:drawable/notification_oversize_large_icon_bg = 0x7f070086
com.ccppdfreader.app.debug:attr/reactiveGuide_animateChange = 0x7f0301a5
com.ccppdfreader.app.debug:string/action_settings = 0x7f0e001f
com.ccppdfreader.app.debug:color/md_theme_dark_secondary = 0x7f050056
com.ccppdfreader.app.debug:dimen/abc_seekbar_track_background_height_material = 0x7f060038
com.ccppdfreader.app.debug:style/TextAppearance.Compat.Notification = 0x7f0f00ec
com.ccppdfreader.app.debug:attr/actionModeCopyDrawable = 0x7f030016
com.ccppdfreader.app.debug:id/tag_transition_group = 0x7f08012e
com.ccppdfreader.app.debug:attr/panelMenuListTheme = 0x7f030189
com.ccppdfreader.app.debug:attr/arcMode = 0x7f030031
com.ccppdfreader.app.debug:anim/fragment_fast_out_extra_slow_in = 0x7f01001a
com.ccppdfreader.app.debug:attr/listPreferredItemPaddingStart = 0x7f030150
com.ccppdfreader.app.debug:attr/imageButtonStyle = 0x7f0300f6
com.ccppdfreader.app.debug:attr/contentDescription = 0x7f030088
com.ccppdfreader.app.debug:attr/animateRelativeTo = 0x7f03002f
com.ccppdfreader.app.debug:id/neverCompleteToEnd = 0x7f0800d1
com.ccppdfreader.app.debug:attr/alphabeticModifiers = 0x7f03002c
com.ccppdfreader.app.debug:layout/abc_search_view = 0x7f0b0019
com.ccppdfreader.app.debug:id/end = 0x7f08009d
com.ccppdfreader.app.debug:attr/actionBarStyle = 0x7f030007
com.ccppdfreader.app.debug:attr/defaultDuration = 0x7f03009e
com.ccppdfreader.app.debug:id/spread_inside = 0x7f080114
com.ccppdfreader.app.debug:id/accessibility_custom_action_7 = 0x7f080029
com.ccppdfreader.app.debug:style/Widget.AppCompat.PopupWindow = 0x7f0f014d
com.ccppdfreader.app.debug:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
com.ccppdfreader.app.debug:animator/fragment_open_enter = 0x7f020004
com.ccppdfreader.app.debug:attr/icon = 0x7f0300f0
com.ccppdfreader.app.debug:attr/actionOverflowButtonStyle = 0x7f030021
com.ccppdfreader.app.debug:attr/triggerId = 0x7f03021e
com.ccppdfreader.app.debug:attr/motionPathRotate = 0x7f03016d
com.ccppdfreader.app.debug:id/progress_circular = 0x7f0800e9
com.ccppdfreader.app.debug:dimen/tooltip_vertical_padding = 0x7f0600ae
com.ccppdfreader.app.debug:attr/layout_constraintWidth_percent = 0x7f030133
com.ccppdfreader.app.debug:drawable/abc_seekbar_thumb_material = 0x7f07003a
com.ccppdfreader.app.debug:dimen/notification_top_pad = 0x7f06008a
com.ccppdfreader.app.debug:attr/actionModeTheme = 0x7f03001f
com.ccppdfreader.app.debug:attr/moveWhenScrollAtTop = 0x7f030173
com.ccppdfreader.app.debug:drawable/ic_settings = 0x7f070077
com.ccppdfreader.app.debug:dimen/notification_right_icon_size = 0x7f060085
com.ccppdfreader.app.debug:id/textViewPdfTitle = 0x7f08013d
com.ccppdfreader.app.debug:attr/subtitle = 0x7f0301d3
com.ccppdfreader.app.debug:id/rectangles = 0x7f0800ee
com.ccppdfreader.app.debug:attr/titleMargin = 0x7f030205
com.ccppdfreader.app.debug:attr/upDuration = 0x7f030222
com.ccppdfreader.app.debug:style/Theme.CCPPDFReader.FullScreen = 0x7f0f010b
com.ccppdfreader.app.debug:drawable/abc_cab_background_top_mtrl_alpha = 0x7f070011
com.ccppdfreader.app.debug:anim/abc_slide_in_top = 0x7f010007
com.ccppdfreader.app.debug:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f0f0047
com.ccppdfreader.app.debug:id/never = 0x7f0800d0
com.ccppdfreader.app.debug:attr/windowActionBar = 0x7f030231
com.ccppdfreader.app.debug:drawable/abc_item_background_holo_dark = 0x7f070022
com.ccppdfreader.app.debug:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f0f0129
com.ccppdfreader.app.debug:dimen/pdf_page_margin = 0x7f060091
com.ccppdfreader.app.debug:attr/arrowShaftLength = 0x7f030033
com.ccppdfreader.app.debug:id/accessibility_custom_action_29 = 0x7f080022
com.ccppdfreader.app.debug:attr/roundPercent = 0x7f0301b1
com.ccppdfreader.app.debug:attr/colorBackgroundFloating = 0x7f030076
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0f0075
com.ccppdfreader.app.debug:attr/targetId = 0x7f0301dc
com.ccppdfreader.app.debug:attr/subtitleTextColor = 0x7f0301d5
com.ccppdfreader.app.debug:attr/flow_verticalBias = 0x7f0300d8
com.ccppdfreader.app.debug:attr/actionBarItemBackground = 0x7f030003
com.ccppdfreader.app.debug:dimen/abc_text_size_button_material = 0x7f060041
com.ccppdfreader.app.debug:attr/allowStacking = 0x7f03002a
com.ccppdfreader.app.debug:string/call_notification_answer_action = 0x7f0e0027
com.ccppdfreader.app.debug:attr/checkboxStyle = 0x7f030066
com.ccppdfreader.app.debug:animator/fragment_fade_enter = 0x7f020002
com.ccppdfreader.app.debug:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f07004b
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f0f0027
com.ccppdfreader.app.debug:drawable/abc_ratingbar_indicator_material = 0x7f070032
com.ccppdfreader.app.debug:attr/fontProviderFetchTimeout = 0x7f0300e1
com.ccppdfreader.app.debug:attr/carousel_backwardTransition = 0x7f030058
com.ccppdfreader.app.debug:interpolator/fast_out_slow_in = 0x7f0a0006
com.ccppdfreader.app.debug:color/md_theme_light_onError = 0x7f050063
com.ccppdfreader.app.debug:string/theme_dark = 0x7f0e0058
com.ccppdfreader.app.debug:style/TextAppearance.AppCompat = 0x7f0f00bc
com.ccppdfreader.app.debug:attr/perpendicularPath_percent = 0x7f030191
com.ccppdfreader.app.debug:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.ccppdfreader.app.debug:color/primary_material_light = 0x7f05007d
com.ccppdfreader.app.debug:animator/fragment_open_exit = 0x7f020005
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ActionButton = 0x7f0f0139
com.ccppdfreader.app.debug:id/match_constraint = 0x7f0800c6
com.ccppdfreader.app.debug:dimen/fab_margin = 0x7f060063
com.ccppdfreader.app.debug:attr/shortcutMatchRequired = 0x7f0301bb
com.ccppdfreader.app.debug:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.ccppdfreader.app.debug:attr/buttonPanelSideLayout = 0x7f030053
com.ccppdfreader.app.debug:attr/motion_postLayoutCollision = 0x7f030171
com.ccppdfreader.app.debug:attr/contentInsetLeft = 0x7f03008b
com.ccppdfreader.app.debug:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0f002b
com.ccppdfreader.app.debug:attr/motionEffect_alpha = 0x7f030164
com.ccppdfreader.app.debug:drawable/abc_list_selector_holo_light = 0x7f07002f
com.ccppdfreader.app.debug:attr/textureEffect = 0x7f0301f7
com.ccppdfreader.app.debug:anim/abc_popup_enter = 0x7f010003
com.ccppdfreader.app.debug:color/md_theme_dark_onTertiary = 0x7f050051
com.ccppdfreader.app.debug:dimen/abc_dialog_title_divider_material = 0x7f060026
com.ccppdfreader.app.debug:attr/actionModeFindDrawable = 0x7f030018
com.ccppdfreader.app.debug:attr/actionBarWidgetTheme = 0x7f03000c
com.ccppdfreader.app.debug:anim/abc_slide_out_top = 0x7f010009
com.ccppdfreader.app.debug:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f0f008d
com.ccppdfreader.app.debug:dimen/abc_alert_dialog_button_dimen = 0x7f060011
com.ccppdfreader.app.debug:anim/slide_in_right = 0x7f01001c
com.ccppdfreader.app.debug:attr/constraintSetEnd = 0x7f030082
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f0f013a
com.ccppdfreader.app.debug:attr/currentState = 0x7f030092
com.ccppdfreader.app.debug:id/is_pooling_container_tag = 0x7f0800b9
com.ccppdfreader.app.debug:id/contentPanel = 0x7f08007b
com.ccppdfreader.app.debug:attr/suggestionRowLayout = 0x7f0301d7
com.ccppdfreader.app.debug:attr/brightness = 0x7f03004a
com.ccppdfreader.app.debug:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f0f0141
com.ccppdfreader.app.debug:string/browse_files = 0x7f0e0026
com.ccppdfreader.app.debug:attr/altSrc = 0x7f03002d
com.ccppdfreader.app.debug:attr/voiceIcon = 0x7f030229
com.ccppdfreader.app.debug:drawable/abc_text_select_handle_middle_mtrl = 0x7f070047
com.ccppdfreader.app.debug:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f070016
com.ccppdfreader.app.debug:attr/viewTransitionOnPositiveCross = 0x7f030227
com.ccppdfreader.app.debug:attr/borderRound = 0x7f030047
