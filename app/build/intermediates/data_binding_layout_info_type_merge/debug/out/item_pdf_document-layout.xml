<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_pdf_document" modulePackage="com.ccppdfreader.app" filePath="app/src/main/res/layout/item_pdf_document.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_pdf_document_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="111" endOffset="35"/></Target><Target id="@+id/textViewPdfTitle" view="TextView"><Expressions/><location startLine="31" startOffset="12" endLine="40" endOffset="41"/></Target><Target id="@+id/textViewPdfPath" view="TextView"><Expressions/><location startLine="42" startOffset="12" endLine="51" endOffset="44"/></Target><Target id="@+id/textViewPdfSize" view="TextView"><Expressions/><location startLine="59" startOffset="16" endLine="65" endOffset="74"/></Target><Target id="@+id/textViewPdfPages" view="TextView"><Expressions/><location startLine="67" startOffset="16" endLine="74" endOffset="54"/></Target><Target id="@+id/textViewPdfProgress" view="TextView"><Expressions/><location startLine="78" startOffset="12" endLine="86" endOffset="43"/></Target><Target id="@+id/progressBarReading" view="ProgressBar"><Expressions/><location startLine="88" startOffset="12" endLine="94" endOffset="43"/></Target><Target id="@+id/imageViewFavorite" view="ImageView"><Expressions/><location startLine="99" startOffset="8" endLine="107" endOffset="35"/></Target></Targets></Layout>