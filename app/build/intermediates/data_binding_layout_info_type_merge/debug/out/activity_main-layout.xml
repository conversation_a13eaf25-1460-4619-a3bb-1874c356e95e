<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.ccppdfreader.app" filePath="app/src/main/res/layout/activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="195" endOffset="53"/></Target><Target id="@+id/app_bar_layout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="11" startOffset="4" endLine="27" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="17" startOffset="8" endLine="25" endOffset="57"/></Target><Target id="@+id/btn_open_pdf" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="93" startOffset="16" endLine="102" endOffset="63"/></Target><Target id="@+id/btn_browse_files" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="104" startOffset="16" endLine="113" endOffset="72"/></Target><Target id="@+id/recycler_recent_files" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="126" startOffset="12" endLine="132" endOffset="37"/></Target><Target id="@+id/layout_empty_recent" view="LinearLayout"><Expressions/><location startLine="135" startOffset="12" endLine="168" endOffset="26"/></Target><Target id="@+id/fab_open_pdf" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="175" startOffset="4" endLine="183" endOffset="41"/></Target><Target id="@+id/bottom_navigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="186" startOffset="4" endLine="193" endOffset="62"/></Target></Targets></Layout>