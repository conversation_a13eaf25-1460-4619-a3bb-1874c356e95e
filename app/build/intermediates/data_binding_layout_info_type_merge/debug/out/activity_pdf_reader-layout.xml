<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_pdf_reader" modulePackage="com.ccppdfreader.app" filePath="app/src/main/res/layout/activity_pdf_reader.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_pdf_reader_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="76" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="16" endOffset="42"/></Target><Target id="@+id/layoutPageInfo" view="LinearLayout"><Expressions/><location startLine="19" startOffset="4" endLine="46" endOffset="18"/></Target><Target id="@+id/textViewPageInfo" view="TextView"><Expressions/><location startLine="28" startOffset="8" endLine="35" endOffset="41"/></Target><Target id="@+id/textViewProgress" view="TextView"><Expressions/><location startLine="37" startOffset="8" endLine="44" endOffset="46"/></Target><Target id="@+id/pdfViewContainer" view="LinearLayout"><Expressions/><location startLine="56" startOffset="8" endLine="61" endOffset="42"/></Target><Target id="@+id/textViewPdfPlaceholder" view="TextView"><Expressions/><location startLine="64" startOffset="8" endLine="72" endOffset="39"/></Target></Targets></Layout>