<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.ccppdfreader.app.debug"
    android:versionCode="1"
    android:versionName="1.0.0-debug" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34" />

    <!-- Permissions for file access -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_DOCUMENTS" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <!-- Internet permission for potential cloud features -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <permission
        android:name="com.ccppdfreader.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.ccppdfreader.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.ccppdfreader.app.CCPPDFReaderApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:theme="@style/Theme.CCPPDFReader.Splash" >

        <!-- Splash Activity -->
        <activity
            android:name="com.ccppdfreader.app.activities.SplashActivity"
            android:exported="true"
            android:theme="@style/Theme.CCPPDFReader" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Main Activity -->
        <activity
            android:name="com.ccppdfreader.app.activities.MainActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="false"
            android:theme="@style/Theme.CCPPDFReader"
            android:windowSoftInputMode="adjustResize" />

        <!-- PDF Reader Activity -->
        <activity
            android:name="com.ccppdfreader.app.activities.PDFReaderActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true"
            android:theme="@style/Theme.CCPPDFReader.FullScreen"
            android:windowSoftInputMode="adjustResize" >

            <!-- Intent filters for opening PDF files -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:mimeType="application/pdf" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="file" />
                <data android:pathPattern=".*\\.pdf" />
            </intent-filter>
        </activity>

        <!-- Settings Activity -->
        <activity
            android:name="com.ccppdfreader.app.activities.SettingsActivity"
            android:exported="false"
            android:parentActivityName="com.ccppdfreader.app.activities.MainActivity"
            android:theme="@style/Theme.CCPPDFReader" />

        <!-- File Explorer Activity -->
        <activity
            android:name="com.ccppdfreader.app.activities.FileExplorerActivity"
            android:exported="false"
            android:parentActivityName="com.ccppdfreader.app.activities.MainActivity"
            android:theme="@style/Theme.CCPPDFReader" />

        <!-- Bookmarks Activity -->
        <activity
            android:name="com.ccppdfreader.app.activities.BookmarksActivity"
            android:exported="false"
            android:parentActivityName="com.ccppdfreader.app.activities.MainActivity"
            android:theme="@style/Theme.CCPPDFReader" />

        <!-- File Provider for sharing files -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.ccppdfreader.app.debug.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.ccppdfreader.app.debug.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
        </provider>
    </application>

</manifest>