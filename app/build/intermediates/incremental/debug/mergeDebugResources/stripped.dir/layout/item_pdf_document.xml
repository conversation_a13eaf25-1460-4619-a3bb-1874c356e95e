<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- PDF Icon -->
        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_pdf"
            android:layout_marginEnd="16dp"
            android:layout_gravity="center_vertical" />

        <!-- Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textViewPdfTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="PDF Document Title"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="?android:attr/textColorPrimary"
                android:maxLines="2"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/textViewPdfPath"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="/path/to/document.pdf"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary"
                android:layout_marginTop="4dp"
                android:maxLines="1"
                android:ellipsize="middle" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">

                <TextView
                    android:id="@+id/textViewPdfSize"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="1.2 MB"
                    android:textSize="12sp"
                    android:textColor="?android:attr/textColorSecondary" />

                <TextView
                    android:id="@+id/textViewPdfPages"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="• 25 pages"
                    android:textSize="12sp"
                    android:textColor="?android:attr/textColorSecondary"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/textViewPdfProgress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="75% complete"
                android:textSize="12sp"
                android:textColor="?attr/colorPrimary"
                android:layout_marginTop="4dp"
                android:visibility="gone" />

            <ProgressBar
                android:id="@+id/progressBarReading"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="4dp"
                android:layout_marginTop="4dp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Favorite Icon -->
        <ImageView
            android:id="@+id/imageViewFavorite"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@android:drawable/btn_star_big_off"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
