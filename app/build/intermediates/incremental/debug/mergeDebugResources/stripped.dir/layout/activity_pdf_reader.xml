<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#FFFFFF"
    tools:context=".activities.PDFReaderActivity">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="#2196F3"
        android:title="PDF Reader"
        android:titleTextColor="#FFFFFF" />

    <!-- Page info bar -->
    <LinearLayout
        android:id="@+id/layoutPageInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="#F5F5F5"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/textViewPageInfo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Page 1 of 10"
            android:textSize="14sp"
            android:textColor="#333333" />

        <TextView
            android:id="@+id/textViewProgress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="10%"
            android:textSize="12sp"
            android:textColor="#666666"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- PDF Content Area -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#FFFFFF">

        <!-- PDF View Container -->
        <LinearLayout
            android:id="@+id/pdfViewContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:background="#FFFFFF" />

        <!-- Fallback placeholder (hidden by default) -->
        <TextView
            android:id="@+id/textViewPdfPlaceholder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="Loading PDF..."
            android:textSize="16sp"
            android:textColor="#666666"
            android:visibility="gone" />

    </FrameLayout>

</LinearLayout>
