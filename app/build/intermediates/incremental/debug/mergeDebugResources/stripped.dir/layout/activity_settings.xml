<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="@dimen/padding_medium">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Settings"
        android:textAppearance="@style/TextAppearance.CCPPDFReader.Headline2"
        android:layout_marginBottom="@dimen/margin_large" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Settings functionality will be implemented in the next phase."
        android:textAppearance="@style/TextAppearance.CCPPDFReader.Body1"
        android:textColor="?attr/colorOnSurfaceVariant" />

</LinearLayout>
