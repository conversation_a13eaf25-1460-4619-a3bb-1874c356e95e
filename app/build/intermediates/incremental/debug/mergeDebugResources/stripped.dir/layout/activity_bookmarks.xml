<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activities.BookmarksActivity">

    <!-- App Bar Layout -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="Bookmarks"
            app:titleTextAppearance="@style/TextAppearance.CCPPDFReader.Headline2"
            app:menu="@menu/bookmarks_menu" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- Bookmarks RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_bookmarks"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingBottom="@dimen/padding_medium"
            tools:listitem="@layout/item_bookmark"
            tools:itemCount="3"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/layout_empty"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="@dimen/padding_extra_large"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:visibility="visible">

            <ImageView
                android:layout_width="@dimen/icon_size_extra_large"
                android:layout_height="@dimen/icon_size_extra_large"
                android:src="@drawable/ic_bookmark"
                android:layout_marginBottom="@dimen/margin_medium"
                app:tint="?attr/colorOnSurfaceVariant"
                android:alpha="0.6" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="No bookmarks yet"
                android:textAppearance="@style/TextAppearance.CCPPDFReader.Body1"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:layout_marginBottom="@dimen/margin_small" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Bookmarks you create will appear here"
                android:textAppearance="@style/TextAppearance.CCPPDFReader.Body2"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:textAlignment="center" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
