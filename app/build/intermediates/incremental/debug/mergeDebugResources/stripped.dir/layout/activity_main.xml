<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".activities.MainActivity">

    <!-- App Bar Layout -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="@string/app_name"
            app:titleTextAppearance="@style/TextAppearance.CCPPDFReader.Headline2"
            app:menu="@menu/main_menu"
            app:navigationIcon="@drawable/ic_menu"
            app:layout_scrollFlags="scroll|enterAlways" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/padding_medium">

            <!-- Welcome Section -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/margin_medium"
                app:cardElevation="@dimen/card_elevation"
                app:cardCornerRadius="@dimen/card_corner_radius"
                style="@style/Widget.CCPPDFReader.CardView">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/padding_large">

                    <ImageView
                        android:layout_width="@dimen/icon_size_extra_large"
                        android:layout_height="@dimen/icon_size_extra_large"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginBottom="@dimen/margin_medium"
                        android:src="@drawable/ic_pdf_document"
                        android:contentDescription="@string/app_name"
                        app:tint="?attr/colorPrimary" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/welcome_title"
                        android:textAppearance="@style/TextAppearance.CCPPDFReader.Headline2"
                        android:textAlignment="center"
                        android:layout_marginBottom="@dimen/margin_small" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/welcome_subtitle"
                        android:textAppearance="@style/TextAppearance.CCPPDFReader.Body1"
                        android:textAlignment="center"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="@dimen/margin_large"
                android:gravity="center">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_open_pdf"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="@dimen/margin_small"
                    android:text="@string/open_pdf"
                    android:drawableTop="@drawable/ic_folder_open"
                    android:drawablePadding="@dimen/spacing_sm"
                    style="@style/Widget.CCPPDFReader.Button" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_browse_files"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="@dimen/margin_small"
                    android:text="@string/browse_files"
                    android:drawableTop="@drawable/ic_storage"
                    android:drawablePadding="@dimen/spacing_sm"
                    style="@style/Widget.CCPPDFReader.Button.Outlined" />

            </LinearLayout>

            <!-- Recent Files Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/recent_files"
                android:textAppearance="@style/TextAppearance.CCPPDFReader.Headline2"
                android:layout_marginBottom="@dimen/margin_medium" />

            <!-- Recent Files RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_recent_files"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/item_pdf_document"
                tools:itemCount="3" />

            <!-- Empty State for Recent Files -->
            <LinearLayout
                android:id="@+id/layout_empty_recent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="@dimen/padding_extra_large"
                android:visibility="gone">

                <ImageView
                    android:layout_width="@dimen/icon_size_extra_large"
                    android:layout_height="@dimen/icon_size_extra_large"
                    android:src="@drawable/ic_document_empty"
                    android:layout_marginBottom="@dimen/margin_medium"
                    app:tint="?attr/colorOnSurfaceVariant"
                    android:alpha="0.6" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/no_recent_files"
                    android:textAppearance="@style/TextAppearance.CCPPDFReader.Body1"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:layout_marginBottom="@dimen/margin_small" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/no_recent_files_desc"
                    android:textAppearance="@style/TextAppearance.CCPPDFReader.Body2"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:textAlignment="center" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_open_pdf"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="@dimen/fab_margin"
        android:src="@drawable/ic_add"
        android:contentDescription="@string/open_pdf"
        app:tint="?attr/colorOnPrimary" />

    <!-- Bottom Navigation -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        app:menu="@menu/bottom_navigation_menu"
        app:labelVisibilityMode="labeled"
        style="@style/Widget.Material3.BottomNavigationView" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
