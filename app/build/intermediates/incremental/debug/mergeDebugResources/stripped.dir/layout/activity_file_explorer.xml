<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activities.FileExplorerActivity">

    <!-- App Bar Layout -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="Browse Files"
            app:titleTextAppearance="@style/TextAppearance.CCPPDFReader.Headline2"
            app:menu="@menu/file_explorer_menu" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- Files RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_files"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingBottom="@dimen/padding_medium"
            tools:listitem="@layout/item_file"
            tools:itemCount="5"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Loading Indicator -->
        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/progress_loading"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:visibility="gone"
            app:indicatorColor="?attr/colorPrimary"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:visibility="visible" />

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/layout_empty"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="@dimen/padding_extra_large"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            tools:visibility="visible">

            <ImageView
                android:layout_width="@dimen/icon_size_extra_large"
                android:layout_height="@dimen/icon_size_extra_large"
                android:src="@drawable/ic_folder_empty"
                android:layout_marginBottom="@dimen/margin_medium"
                app:tint="?attr/colorOnSurfaceVariant"
                android:alpha="0.6" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="No files found"
                android:textAppearance="@style/TextAppearance.CCPPDFReader.Body1"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:layout_marginBottom="@dimen/margin_small" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="This folder is empty or contains no PDF files"
                android:textAppearance="@style/TextAppearance.CCPPDFReader.Body2"
                android:textColor="?attr/colorOnSurfaceVariant"
                android:textAlignment="center" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
