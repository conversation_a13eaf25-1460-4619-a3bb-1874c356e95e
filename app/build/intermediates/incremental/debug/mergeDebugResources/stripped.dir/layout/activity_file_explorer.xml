<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".activities.FileExplorerActivity">

    <!-- Simple Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:title="@string/file_explorer"
        android:titleTextColor="@color/white" />

    <!-- Path Display -->
    <TextView
        android:id="@+id/textViewCurrentPath"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp"
        android:background="@color/teal_200"
        android:textColor="@color/black"
        android:textSize="14sp" />

    <!-- Main Content -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- RecyclerView for files -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewFiles"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="8dp" />

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/emptyStateLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="32dp"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/no_files_found"
                android:textSize="16sp"
                android:gravity="center" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
