<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="black">#FF000000</color>
    <color name="md_theme_dark_background">#1A1C1E</color>
    <color name="md_theme_dark_error">#FFB4AB</color>
    <color name="md_theme_dark_errorContainer">#93000A</color>
    <color name="md_theme_dark_inverseOnSurface">#1A1C1E</color>
    <color name="md_theme_dark_inversePrimary">#1976D2</color>
    <color name="md_theme_dark_inverseSurface">#E2E2E6</color>
    <color name="md_theme_dark_onBackground">#E2E2E6</color>
    <color name="md_theme_dark_onError">#690005</color>
    <color name="md_theme_dark_onErrorContainer">#FFDAD6</color>
    <color name="md_theme_dark_onPrimary">#003258</color>
    <color name="md_theme_dark_onPrimaryContainer">#D1E4FF</color>
    <color name="md_theme_dark_onSecondary">#253140</color>
    <color name="md_theme_dark_onSecondaryContainer">#D7E3F7</color>
    <color name="md_theme_dark_onSurface">#E2E2E6</color>
    <color name="md_theme_dark_onSurfaceVariant">#C3C7CF</color>
    <color name="md_theme_dark_onTertiary">#3A2948</color>
    <color name="md_theme_dark_onTertiaryContainer">#F2DAFF</color>
    <color name="md_theme_dark_outline">#8D9199</color>
    <color name="md_theme_dark_primary">#9ECAFF</color>
    <color name="md_theme_dark_primaryContainer">#00497D</color>
    <color name="md_theme_dark_secondary">#BBC7DB</color>
    <color name="md_theme_dark_secondaryContainer">#3B4858</color>
    <color name="md_theme_dark_surface">#1A1C1E</color>
    <color name="md_theme_dark_surfaceVariant">#43474E</color>
    <color name="md_theme_dark_tertiary">#D5BEE4</color>
    <color name="md_theme_dark_tertiaryContainer">#52426A</color>
    <color name="md_theme_light_background">#FDFCFF</color>
    <color name="md_theme_light_error">#BA1A1A</color>
    <color name="md_theme_light_errorContainer">#FFDAD6</color>
    <color name="md_theme_light_inverseOnSurface">#F1F0F4</color>
    <color name="md_theme_light_inversePrimary">#9ECAFF</color>
    <color name="md_theme_light_inverseSurface">#2F3033</color>
    <color name="md_theme_light_onBackground">#1A1C1E</color>
    <color name="md_theme_light_onError">#FFFFFF</color>
    <color name="md_theme_light_onErrorContainer">#410002</color>
    <color name="md_theme_light_onPrimary">#FFFFFF</color>
    <color name="md_theme_light_onPrimaryContainer">#001D36</color>
    <color name="md_theme_light_onSecondary">#FFFFFF</color>
    <color name="md_theme_light_onSecondaryContainer">#101C2B</color>
    <color name="md_theme_light_onSurface">#1A1C1E</color>
    <color name="md_theme_light_onSurfaceVariant">#43474E</color>
    <color name="md_theme_light_onTertiary">#FFFFFF</color>
    <color name="md_theme_light_onTertiaryContainer">#251431</color>
    <color name="md_theme_light_outline">#73777F</color>
    <color name="md_theme_light_primary">#1976D2</color>
    <color name="md_theme_light_primaryContainer">#D1E4FF</color>
    <color name="md_theme_light_secondary">#535F70</color>
    <color name="md_theme_light_secondaryContainer">#D7E3F7</color>
    <color name="md_theme_light_surface">#FDFCFF</color>
    <color name="md_theme_light_surfaceVariant">#DFE2EB</color>
    <color name="md_theme_light_tertiary">#6B5B92</color>
    <color name="md_theme_light_tertiaryContainer">#F2DAFF</color>
    <color name="pdf_background">#F5F5F5</color>
    <color name="pdf_background_dark">#121212</color>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="search_highlight">#FFFF00</color>
    <color name="search_highlight_current">#FF9800</color>
    <color name="splash_background">#1976D2</color>
    <color name="splash_icon_tint">#FFFFFF</color>
    <color name="status_bar_dark">#0D47A1</color>
    <color name="status_bar_light">#E3F2FD</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="toolbar_overlay">#80000000</color>
    <color name="white">#FFFFFFFF</color>
    <dimen name="app_bar_height">56dp</dimen>
    <dimen name="bottom_nav_height">56dp</dimen>
    <dimen name="button_corner_radius">12dp</dimen>
    <dimen name="button_height">48dp</dimen>
    <dimen name="card_corner_radius">12dp</dimen>
    <dimen name="card_elevation">4dp</dimen>
    <dimen name="container_padding">16dp</dimen>
    <dimen name="desktop_breakpoint">840dp</dimen>
    <dimen name="drawer_width">280dp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <dimen name="icon_size_extra_large">48dp</dimen>
    <dimen name="icon_size_large">32dp</dimen>
    <dimen name="icon_size_medium">24dp</dimen>
    <dimen name="icon_size_small">16dp</dimen>
    <dimen name="list_item_height">72dp</dimen>
    <dimen name="list_item_height_small">56dp</dimen>
    <dimen name="list_item_padding_horizontal">16dp</dimen>
    <dimen name="list_item_padding_vertical">12dp</dimen>
    <dimen name="margin_extra_large">32dp</dimen>
    <dimen name="margin_large">24dp</dimen>
    <dimen name="margin_medium">16dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="padding_extra_large">32dp</dimen>
    <dimen name="padding_large">24dp</dimen>
    <dimen name="padding_medium">16dp</dimen>
    <dimen name="padding_small">8dp</dimen>
    <dimen name="pdf_bottom_bar_height">56dp</dimen>
    <dimen name="pdf_page_margin">8dp</dimen>
    <dimen name="pdf_toolbar_height">56dp</dimen>
    <dimen name="search_bar_height">48dp</dimen>
    <dimen name="spacing_lg">24dp</dimen>
    <dimen name="spacing_md">16dp</dimen>
    <dimen name="spacing_sm">8dp</dimen>
    <dimen name="spacing_xl">32dp</dimen>
    <dimen name="spacing_xs">4dp</dimen>
    <dimen name="spacing_xxl">48dp</dimen>
    <dimen name="splash_logo_size">120dp</dimen>
    <dimen name="tablet_breakpoint">600dp</dimen>
    <dimen name="text_size_body_large">16sp</dimen>
    <dimen name="text_size_body_medium">14sp</dimen>
    <dimen name="text_size_body_small">12sp</dimen>
    <dimen name="text_size_headline_large">32sp</dimen>
    <dimen name="text_size_headline_medium">24sp</dimen>
    <dimen name="text_size_headline_small">20sp</dimen>
    <dimen name="text_size_label_large">14sp</dimen>
    <dimen name="text_size_label_medium">12sp</dimen>
    <dimen name="text_size_label_small">10sp</dimen>
    <dimen name="text_size_title_large">18sp</dimen>
    <dimen name="text_size_title_medium">16sp</dimen>
    <dimen name="text_size_title_small">14sp</dimen>
    <dimen name="toolbar_elevation">4dp</dimen>
    <string name="action_bookmark">Bookmark</string>
    <string name="action_close">Close</string>
    <string name="action_refresh">Refresh</string>
    <string name="action_search">Search</string>
    <string name="action_settings">Settings</string>
    <string name="action_share">Share</string>
    <string name="add_to_favorites">Add to Favorites</string>
    <string name="app_description">Professional PDF Reader with Modern Design</string>
    <string name="app_name">CCP PDF Reader</string>
    <string name="browse_files">Browse Files</string>
    <string name="cancel">Cancel</string>
    <string name="error">Error</string>
    <string name="error_file_access">Cannot access file</string>
    <string name="error_generic">Something went wrong</string>
    <string name="error_network">Network error</string>
    <string name="error_permission_denied">Permission denied</string>
    <string name="file_cannot_open">Cannot open this file</string>
    <string name="file_corrupted">File appears to be corrupted</string>
    <string name="file_not_found">File not found</string>
    <string name="loading">Loading…</string>
    <string name="loading_document">Loading document…</string>
    <string name="nav_favorites">Favorites</string>
    <string name="nav_home">Home</string>
    <string name="nav_recent">Recent</string>
    <string name="nav_settings">Settings</string>
    <string name="no">No</string>
    <string name="no_recent_files">No recent files</string>
    <string name="no_recent_files_desc">PDFs you open will appear here</string>
    <string name="no_search_results">No results found</string>
    <string name="ok">OK</string>
    <string name="open_pdf">Open PDF</string>
    <string name="page_indicator">Page %1$d of %2$d</string>
    <string name="permission_deny">Deny</string>
    <string name="permission_grant">Grant Permission</string>
    <string name="permission_storage_message">This app needs storage permission to access PDF files on your device.</string>
    <string name="permission_storage_title">Storage Permission Required</string>
    <string name="recent_files">Recent Files</string>
    <string name="remove_from_favorites">Remove from Favorites</string>
    <string name="retry">Retry</string>
    <string name="search_hint">Search in document</string>
    <string name="search_results">%1$d results found</string>
    <string name="settings_about">About</string>
    <string name="settings_appearance">Appearance</string>
    <string name="settings_reading">Reading</string>
    <string name="settings_theme">Theme</string>
    <string name="share_pdf">Share PDF</string>
    <string name="success">Success</string>
    <string name="theme_dark">Dark</string>
    <string name="theme_light">Light</string>
    <string name="theme_system">System Default</string>
    <string name="welcome_subtitle">Open and read PDF documents with ease</string>
    <string name="welcome_title">Welcome to CCP PDF Reader</string>
    <string name="yes">Yes</string>
    <string name="zoom_level">Zoom: %1$d%%</string>
    <style name="Theme.CCPPDFReader" parent="Theme.AppCompat.DayNight">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
        
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
</resources>