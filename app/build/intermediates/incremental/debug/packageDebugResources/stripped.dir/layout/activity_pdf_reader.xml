<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".activities.PDFReaderActivity">

    <!-- PDF View -->
    <com.github.barteksc.pdfviewer.PDFView
        android:id="@+id/pdf_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/colorSurface" />

    <!-- Top Toolbar (Initially hidden) -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/toolbar_overlay"
        android:visibility="gone"
        tools:visibility="visible">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:titleTextColor="@android:color/white"
            app:navigationIconTint="@android:color/white"
            app:menu="@menu/pdf_reader_menu"
            tools:title="Sample Document.pdf" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Bottom Controls (Initially hidden) -->
    <LinearLayout
        android:id="@+id/bottom_controls"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:orientation="vertical"
        android:background="@color/toolbar_overlay"
        android:padding="@dimen/padding_medium"
        android:visibility="gone"
        tools:visibility="visible">

        <!-- Page Navigation -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="@dimen/margin_small">

            <ImageButton
                android:id="@+id/btn_previous_page"
                android:layout_width="@dimen/button_height"
                android:layout_height="@dimen/button_height"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_chevron_left"
                android:contentDescription="Previous page"
                app:tint="@android:color/white" />

            <TextView
                android:id="@+id/tv_page_indicator"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@android:color/white"
                android:textAlignment="center"
                android:textAppearance="@style/TextAppearance.CCPPDFReader.Body1"
                tools:text="Page 5 of 24" />

            <ImageButton
                android:id="@+id/btn_next_page"
                android:layout_width="@dimen/button_height"
                android:layout_height="@dimen/button_height"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_chevron_right"
                android:contentDescription="Next page"
                app:tint="@android:color/white" />

        </LinearLayout>

        <!-- Page Slider -->
        <com.google.android.material.slider.Slider
            android:id="@+id/slider_page"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:valueFrom="1"
            android:valueTo="100"
            android:stepSize="1"
            app:thumbColor="@android:color/white"
            app:trackColorActive="@android:color/white"
            app:trackColorInactive="#80FFFFFF"
            tools:value="20" />

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginTop="@dimen/margin_small">

            <ImageButton
                android:id="@+id/btn_zoom_out"
                android:layout_width="@dimen/button_height"
                android:layout_height="@dimen/button_height"
                android:layout_marginEnd="@dimen/margin_medium"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_zoom_out"
                android:contentDescription="Zoom out"
                app:tint="@android:color/white" />

            <TextView
                android:id="@+id/tv_zoom_level"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_medium"
                android:textColor="@android:color/white"
                android:textAppearance="@style/TextAppearance.CCPPDFReader.Body2"
                android:minWidth="60dp"
                android:textAlignment="center"
                tools:text="100%" />

            <ImageButton
                android:id="@+id/btn_zoom_in"
                android:layout_width="@dimen/button_height"
                android:layout_height="@dimen/button_height"
                android:layout_marginStart="@dimen/margin_medium"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_zoom_in"
                android:contentDescription="Zoom in"
                app:tint="@android:color/white" />

        </LinearLayout>

    </LinearLayout>

    <!-- Search Bar (Initially hidden) -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/search_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/margin_medium"
        android:visibility="gone"
        app:cardElevation="@dimen/card_elevation"
        app:cardCornerRadius="@dimen/card_corner_radius"
        tools:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="@dimen/padding_small"
            android:gravity="center_vertical">

            <EditText
                android:id="@+id/et_search"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="@string/search_hint"
                android:background="@null"
                android:padding="@dimen/padding_small"
                android:textAppearance="@style/TextAppearance.CCPPDFReader.Body1"
                android:imeOptions="actionSearch"
                android:inputType="text" />

            <ImageButton
                android:id="@+id/btn_search_previous"
                android:layout_width="@dimen/icon_size_medium"
                android:layout_height="@dimen/icon_size_medium"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_chevron_up"
                android:contentDescription="Previous result" />

            <ImageButton
                android:id="@+id/btn_search_next"
                android:layout_width="@dimen/icon_size_medium"
                android:layout_height="@dimen/icon_size_medium"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_chevron_down"
                android:contentDescription="Next result" />

            <ImageButton
                android:id="@+id/btn_close_search"
                android:layout_width="@dimen/icon_size_medium"
                android:layout_height="@dimen/icon_size_medium"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_close"
                android:contentDescription="Close search" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Loading Indicator -->
    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/progress_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:indeterminate="true"
        app:indicatorColor="?attr/colorPrimary"
        android:visibility="gone"
        tools:visibility="visible" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
