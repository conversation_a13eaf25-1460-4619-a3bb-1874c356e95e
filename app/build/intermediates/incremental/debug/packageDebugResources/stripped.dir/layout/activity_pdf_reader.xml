<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".activities.PDFReaderActivity">

    <!-- Simple Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:title="PDF Reader"
        android:titleTextColor="@color/white" />

    <!-- PDF Content Area -->
    <FrameLayout
        android:id="@+id/pdfContainer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/white">

        <TextView
            android:id="@+id/textViewPdfPlaceholder"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="PDF content will be displayed here"
            android:textSize="16sp" />

    </FrameLayout>

</LinearLayout>
