<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/margin_small"
    android:layout_marginVertical="@dimen/margin_small"
    android:clickable="true"
    android:focusable="true"
    app:cardElevation="2dp"
    app:cardCornerRadius="@dimen/card_corner_radius"
    app:rippleColor="?attr/colorPrimary"
    style="@style/Widget.CCPPDFReader.CardView">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/padding_medium">

        <!-- Bookmark Icon -->
        <ImageView
            android:id="@+id/iv_bookmark_icon"
            android:layout_width="@dimen/icon_size_large"
            android:layout_height="@dimen/icon_size_large"
            android:src="@drawable/ic_bookmark"
            android:contentDescription="Bookmark"
            app:tint="?attr/colorPrimary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Bookmark Title -->
        <TextView
            android:id="@+id/tv_bookmark_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginEnd="@dimen/margin_small"
            android:textAppearance="@style/TextAppearance.CCPPDFReader.Body1"
            android:textColor="?attr/colorOnSurface"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/iv_bookmark_icon"
            app:layout_constraintEnd_toStartOf="@id/btn_more_options"
            app:layout_constraintTop_toTopOf="@id/iv_bookmark_icon"
            tools:text="Page 15 - Important Section" />

        <!-- File Name -->
        <TextView
            android:id="@+id/tv_file_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginTop="@dimen/margin_xs"
            android:textAppearance="@style/TextAppearance.CCPPDFReader.Body2"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/iv_bookmark_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_bookmark_title"
            tools:text="Sample Document.pdf" />

        <!-- Page Number and Date -->
        <TextView
            android:id="@+id/tv_bookmark_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginTop="@dimen/margin_xs"
            android:textAppearance="@style/TextAppearance.CCPPDFReader.Body2"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/iv_bookmark_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_file_name"
            tools:text="Page 15 • Mar 15, 2024 14:30" />

        <!-- More Options Button -->
        <ImageButton
            android:id="@+id/btn_more_options"
            android:layout_width="@dimen/icon_size_medium"
            android:layout_height="@dimen/icon_size_medium"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_more_vert"
            android:contentDescription="More options"
            app:tint="?attr/colorOnSurfaceVariant"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
