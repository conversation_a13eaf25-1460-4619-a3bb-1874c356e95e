1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ccppdfreader.app.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions for file access -->
12    <uses-permission
12-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:6:5-7:38
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:6:22-77
14        android:maxSdkVersion="32" />
14-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:7:9-35
15    <uses-permission android:name="android.permission.READ_MEDIA_DOCUMENTS" />
15-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:8:5-79
15-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:8:22-76
16    <uses-permission
16-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:9:5-11:40
17        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
17-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:9:22-78
18        android:maxSdkVersion="28" />
18-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:10:9-35
19
20    <!-- Internet permission for potential cloud features -->
21    <uses-permission android:name="android.permission.INTERNET" />
21-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:14:5-67
21-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:14:22-64
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:15:5-79
22-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:15:22-76
23
24    <permission
24-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
25        android:name="com.ccppdfreader.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
25-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
26        android:protectionLevel="signature" />
26-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
27
28    <uses-permission android:name="com.ccppdfreader.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
28-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
28-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
29
30    <application
30-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:17:5-104:19
31        android:name="com.ccppdfreader.app.CCPPDFReaderApplication"
31-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:18:9-48
32        android:allowBackup="true"
32-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:19:9-35
33        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
33-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
34        android:dataExtractionRules="@xml/data_extraction_rules"
34-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:20:9-65
35        android:debuggable="true"
36        android:extractNativeLibs="false"
37        android:fullBackupContent="@xml/backup_rules"
37-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:21:9-54
38        android:icon="@mipmap/ic_launcher"
38-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:22:9-43
39        android:label="@string/app_name"
39-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:23:9-41
40        android:requestLegacyExternalStorage="true"
40-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:26:9-52
41        android:roundIcon="@mipmap/ic_launcher_round"
41-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:24:9-54
42        android:theme="@style/Theme.CCPPDFReader.Splash" >
42-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:25:9-57
43
44        <!-- Splash Activity -->
45        <activity
45-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:30:9-38:20
46            android:name="com.ccppdfreader.app.activities.SplashActivity"
46-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:31:13-54
47            android:exported="true"
47-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:32:13-36
48            android:theme="@style/Theme.CCPPDFReader" >
48-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:33:13-54
49            <intent-filter>
49-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:34:13-37:29
50                <action android:name="android.intent.action.MAIN" />
50-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:35:17-69
50-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:35:25-66
51
52                <category android:name="android.intent.category.LAUNCHER" />
52-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:36:17-77
52-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:36:27-74
53            </intent-filter>
54        </activity>
55
56        <!-- Main Activity -->
57        <activity
57-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:41:9-46:58
58            android:name="com.ccppdfreader.app.activities.MainActivity"
58-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:42:13-52
59            android:configChanges="orientation|screenSize|keyboardHidden"
59-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:45:13-74
60            android:exported="false"
60-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:43:13-37
61            android:theme="@style/Theme.CCPPDFReader"
61-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:44:13-54
62            android:windowSoftInputMode="adjustResize" />
62-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:46:13-55
63
64        <!-- PDF Reader Activity -->
65        <activity
65-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:49:9-70:20
66            android:name="com.ccppdfreader.app.activities.PDFReaderActivity"
66-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:50:13-57
67            android:configChanges="orientation|screenSize|keyboardHidden"
67-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:53:13-74
68            android:exported="true"
68-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:51:13-36
69            android:theme="@style/Theme.CCPPDFReader.FullScreen"
69-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:52:13-65
70            android:windowSoftInputMode="adjustResize" >
70-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:54:13-55
71
72            <!-- Intent filters for opening PDF files -->
73            <intent-filter>
73-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:57:13-62:29
74                <action android:name="android.intent.action.VIEW" />
74-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:58:17-69
74-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:58:25-66
75
76                <category android:name="android.intent.category.DEFAULT" />
76-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:59:17-76
76-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:59:27-73
77                <category android:name="android.intent.category.BROWSABLE" />
77-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:60:17-78
77-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:60:27-75
78
79                <data android:mimeType="application/pdf" />
79-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:61:17-60
79-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:61:23-57
80            </intent-filter>
81            <intent-filter>
81-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:64:13-69:29
82                <action android:name="android.intent.action.VIEW" />
82-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:58:17-69
82-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:58:25-66
83
84                <category android:name="android.intent.category.DEFAULT" />
84-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:59:17-76
84-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:59:27-73
85
86                <data android:scheme="file" />
86-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:61:17-60
86-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:67:23-44
87                <data android:pathPattern=".*\\.pdf" />
87-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:61:17-60
87-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:68:23-53
88            </intent-filter>
89        </activity>
90
91        <!-- Settings Activity -->
92        <activity
92-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:73:9-77:69
93            android:name="com.ccppdfreader.app.activities.SettingsActivity"
93-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:74:13-56
94            android:exported="false"
94-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:75:13-37
95            android:parentActivityName="com.ccppdfreader.app.activities.MainActivity"
95-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:77:13-66
96            android:theme="@style/Theme.CCPPDFReader" />
96-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:76:13-54
97
98        <!-- File Explorer Activity -->
99        <activity
99-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:80:9-84:69
100            android:name="com.ccppdfreader.app.activities.FileExplorerActivity"
100-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:81:13-60
101            android:exported="false"
101-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:82:13-37
102            android:parentActivityName="com.ccppdfreader.app.activities.MainActivity"
102-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:84:13-66
103            android:theme="@style/Theme.CCPPDFReader" />
103-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:83:13-54
104
105        <!-- Bookmarks Activity -->
106        <activity
106-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:87:9-91:69
107            android:name="com.ccppdfreader.app.activities.BookmarksActivity"
107-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:88:13-57
108            android:exported="false"
108-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:89:13-37
109            android:parentActivityName="com.ccppdfreader.app.activities.MainActivity"
109-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:91:13-66
110            android:theme="@style/Theme.CCPPDFReader" />
110-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:90:13-54
111
112        <!-- File Provider for sharing files -->
113        <provider
114            android:name="androidx.core.content.FileProvider"
114-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:95:13-62
115            android:authorities="com.ccppdfreader.app.debug.fileprovider"
115-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:96:13-64
116            android:exported="false"
116-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:97:13-37
117            android:grantUriPermissions="true" >
117-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:98:13-47
118            <meta-data
118-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:99:13-101:54
119                android:name="android.support.FILE_PROVIDER_PATHS"
119-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:100:17-67
120                android:resource="@xml/file_paths" />
120-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:101:17-51
121        </provider>
122        <provider
122-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
123            android:name="androidx.startup.InitializationProvider"
123-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
124            android:authorities="com.ccppdfreader.app.debug.androidx-startup"
124-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
125            android:exported="false" >
125-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
126            <meta-data
126-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
127                android:name="androidx.emoji2.text.EmojiCompatInitializer"
127-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
128                android:value="androidx.startup" />
128-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
129            <meta-data
129-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1c8024321a02e22d07ef02d34edf55b3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
130                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
130-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1c8024321a02e22d07ef02d34edf55b3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
131                android:value="androidx.startup" />
131-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1c8024321a02e22d07ef02d34edf55b3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
132            <meta-data
132-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
133                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
133-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
134                android:value="androidx.startup" />
134-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
135        </provider>
136
137        <receiver
137-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
138            android:name="androidx.profileinstaller.ProfileInstallReceiver"
138-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
139            android:directBootAware="false"
139-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
140            android:enabled="true"
140-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
141            android:exported="true"
141-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
142            android:permission="android.permission.DUMP" >
142-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
143            <intent-filter>
143-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
144                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
144-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
144-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
145            </intent-filter>
146            <intent-filter>
146-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
147                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
147-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
147-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
148            </intent-filter>
149            <intent-filter>
149-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
150                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
150-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
150-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
151            </intent-filter>
152            <intent-filter>
152-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
153                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
153-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
153-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
154            </intent-filter>
155        </receiver>
156    </application>
157
158</manifest>
