1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ccppdfreader.app.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions for file access -->
12    <uses-permission
12-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:6:5-7:38
13        android:name="android.permission.READ_EXTERNAL_STORAGE"
13-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:6:22-77
14        android:maxSdkVersion="32" />
14-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:7:9-35
15    <uses-permission android:name="android.permission.READ_MEDIA_DOCUMENTS" />
15-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:8:5-79
15-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:8:22-76
16    <uses-permission
16-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:9:5-11:40
17        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
17-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:9:22-78
18        android:maxSdkVersion="28" />
18-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:10:9-35
19
20    <!-- Internet permission for potential cloud features -->
21    <uses-permission android:name="android.permission.INTERNET" />
21-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:14:5-67
21-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:14:22-64
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:15:5-79
22-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:15:22-76
23
24    <queries>
24-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:9:5-19:15
25
26        <!-- Query Camera Packages -->
27        <intent>
27-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:12:9-14:18
28            <action android:name="android.media.action.IMAGE_CAPTURE" />
28-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:13:13-73
28-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:13:21-70
29        </intent>
30        <!-- Query Open Document -->
31        <intent>
31-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:16:9-18:18
32            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
32-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:17:13-79
32-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:17:21-76
33        </intent>
34    </queries>
35
36    <permission
36-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
37        android:name="com.ccppdfreader.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
37-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
38        android:protectionLevel="signature" />
38-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
39
40    <uses-permission android:name="com.ccppdfreader.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
40-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
40-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
41
42    <application
42-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:17:5-104:19
43        android:name="com.ccppdfreader.app.CCPPDFReaderApplication"
43-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:18:9-48
44        android:allowBackup="true"
44-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:19:9-35
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
46        android:dataExtractionRules="@xml/data_extraction_rules"
46-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:20:9-65
47        android:debuggable="true"
48        android:extractNativeLibs="false"
49        android:fullBackupContent="@xml/backup_rules"
49-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:21:9-54
50        android:icon="@mipmap/ic_launcher"
50-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:22:9-43
51        android:label="@string/app_name"
51-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:23:9-41
52        android:requestLegacyExternalStorage="true"
52-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:26:9-52
53        android:roundIcon="@mipmap/ic_launcher_round"
53-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:24:9-54
54        android:theme="@style/Theme.CCPPDFReader.Splash" >
54-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:25:9-57
55
56        <!-- Splash Activity -->
57        <activity
57-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:30:9-38:20
58            android:name="com.ccppdfreader.app.activities.SplashActivity"
58-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:31:13-54
59            android:exported="true"
59-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:32:13-36
60            android:theme="@style/Theme.CCPPDFReader" >
60-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:33:13-54
61            <intent-filter>
61-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:34:13-37:29
62                <action android:name="android.intent.action.MAIN" />
62-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:35:17-69
62-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:35:25-66
63
64                <category android:name="android.intent.category.LAUNCHER" />
64-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:36:17-77
64-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:36:27-74
65            </intent-filter>
66        </activity>
67
68        <!-- Main Activity -->
69        <activity
69-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:41:9-46:58
70            android:name="com.ccppdfreader.app.activities.MainActivity"
70-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:42:13-52
71            android:configChanges="orientation|screenSize|keyboardHidden"
71-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:45:13-74
72            android:exported="false"
72-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:43:13-37
73            android:theme="@style/Theme.CCPPDFReader"
73-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:44:13-54
74            android:windowSoftInputMode="adjustResize" />
74-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:46:13-55
75
76        <!-- PDF Reader Activity -->
77        <activity
77-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:49:9-70:20
78            android:name="com.ccppdfreader.app.activities.PDFReaderActivity"
78-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:50:13-57
79            android:configChanges="orientation|screenSize|keyboardHidden"
79-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:53:13-74
80            android:exported="true"
80-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:51:13-36
81            android:theme="@style/Theme.CCPPDFReader.FullScreen"
81-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:52:13-65
82            android:windowSoftInputMode="adjustResize" >
82-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:54:13-55
83
84            <!-- Intent filters for opening PDF files -->
85            <intent-filter>
85-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:57:13-62:29
86                <action android:name="android.intent.action.VIEW" />
86-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:58:17-69
86-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:58:25-66
87
88                <category android:name="android.intent.category.DEFAULT" />
88-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:59:17-76
88-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:59:27-73
89                <category android:name="android.intent.category.BROWSABLE" />
89-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:60:17-78
89-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:60:27-75
90
91                <data android:mimeType="application/pdf" />
91-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:61:17-60
91-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:61:23-57
92            </intent-filter>
93            <intent-filter>
93-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:64:13-69:29
94                <action android:name="android.intent.action.VIEW" />
94-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:58:17-69
94-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:58:25-66
95
96                <category android:name="android.intent.category.DEFAULT" />
96-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:59:17-76
96-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:59:27-73
97
98                <data android:scheme="file" />
98-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:61:17-60
98-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:67:23-44
99                <data android:pathPattern=".*\\.pdf" />
99-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:61:17-60
99-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:68:23-53
100            </intent-filter>
101        </activity>
102
103        <!-- Settings Activity -->
104        <activity
104-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:73:9-77:69
105            android:name="com.ccppdfreader.app.activities.SettingsActivity"
105-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:74:13-56
106            android:exported="false"
106-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:75:13-37
107            android:parentActivityName="com.ccppdfreader.app.activities.MainActivity"
107-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:77:13-66
108            android:theme="@style/Theme.CCPPDFReader" />
108-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:76:13-54
109
110        <!-- File Explorer Activity -->
111        <activity
111-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:80:9-84:69
112            android:name="com.ccppdfreader.app.activities.FileExplorerActivity"
112-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:81:13-60
113            android:exported="false"
113-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:82:13-37
114            android:parentActivityName="com.ccppdfreader.app.activities.MainActivity"
114-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:84:13-66
115            android:theme="@style/Theme.CCPPDFReader" />
115-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:83:13-54
116
117        <!-- Bookmarks Activity -->
118        <activity
118-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:87:9-91:69
119            android:name="com.ccppdfreader.app.activities.BookmarksActivity"
119-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:88:13-57
120            android:exported="false"
120-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:89:13-37
121            android:parentActivityName="com.ccppdfreader.app.activities.MainActivity"
121-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:91:13-66
122            android:theme="@style/Theme.CCPPDFReader" />
122-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:90:13-54
123
124        <!-- File Provider for sharing files -->
125        <provider
126            android:name="androidx.core.content.FileProvider"
126-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:95:13-62
127            android:authorities="com.ccppdfreader.app.debug.fileprovider"
127-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:96:13-64
128            android:exported="false"
128-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:97:13-37
129            android:grantUriPermissions="true" >
129-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:98:13-47
130            <meta-data
130-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:99:13-101:54
131                android:name="android.support.FILE_PROVIDER_PATHS"
131-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:100:17-67
132                android:resource="@xml/file_paths" />
132-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:101:17-51
133        </provider>
134
135        <activity
135-->[com.karumi:dexter:6.2.3] /Users/<USER>/.gradle/caches/8.12/transforms/934452843d53e01b27a83951a2f5ce03/transformed/dexter-6.2.3/AndroidManifest.xml:27:9-29:72
136            android:name="com.karumi.dexter.DexterActivity"
136-->[com.karumi:dexter:6.2.3] /Users/<USER>/.gradle/caches/8.12/transforms/934452843d53e01b27a83951a2f5ce03/transformed/dexter-6.2.3/AndroidManifest.xml:28:13-60
137            android:theme="@style/Dexter.Internal.Theme.Transparent" />
137-->[com.karumi:dexter:6.2.3] /Users/<USER>/.gradle/caches/8.12/transforms/934452843d53e01b27a83951a2f5ce03/transformed/dexter-6.2.3/AndroidManifest.xml:29:13-69
138        <activity
138-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:22:9-25:68
139            android:name="com.github.dhaval2404.imagepicker.ImagePickerActivity"
139-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:23:13-81
140            android:screenOrientation="unspecified"
140-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:24:13-52
141            android:theme="@style/Theme.Transparent.ImagePicker" />
141-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:25:13-65
142        <activity
142-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:26:9-29:72
143            android:name="com.yalantis.ucrop.UCropActivity"
143-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:27:13-60
144            android:screenOrientation="portrait"
144-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:28:13-49
145            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
145-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:29:13-69
146
147        <provider
147-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:31:9-39:20
148            android:name="com.github.dhaval2404.imagepicker.ImagePickerFileProvider"
148-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:32:13-85
149            android:authorities="com.ccppdfreader.app.debug.imagepicker.provider"
149-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:33:13-72
150            android:exported="false"
150-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:34:13-37
151            android:grantUriPermissions="true" >
151-->[com.github.dhaval2404:imagepicker:2.1] /Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/AndroidManifest.xml:35:13-47
152            <meta-data
152-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:99:13-101:54
153                android:name="android.support.FILE_PROVIDER_PATHS"
153-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:100:17-67
154                android:resource="@xml/image_picker_provider_paths" />
154-->/Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:101:17-51
155        </provider>
156        <provider
156-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
157            android:name="androidx.startup.InitializationProvider"
157-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
158            android:authorities="com.ccppdfreader.app.debug.androidx-startup"
158-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
159            android:exported="false" >
159-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
160            <meta-data
160-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
161                android:name="androidx.emoji2.text.EmojiCompatInitializer"
161-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
162                android:value="androidx.startup" />
162-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
163            <meta-data
163-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1c8024321a02e22d07ef02d34edf55b3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
164                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
164-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1c8024321a02e22d07ef02d34edf55b3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
165                android:value="androidx.startup" />
165-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1c8024321a02e22d07ef02d34edf55b3/transformed/lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
166            <meta-data
166-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
167                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
167-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
168                android:value="androidx.startup" />
168-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
169        </provider>
170
171        <receiver
171-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
172            android:name="androidx.profileinstaller.ProfileInstallReceiver"
172-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
173            android:directBootAware="false"
173-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
174            android:enabled="true"
174-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
175            android:exported="true"
175-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
176            android:permission="android.permission.DUMP" >
176-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
177            <intent-filter>
177-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
178                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
178-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
178-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
179            </intent-filter>
180            <intent-filter>
180-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
181                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
181-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
181-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
182            </intent-filter>
183            <intent-filter>
183-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
184                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
184-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
184-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
185            </intent-filter>
186            <intent-filter>
186-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
187                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
187-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
187-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/9838c404f44017f30fde3995b3e2b463/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
188            </intent-filter>
189        </receiver>
190    </application>
191
192</manifest>
