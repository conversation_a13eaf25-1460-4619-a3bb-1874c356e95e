int anim abc_fade_in 0x7f010000
int anim abc_fade_out 0x7f010001
int anim abc_grow_fade_in_from_bottom 0x7f010002
int anim abc_popup_enter 0x7f010003
int anim abc_popup_exit 0x7f010004
int anim abc_shrink_fade_out_from_bottom 0x7f010005
int anim abc_slide_in_bottom 0x7f010006
int anim abc_slide_in_top 0x7f010007
int anim abc_slide_out_bottom 0x7f010008
int anim abc_slide_out_top 0x7f010009
int anim abc_tooltip_enter 0x7f01000a
int anim abc_tooltip_exit 0x7f01000b
int anim btn_checkbox_to_checked_box_inner_merged_animation 0x7f01000c
int anim btn_checkbox_to_checked_box_outer_merged_animation 0x7f01000d
int anim btn_checkbox_to_checked_icon_null_animation 0x7f01000e
int anim btn_checkbox_to_unchecked_box_inner_merged_animation 0x7f01000f
int anim btn_checkbox_to_unchecked_check_path_merged_animation 0x7f010010
int anim btn_checkbox_to_unchecked_icon_null_animation 0x7f010011
int anim btn_radio_to_off_mtrl_dot_group_animation 0x7f010012
int anim btn_radio_to_off_mtrl_ring_outer_animation 0x7f010013
int anim btn_radio_to_off_mtrl_ring_outer_path_animation 0x7f010014
int anim btn_radio_to_on_mtrl_dot_group_animation 0x7f010015
int anim btn_radio_to_on_mtrl_ring_outer_animation 0x7f010016
int anim btn_radio_to_on_mtrl_ring_outer_path_animation 0x7f010017
int anim fade_in 0x7f010018
int anim fade_out 0x7f010019
int anim fragment_fast_out_extra_slow_in 0x7f01001a
int anim scale_in 0x7f01001b
int anim slide_in_right 0x7f01001c
int anim slide_out_left 0x7f01001d
int animator fragment_close_enter 0x7f020000
int animator fragment_close_exit 0x7f020001
int animator fragment_fade_enter 0x7f020002
int animator fragment_fade_exit 0x7f020003
int animator fragment_open_enter 0x7f020004
int animator fragment_open_exit 0x7f020005
int attr SharedValue 0x7f030000
int attr SharedValueId 0x7f030001
int attr actionBarDivider 0x7f030002
int attr actionBarItemBackground 0x7f030003
int attr actionBarPopupTheme 0x7f030004
int attr actionBarSize 0x7f030005
int attr actionBarSplitStyle 0x7f030006
int attr actionBarStyle 0x7f030007
int attr actionBarTabBarStyle 0x7f030008
int attr actionBarTabStyle 0x7f030009
int attr actionBarTabTextStyle 0x7f03000a
int attr actionBarTheme 0x7f03000b
int attr actionBarWidgetTheme 0x7f03000c
int attr actionButtonStyle 0x7f03000d
int attr actionDropDownStyle 0x7f03000e
int attr actionLayout 0x7f03000f
int attr actionMenuTextAppearance 0x7f030010
int attr actionMenuTextColor 0x7f030011
int attr actionModeBackground 0x7f030012
int attr actionModeCloseButtonStyle 0x7f030013
int attr actionModeCloseContentDescription 0x7f030014
int attr actionModeCloseDrawable 0x7f030015
int attr actionModeCopyDrawable 0x7f030016
int attr actionModeCutDrawable 0x7f030017
int attr actionModeFindDrawable 0x7f030018
int attr actionModePasteDrawable 0x7f030019
int attr actionModePopupWindowStyle 0x7f03001a
int attr actionModeSelectAllDrawable 0x7f03001b
int attr actionModeShareDrawable 0x7f03001c
int attr actionModeSplitBackground 0x7f03001d
int attr actionModeStyle 0x7f03001e
int attr actionModeTheme 0x7f03001f
int attr actionModeWebSearchDrawable 0x7f030020
int attr actionOverflowButtonStyle 0x7f030021
int attr actionOverflowMenuStyle 0x7f030022
int attr actionProviderClass 0x7f030023
int attr actionViewClass 0x7f030024
int attr activityChooserViewStyle 0x7f030025
int attr alertDialogButtonGroupStyle 0x7f030026
int attr alertDialogCenterButtons 0x7f030027
int attr alertDialogStyle 0x7f030028
int attr alertDialogTheme 0x7f030029
int attr allowStacking 0x7f03002a
int attr alpha 0x7f03002b
int attr alphabeticModifiers 0x7f03002c
int attr altSrc 0x7f03002d
int attr animateCircleAngleTo 0x7f03002e
int attr animateRelativeTo 0x7f03002f
int attr applyMotionScene 0x7f030030
int attr arcMode 0x7f030031
int attr arrowHeadLength 0x7f030032
int attr arrowShaftLength 0x7f030033
int attr attributeName 0x7f030034
int attr autoCompleteMode 0x7f030035
int attr autoCompleteTextViewStyle 0x7f030036
int attr autoSizeMaxTextSize 0x7f030037
int attr autoSizeMinTextSize 0x7f030038
int attr autoSizePresetSizes 0x7f030039
int attr autoSizeStepGranularity 0x7f03003a
int attr autoSizeTextType 0x7f03003b
int attr autoTransition 0x7f03003c
int attr background 0x7f03003d
int attr backgroundSplit 0x7f03003e
int attr backgroundStacked 0x7f03003f
int attr backgroundTint 0x7f030040
int attr backgroundTintMode 0x7f030041
int attr barLength 0x7f030042
int attr barrierAllowsGoneWidgets 0x7f030043
int attr barrierDirection 0x7f030044
int attr barrierMargin 0x7f030045
int attr blendSrc 0x7f030046
int attr borderRound 0x7f030047
int attr borderRoundPercent 0x7f030048
int attr borderlessButtonStyle 0x7f030049
int attr brightness 0x7f03004a
int attr buttonBarButtonStyle 0x7f03004b
int attr buttonBarNegativeButtonStyle 0x7f03004c
int attr buttonBarNeutralButtonStyle 0x7f03004d
int attr buttonBarPositiveButtonStyle 0x7f03004e
int attr buttonBarStyle 0x7f03004f
int attr buttonCompat 0x7f030050
int attr buttonGravity 0x7f030051
int attr buttonIconDimen 0x7f030052
int attr buttonPanelSideLayout 0x7f030053
int attr buttonStyle 0x7f030054
int attr buttonStyleSmall 0x7f030055
int attr buttonTint 0x7f030056
int attr buttonTintMode 0x7f030057
int attr carousel_backwardTransition 0x7f030058
int attr carousel_emptyViewsBehavior 0x7f030059
int attr carousel_firstView 0x7f03005a
int attr carousel_forwardTransition 0x7f03005b
int attr carousel_infinite 0x7f03005c
int attr carousel_nextState 0x7f03005d
int attr carousel_previousState 0x7f03005e
int attr carousel_touchUpMode 0x7f03005f
int attr carousel_touchUp_dampeningFactor 0x7f030060
int attr carousel_touchUp_velocityThreshold 0x7f030061
int attr chainUseRtl 0x7f030062
int attr checkMarkCompat 0x7f030063
int attr checkMarkTint 0x7f030064
int attr checkMarkTintMode 0x7f030065
int attr checkboxStyle 0x7f030066
int attr checkedTextViewStyle 0x7f030067
int attr circleRadius 0x7f030068
int attr circularflow_angles 0x7f030069
int attr circularflow_defaultAngle 0x7f03006a
int attr circularflow_defaultRadius 0x7f03006b
int attr circularflow_radiusInDP 0x7f03006c
int attr circularflow_viewCenter 0x7f03006d
int attr clearsTag 0x7f03006e
int attr clickAction 0x7f03006f
int attr closeIcon 0x7f030070
int attr closeItemLayout 0x7f030071
int attr collapseContentDescription 0x7f030072
int attr collapseIcon 0x7f030073
int attr color 0x7f030074
int attr colorAccent 0x7f030075
int attr colorBackgroundFloating 0x7f030076
int attr colorButtonNormal 0x7f030077
int attr colorControlActivated 0x7f030078
int attr colorControlHighlight 0x7f030079
int attr colorControlNormal 0x7f03007a
int attr colorError 0x7f03007b
int attr colorPrimary 0x7f03007c
int attr colorPrimaryDark 0x7f03007d
int attr colorSwitchThumbNormal 0x7f03007e
int attr commitIcon 0x7f03007f
int attr constraintRotate 0x7f030080
int attr constraintSet 0x7f030081
int attr constraintSetEnd 0x7f030082
int attr constraintSetStart 0x7f030083
int attr constraint_referenced_ids 0x7f030084
int attr constraint_referenced_tags 0x7f030085
int attr constraints 0x7f030086
int attr content 0x7f030087
int attr contentDescription 0x7f030088
int attr contentInsetEnd 0x7f030089
int attr contentInsetEndWithActions 0x7f03008a
int attr contentInsetLeft 0x7f03008b
int attr contentInsetRight 0x7f03008c
int attr contentInsetStart 0x7f03008d
int attr contentInsetStartWithNavigation 0x7f03008e
int attr contrast 0x7f03008f
int attr controlBackground 0x7f030090
int attr crossfade 0x7f030091
int attr currentState 0x7f030092
int attr curveFit 0x7f030093
int attr customBoolean 0x7f030094
int attr customColorDrawableValue 0x7f030095
int attr customColorValue 0x7f030096
int attr customDimension 0x7f030097
int attr customFloatValue 0x7f030098
int attr customIntegerValue 0x7f030099
int attr customNavigationLayout 0x7f03009a
int attr customPixelDimension 0x7f03009b
int attr customReference 0x7f03009c
int attr customStringValue 0x7f03009d
int attr defaultDuration 0x7f03009e
int attr defaultQueryHint 0x7f03009f
int attr defaultState 0x7f0300a0
int attr deltaPolarAngle 0x7f0300a1
int attr deltaPolarRadius 0x7f0300a2
int attr deriveConstraintsFrom 0x7f0300a3
int attr dialogCornerRadius 0x7f0300a4
int attr dialogPreferredPadding 0x7f0300a5
int attr dialogTheme 0x7f0300a6
int attr displayOptions 0x7f0300a7
int attr divider 0x7f0300a8
int attr dividerHorizontal 0x7f0300a9
int attr dividerPadding 0x7f0300aa
int attr dividerVertical 0x7f0300ab
int attr dragDirection 0x7f0300ac
int attr dragScale 0x7f0300ad
int attr dragThreshold 0x7f0300ae
int attr drawPath 0x7f0300af
int attr drawableBottomCompat 0x7f0300b0
int attr drawableEndCompat 0x7f0300b1
int attr drawableLeftCompat 0x7f0300b2
int attr drawableRightCompat 0x7f0300b3
int attr drawableSize 0x7f0300b4
int attr drawableStartCompat 0x7f0300b5
int attr drawableTint 0x7f0300b6
int attr drawableTintMode 0x7f0300b7
int attr drawableTopCompat 0x7f0300b8
int attr drawerArrowStyle 0x7f0300b9
int attr dropDownListViewStyle 0x7f0300ba
int attr dropdownListPreferredItemHeight 0x7f0300bb
int attr duration 0x7f0300bc
int attr editTextBackground 0x7f0300bd
int attr editTextColor 0x7f0300be
int attr editTextStyle 0x7f0300bf
int attr elevation 0x7f0300c0
int attr emojiCompatEnabled 0x7f0300c1
int attr expandActivityOverflowButtonDrawable 0x7f0300c2
int attr fastScrollEnabled 0x7f0300c3
int attr fastScrollHorizontalThumbDrawable 0x7f0300c4
int attr fastScrollHorizontalTrackDrawable 0x7f0300c5
int attr fastScrollVerticalThumbDrawable 0x7f0300c6
int attr fastScrollVerticalTrackDrawable 0x7f0300c7
int attr firstBaselineToTopHeight 0x7f0300c8
int attr flow_firstHorizontalBias 0x7f0300c9
int attr flow_firstHorizontalStyle 0x7f0300ca
int attr flow_firstVerticalBias 0x7f0300cb
int attr flow_firstVerticalStyle 0x7f0300cc
int attr flow_horizontalAlign 0x7f0300cd
int attr flow_horizontalBias 0x7f0300ce
int attr flow_horizontalGap 0x7f0300cf
int attr flow_horizontalStyle 0x7f0300d0
int attr flow_lastHorizontalBias 0x7f0300d1
int attr flow_lastHorizontalStyle 0x7f0300d2
int attr flow_lastVerticalBias 0x7f0300d3
int attr flow_lastVerticalStyle 0x7f0300d4
int attr flow_maxElementsWrap 0x7f0300d5
int attr flow_padding 0x7f0300d6
int attr flow_verticalAlign 0x7f0300d7
int attr flow_verticalBias 0x7f0300d8
int attr flow_verticalGap 0x7f0300d9
int attr flow_verticalStyle 0x7f0300da
int attr flow_wrapMode 0x7f0300db
int attr font 0x7f0300dc
int attr fontFamily 0x7f0300dd
int attr fontProviderAuthority 0x7f0300de
int attr fontProviderCerts 0x7f0300df
int attr fontProviderFetchStrategy 0x7f0300e0
int attr fontProviderFetchTimeout 0x7f0300e1
int attr fontProviderPackage 0x7f0300e2
int attr fontProviderQuery 0x7f0300e3
int attr fontProviderSystemFontFamily 0x7f0300e4
int attr fontStyle 0x7f0300e5
int attr fontVariationSettings 0x7f0300e6
int attr fontWeight 0x7f0300e7
int attr framePosition 0x7f0300e8
int attr gapBetweenBars 0x7f0300e9
int attr goIcon 0x7f0300ea
int attr guidelineUseRtl 0x7f0300eb
int attr height 0x7f0300ec
int attr hideOnContentScroll 0x7f0300ed
int attr homeAsUpIndicator 0x7f0300ee
int attr homeLayout 0x7f0300ef
int attr icon 0x7f0300f0
int attr iconTint 0x7f0300f1
int attr iconTintMode 0x7f0300f2
int attr iconifiedByDefault 0x7f0300f3
int attr ifTagNotSet 0x7f0300f4
int attr ifTagSet 0x7f0300f5
int attr imageButtonStyle 0x7f0300f6
int attr imagePanX 0x7f0300f7
int attr imagePanY 0x7f0300f8
int attr imageRotate 0x7f0300f9
int attr imageZoom 0x7f0300fa
int attr indeterminateProgressStyle 0x7f0300fb
int attr initialActivityCount 0x7f0300fc
int attr isLightTheme 0x7f0300fd
int attr itemPadding 0x7f0300fe
int attr keyPositionType 0x7f0300ff
int attr lStar 0x7f030100
int attr lastBaselineToBottomHeight 0x7f030101
int attr layout 0x7f030102
int attr layoutDescription 0x7f030103
int attr layoutDuringTransition 0x7f030104
int attr layoutManager 0x7f030105
int attr layout_constrainedHeight 0x7f030106
int attr layout_constrainedWidth 0x7f030107
int attr layout_constraintBaseline_creator 0x7f030108
int attr layout_constraintBaseline_toBaselineOf 0x7f030109
int attr layout_constraintBaseline_toBottomOf 0x7f03010a
int attr layout_constraintBaseline_toTopOf 0x7f03010b
int attr layout_constraintBottom_creator 0x7f03010c
int attr layout_constraintBottom_toBottomOf 0x7f03010d
int attr layout_constraintBottom_toTopOf 0x7f03010e
int attr layout_constraintCircle 0x7f03010f
int attr layout_constraintCircleAngle 0x7f030110
int attr layout_constraintCircleRadius 0x7f030111
int attr layout_constraintDimensionRatio 0x7f030112
int attr layout_constraintEnd_toEndOf 0x7f030113
int attr layout_constraintEnd_toStartOf 0x7f030114
int attr layout_constraintGuide_begin 0x7f030115
int attr layout_constraintGuide_end 0x7f030116
int attr layout_constraintGuide_percent 0x7f030117
int attr layout_constraintHeight 0x7f030118
int attr layout_constraintHeight_default 0x7f030119
int attr layout_constraintHeight_max 0x7f03011a
int attr layout_constraintHeight_min 0x7f03011b
int attr layout_constraintHeight_percent 0x7f03011c
int attr layout_constraintHorizontal_bias 0x7f03011d
int attr layout_constraintHorizontal_chainStyle 0x7f03011e
int attr layout_constraintHorizontal_weight 0x7f03011f
int attr layout_constraintLeft_creator 0x7f030120
int attr layout_constraintLeft_toLeftOf 0x7f030121
int attr layout_constraintLeft_toRightOf 0x7f030122
int attr layout_constraintRight_creator 0x7f030123
int attr layout_constraintRight_toLeftOf 0x7f030124
int attr layout_constraintRight_toRightOf 0x7f030125
int attr layout_constraintStart_toEndOf 0x7f030126
int attr layout_constraintStart_toStartOf 0x7f030127
int attr layout_constraintTag 0x7f030128
int attr layout_constraintTop_creator 0x7f030129
int attr layout_constraintTop_toBottomOf 0x7f03012a
int attr layout_constraintTop_toTopOf 0x7f03012b
int attr layout_constraintVertical_bias 0x7f03012c
int attr layout_constraintVertical_chainStyle 0x7f03012d
int attr layout_constraintVertical_weight 0x7f03012e
int attr layout_constraintWidth 0x7f03012f
int attr layout_constraintWidth_default 0x7f030130
int attr layout_constraintWidth_max 0x7f030131
int attr layout_constraintWidth_min 0x7f030132
int attr layout_constraintWidth_percent 0x7f030133
int attr layout_editor_absoluteX 0x7f030134
int attr layout_editor_absoluteY 0x7f030135
int attr layout_goneMarginBaseline 0x7f030136
int attr layout_goneMarginBottom 0x7f030137
int attr layout_goneMarginEnd 0x7f030138
int attr layout_goneMarginLeft 0x7f030139
int attr layout_goneMarginRight 0x7f03013a
int attr layout_goneMarginStart 0x7f03013b
int attr layout_goneMarginTop 0x7f03013c
int attr layout_marginBaseline 0x7f03013d
int attr layout_optimizationLevel 0x7f03013e
int attr layout_wrapBehaviorInParent 0x7f03013f
int attr limitBoundsTo 0x7f030140
int attr lineHeight 0x7f030141
int attr listChoiceBackgroundIndicator 0x7f030142
int attr listChoiceIndicatorMultipleAnimated 0x7f030143
int attr listChoiceIndicatorSingleAnimated 0x7f030144
int attr listDividerAlertDialog 0x7f030145
int attr listItemLayout 0x7f030146
int attr listLayout 0x7f030147
int attr listMenuViewStyle 0x7f030148
int attr listPopupWindowStyle 0x7f030149
int attr listPreferredItemHeight 0x7f03014a
int attr listPreferredItemHeightLarge 0x7f03014b
int attr listPreferredItemHeightSmall 0x7f03014c
int attr listPreferredItemPaddingEnd 0x7f03014d
int attr listPreferredItemPaddingLeft 0x7f03014e
int attr listPreferredItemPaddingRight 0x7f03014f
int attr listPreferredItemPaddingStart 0x7f030150
int attr logo 0x7f030151
int attr logoDescription 0x7f030152
int attr maxAcceleration 0x7f030153
int attr maxButtonHeight 0x7f030154
int attr maxHeight 0x7f030155
int attr maxVelocity 0x7f030156
int attr maxWidth 0x7f030157
int attr measureWithLargestChild 0x7f030158
int attr menu 0x7f030159
int attr methodName 0x7f03015a
int attr minHeight 0x7f03015b
int attr minWidth 0x7f03015c
int attr mock_diagonalsColor 0x7f03015d
int attr mock_label 0x7f03015e
int attr mock_labelBackgroundColor 0x7f03015f
int attr mock_labelColor 0x7f030160
int attr mock_showDiagonals 0x7f030161
int attr mock_showLabel 0x7f030162
int attr motionDebug 0x7f030163
int attr motionEffect_alpha 0x7f030164
int attr motionEffect_end 0x7f030165
int attr motionEffect_move 0x7f030166
int attr motionEffect_start 0x7f030167
int attr motionEffect_strict 0x7f030168
int attr motionEffect_translationX 0x7f030169
int attr motionEffect_translationY 0x7f03016a
int attr motionEffect_viewTransition 0x7f03016b
int attr motionInterpolator 0x7f03016c
int attr motionPathRotate 0x7f03016d
int attr motionProgress 0x7f03016e
int attr motionStagger 0x7f03016f
int attr motionTarget 0x7f030170
int attr motion_postLayoutCollision 0x7f030171
int attr motion_triggerOnCollision 0x7f030172
int attr moveWhenScrollAtTop 0x7f030173
int attr multiChoiceItemLayout 0x7f030174
int attr navigationContentDescription 0x7f030175
int attr navigationIcon 0x7f030176
int attr navigationMode 0x7f030177
int attr nestedScrollFlags 0x7f030178
int attr nestedScrollViewStyle 0x7f030179
int attr numericModifiers 0x7f03017a
int attr onCross 0x7f03017b
int attr onHide 0x7f03017c
int attr onNegativeCross 0x7f03017d
int attr onPositiveCross 0x7f03017e
int attr onShow 0x7f03017f
int attr onStateTransition 0x7f030180
int attr onTouchUp 0x7f030181
int attr overlapAnchor 0x7f030182
int attr overlay 0x7f030183
int attr paddingBottomNoButtons 0x7f030184
int attr paddingEnd 0x7f030185
int attr paddingStart 0x7f030186
int attr paddingTopNoTitle 0x7f030187
int attr panelBackground 0x7f030188
int attr panelMenuListTheme 0x7f030189
int attr panelMenuListWidth 0x7f03018a
int attr pathMotionArc 0x7f03018b
int attr path_percent 0x7f03018c
int attr percentHeight 0x7f03018d
int attr percentWidth 0x7f03018e
int attr percentX 0x7f03018f
int attr percentY 0x7f030190
int attr perpendicularPath_percent 0x7f030191
int attr pivotAnchor 0x7f030192
int attr placeholder_emptyVisibility 0x7f030193
int attr polarRelativeTo 0x7f030194
int attr popupMenuStyle 0x7f030195
int attr popupTheme 0x7f030196
int attr popupWindowStyle 0x7f030197
int attr preserveIconSpacing 0x7f030198
int attr progressBarPadding 0x7f030199
int attr progressBarStyle 0x7f03019a
int attr quantizeMotionInterpolator 0x7f03019b
int attr quantizeMotionPhase 0x7f03019c
int attr quantizeMotionSteps 0x7f03019d
int attr queryBackground 0x7f03019e
int attr queryHint 0x7f03019f
int attr queryPatterns 0x7f0301a0
int attr radioButtonStyle 0x7f0301a1
int attr ratingBarStyle 0x7f0301a2
int attr ratingBarStyleIndicator 0x7f0301a3
int attr ratingBarStyleSmall 0x7f0301a4
int attr reactiveGuide_animateChange 0x7f0301a5
int attr reactiveGuide_applyToAllConstraintSets 0x7f0301a6
int attr reactiveGuide_applyToConstraintSet 0x7f0301a7
int attr reactiveGuide_valueId 0x7f0301a8
int attr recyclerViewStyle 0x7f0301a9
int attr region_heightLessThan 0x7f0301aa
int attr region_heightMoreThan 0x7f0301ab
int attr region_widthLessThan 0x7f0301ac
int attr region_widthMoreThan 0x7f0301ad
int attr reverseLayout 0x7f0301ae
int attr rotationCenterId 0x7f0301af
int attr round 0x7f0301b0
int attr roundPercent 0x7f0301b1
int attr saturation 0x7f0301b2
int attr scaleFromTextSize 0x7f0301b3
int attr searchHintIcon 0x7f0301b4
int attr searchIcon 0x7f0301b5
int attr searchViewStyle 0x7f0301b6
int attr seekBarStyle 0x7f0301b7
int attr selectableItemBackground 0x7f0301b8
int attr selectableItemBackgroundBorderless 0x7f0301b9
int attr setsTag 0x7f0301ba
int attr shortcutMatchRequired 0x7f0301bb
int attr showAsAction 0x7f0301bc
int attr showDividers 0x7f0301bd
int attr showPaths 0x7f0301be
int attr showText 0x7f0301bf
int attr showTitle 0x7f0301c0
int attr singleChoiceItemLayout 0x7f0301c1
int attr sizePercent 0x7f0301c2
int attr spanCount 0x7f0301c3
int attr spinBars 0x7f0301c4
int attr spinnerDropDownItemStyle 0x7f0301c5
int attr spinnerStyle 0x7f0301c6
int attr splitTrack 0x7f0301c7
int attr springBoundary 0x7f0301c8
int attr springDamping 0x7f0301c9
int attr springMass 0x7f0301ca
int attr springStiffness 0x7f0301cb
int attr springStopThreshold 0x7f0301cc
int attr srcCompat 0x7f0301cd
int attr stackFromEnd 0x7f0301ce
int attr staggered 0x7f0301cf
int attr state_above_anchor 0x7f0301d0
int attr subMenuArrow 0x7f0301d1
int attr submitBackground 0x7f0301d2
int attr subtitle 0x7f0301d3
int attr subtitleTextAppearance 0x7f0301d4
int attr subtitleTextColor 0x7f0301d5
int attr subtitleTextStyle 0x7f0301d6
int attr suggestionRowLayout 0x7f0301d7
int attr switchMinWidth 0x7f0301d8
int attr switchPadding 0x7f0301d9
int attr switchStyle 0x7f0301da
int attr switchTextAppearance 0x7f0301db
int attr targetId 0x7f0301dc
int attr telltales_tailColor 0x7f0301dd
int attr telltales_tailScale 0x7f0301de
int attr telltales_velocityMode 0x7f0301df
int attr textAllCaps 0x7f0301e0
int attr textAppearanceLargePopupMenu 0x7f0301e1
int attr textAppearanceListItem 0x7f0301e2
int attr textAppearanceListItemSecondary 0x7f0301e3
int attr textAppearanceListItemSmall 0x7f0301e4
int attr textAppearancePopupMenuHeader 0x7f0301e5
int attr textAppearanceSearchResultSubtitle 0x7f0301e6
int attr textAppearanceSearchResultTitle 0x7f0301e7
int attr textAppearanceSmallPopupMenu 0x7f0301e8
int attr textBackground 0x7f0301e9
int attr textBackgroundPanX 0x7f0301ea
int attr textBackgroundPanY 0x7f0301eb
int attr textBackgroundRotate 0x7f0301ec
int attr textBackgroundZoom 0x7f0301ed
int attr textColorAlertDialogListItem 0x7f0301ee
int attr textColorSearchUrl 0x7f0301ef
int attr textFillColor 0x7f0301f0
int attr textLocale 0x7f0301f1
int attr textOutlineColor 0x7f0301f2
int attr textOutlineThickness 0x7f0301f3
int attr textPanX 0x7f0301f4
int attr textPanY 0x7f0301f5
int attr textureBlurFactor 0x7f0301f6
int attr textureEffect 0x7f0301f7
int attr textureHeight 0x7f0301f8
int attr textureWidth 0x7f0301f9
int attr theme 0x7f0301fa
int attr thickness 0x7f0301fb
int attr thumbTextPadding 0x7f0301fc
int attr thumbTint 0x7f0301fd
int attr thumbTintMode 0x7f0301fe
int attr tickMark 0x7f0301ff
int attr tickMarkTint 0x7f030200
int attr tickMarkTintMode 0x7f030201
int attr tint 0x7f030202
int attr tintMode 0x7f030203
int attr title 0x7f030204
int attr titleMargin 0x7f030205
int attr titleMarginBottom 0x7f030206
int attr titleMarginEnd 0x7f030207
int attr titleMarginStart 0x7f030208
int attr titleMarginTop 0x7f030209
int attr titleMargins 0x7f03020a
int attr titleTextAppearance 0x7f03020b
int attr titleTextColor 0x7f03020c
int attr titleTextStyle 0x7f03020d
int attr toolbarNavigationButtonStyle 0x7f03020e
int attr toolbarStyle 0x7f03020f
int attr tooltipForegroundColor 0x7f030210
int attr tooltipFrameBackground 0x7f030211
int attr tooltipText 0x7f030212
int attr touchAnchorId 0x7f030213
int attr touchAnchorSide 0x7f030214
int attr touchRegionId 0x7f030215
int attr track 0x7f030216
int attr trackTint 0x7f030217
int attr trackTintMode 0x7f030218
int attr transformPivotTarget 0x7f030219
int attr transitionDisable 0x7f03021a
int attr transitionEasing 0x7f03021b
int attr transitionFlags 0x7f03021c
int attr transitionPathRotate 0x7f03021d
int attr triggerId 0x7f03021e
int attr triggerReceiver 0x7f03021f
int attr triggerSlack 0x7f030220
int attr ttcIndex 0x7f030221
int attr upDuration 0x7f030222
int attr viewInflaterClass 0x7f030223
int attr viewTransitionMode 0x7f030224
int attr viewTransitionOnCross 0x7f030225
int attr viewTransitionOnNegativeCross 0x7f030226
int attr viewTransitionOnPositiveCross 0x7f030227
int attr visibilityMode 0x7f030228
int attr voiceIcon 0x7f030229
int attr warmth 0x7f03022a
int attr waveDecay 0x7f03022b
int attr waveOffset 0x7f03022c
int attr wavePeriod 0x7f03022d
int attr wavePhase 0x7f03022e
int attr waveShape 0x7f03022f
int attr waveVariesBy 0x7f030230
int attr windowActionBar 0x7f030231
int attr windowActionBarOverlay 0x7f030232
int attr windowActionModeOverlay 0x7f030233
int attr windowFixedHeightMajor 0x7f030234
int attr windowFixedHeightMinor 0x7f030235
int attr windowFixedWidthMajor 0x7f030236
int attr windowFixedWidthMinor 0x7f030237
int attr windowMinWidthMajor 0x7f030238
int attr windowMinWidthMinor 0x7f030239
int attr windowNoTitle 0x7f03023a
int bool abc_action_bar_embed_tabs 0x7f040000
int bool abc_config_actionMenuItemAllCaps 0x7f040001
int color abc_background_cache_hint_selector_material_dark 0x7f050000
int color abc_background_cache_hint_selector_material_light 0x7f050001
int color abc_btn_colored_borderless_text_material 0x7f050002
int color abc_btn_colored_text_material 0x7f050003
int color abc_color_highlight_material 0x7f050004
int color abc_decor_view_status_guard 0x7f050005
int color abc_decor_view_status_guard_light 0x7f050006
int color abc_hint_foreground_material_dark 0x7f050007
int color abc_hint_foreground_material_light 0x7f050008
int color abc_primary_text_disable_only_material_dark 0x7f050009
int color abc_primary_text_disable_only_material_light 0x7f05000a
int color abc_primary_text_material_dark 0x7f05000b
int color abc_primary_text_material_light 0x7f05000c
int color abc_search_url_text 0x7f05000d
int color abc_search_url_text_normal 0x7f05000e
int color abc_search_url_text_pressed 0x7f05000f
int color abc_search_url_text_selected 0x7f050010
int color abc_secondary_text_material_dark 0x7f050011
int color abc_secondary_text_material_light 0x7f050012
int color abc_tint_btn_checkable 0x7f050013
int color abc_tint_default 0x7f050014
int color abc_tint_edittext 0x7f050015
int color abc_tint_seek_thumb 0x7f050016
int color abc_tint_spinner 0x7f050017
int color abc_tint_switch_track 0x7f050018
int color accent_material_dark 0x7f050019
int color accent_material_light 0x7f05001a
int color androidx_core_ripple_material_light 0x7f05001b
int color androidx_core_secondary_text_default_material_light 0x7f05001c
int color background_floating_material_dark 0x7f05001d
int color background_floating_material_light 0x7f05001e
int color background_material_dark 0x7f05001f
int color background_material_light 0x7f050020
int color black 0x7f050021
int color bright_foreground_disabled_material_dark 0x7f050022
int color bright_foreground_disabled_material_light 0x7f050023
int color bright_foreground_inverse_material_dark 0x7f050024
int color bright_foreground_inverse_material_light 0x7f050025
int color bright_foreground_material_dark 0x7f050026
int color bright_foreground_material_light 0x7f050027
int color button_material_dark 0x7f050028
int color button_material_light 0x7f050029
int color call_notification_answer_color 0x7f05002a
int color call_notification_decline_color 0x7f05002b
int color dim_foreground_disabled_material_dark 0x7f05002c
int color dim_foreground_disabled_material_light 0x7f05002d
int color dim_foreground_material_dark 0x7f05002e
int color dim_foreground_material_light 0x7f05002f
int color error_color_material_dark 0x7f050030
int color error_color_material_light 0x7f050031
int color foreground_material_dark 0x7f050032
int color foreground_material_light 0x7f050033
int color highlighted_text_material_dark 0x7f050034
int color highlighted_text_material_light 0x7f050035
int color material_blue_grey_800 0x7f050036
int color material_blue_grey_900 0x7f050037
int color material_blue_grey_950 0x7f050038
int color material_deep_teal_200 0x7f050039
int color material_deep_teal_500 0x7f05003a
int color material_grey_100 0x7f05003b
int color material_grey_300 0x7f05003c
int color material_grey_50 0x7f05003d
int color material_grey_600 0x7f05003e
int color material_grey_800 0x7f05003f
int color material_grey_850 0x7f050040
int color material_grey_900 0x7f050041
int color md_theme_dark_background 0x7f050042
int color md_theme_dark_error 0x7f050043
int color md_theme_dark_errorContainer 0x7f050044
int color md_theme_dark_inverseOnSurface 0x7f050045
int color md_theme_dark_inversePrimary 0x7f050046
int color md_theme_dark_inverseSurface 0x7f050047
int color md_theme_dark_onBackground 0x7f050048
int color md_theme_dark_onError 0x7f050049
int color md_theme_dark_onErrorContainer 0x7f05004a
int color md_theme_dark_onPrimary 0x7f05004b
int color md_theme_dark_onPrimaryContainer 0x7f05004c
int color md_theme_dark_onSecondary 0x7f05004d
int color md_theme_dark_onSecondaryContainer 0x7f05004e
int color md_theme_dark_onSurface 0x7f05004f
int color md_theme_dark_onSurfaceVariant 0x7f050050
int color md_theme_dark_onTertiary 0x7f050051
int color md_theme_dark_onTertiaryContainer 0x7f050052
int color md_theme_dark_outline 0x7f050053
int color md_theme_dark_primary 0x7f050054
int color md_theme_dark_primaryContainer 0x7f050055
int color md_theme_dark_secondary 0x7f050056
int color md_theme_dark_secondaryContainer 0x7f050057
int color md_theme_dark_surface 0x7f050058
int color md_theme_dark_surfaceVariant 0x7f050059
int color md_theme_dark_tertiary 0x7f05005a
int color md_theme_dark_tertiaryContainer 0x7f05005b
int color md_theme_light_background 0x7f05005c
int color md_theme_light_error 0x7f05005d
int color md_theme_light_errorContainer 0x7f05005e
int color md_theme_light_inverseOnSurface 0x7f05005f
int color md_theme_light_inversePrimary 0x7f050060
int color md_theme_light_inverseSurface 0x7f050061
int color md_theme_light_onBackground 0x7f050062
int color md_theme_light_onError 0x7f050063
int color md_theme_light_onErrorContainer 0x7f050064
int color md_theme_light_onPrimary 0x7f050065
int color md_theme_light_onPrimaryContainer 0x7f050066
int color md_theme_light_onSecondary 0x7f050067
int color md_theme_light_onSecondaryContainer 0x7f050068
int color md_theme_light_onSurface 0x7f050069
int color md_theme_light_onSurfaceVariant 0x7f05006a
int color md_theme_light_onTertiary 0x7f05006b
int color md_theme_light_onTertiaryContainer 0x7f05006c
int color md_theme_light_outline 0x7f05006d
int color md_theme_light_primary 0x7f05006e
int color md_theme_light_primaryContainer 0x7f05006f
int color md_theme_light_secondary 0x7f050070
int color md_theme_light_secondaryContainer 0x7f050071
int color md_theme_light_surface 0x7f050072
int color md_theme_light_surfaceVariant 0x7f050073
int color md_theme_light_tertiary 0x7f050074
int color md_theme_light_tertiaryContainer 0x7f050075
int color notification_action_color_filter 0x7f050076
int color notification_icon_bg_color 0x7f050077
int color pdf_background 0x7f050078
int color pdf_background_dark 0x7f050079
int color primary_dark_material_dark 0x7f05007a
int color primary_dark_material_light 0x7f05007b
int color primary_material_dark 0x7f05007c
int color primary_material_light 0x7f05007d
int color primary_text_default_material_dark 0x7f05007e
int color primary_text_default_material_light 0x7f05007f
int color primary_text_disabled_material_dark 0x7f050080
int color primary_text_disabled_material_light 0x7f050081
int color purple_200 0x7f050082
int color purple_500 0x7f050083
int color purple_700 0x7f050084
int color ripple_material_dark 0x7f050085
int color ripple_material_light 0x7f050086
int color search_highlight 0x7f050087
int color search_highlight_current 0x7f050088
int color secondary_text_default_material_dark 0x7f050089
int color secondary_text_default_material_light 0x7f05008a
int color secondary_text_disabled_material_dark 0x7f05008b
int color secondary_text_disabled_material_light 0x7f05008c
int color splash_background 0x7f05008d
int color splash_icon_tint 0x7f05008e
int color status_bar_dark 0x7f05008f
int color status_bar_light 0x7f050090
int color switch_thumb_disabled_material_dark 0x7f050091
int color switch_thumb_disabled_material_light 0x7f050092
int color switch_thumb_material_dark 0x7f050093
int color switch_thumb_material_light 0x7f050094
int color switch_thumb_normal_material_dark 0x7f050095
int color switch_thumb_normal_material_light 0x7f050096
int color teal_200 0x7f050097
int color teal_700 0x7f050098
int color toolbar_overlay 0x7f050099
int color tooltip_background_dark 0x7f05009a
int color tooltip_background_light 0x7f05009b
int color white 0x7f05009c
int dimen abc_action_bar_content_inset_material 0x7f060000
int dimen abc_action_bar_content_inset_with_nav 0x7f060001
int dimen abc_action_bar_default_height_material 0x7f060002
int dimen abc_action_bar_default_padding_end_material 0x7f060003
int dimen abc_action_bar_default_padding_start_material 0x7f060004
int dimen abc_action_bar_elevation_material 0x7f060005
int dimen abc_action_bar_icon_vertical_padding_material 0x7f060006
int dimen abc_action_bar_overflow_padding_end_material 0x7f060007
int dimen abc_action_bar_overflow_padding_start_material 0x7f060008
int dimen abc_action_bar_stacked_max_height 0x7f060009
int dimen abc_action_bar_stacked_tab_max_width 0x7f06000a
int dimen abc_action_bar_subtitle_bottom_margin_material 0x7f06000b
int dimen abc_action_bar_subtitle_top_margin_material 0x7f06000c
int dimen abc_action_button_min_height_material 0x7f06000d
int dimen abc_action_button_min_width_material 0x7f06000e
int dimen abc_action_button_min_width_overflow_material 0x7f06000f
int dimen abc_alert_dialog_button_bar_height 0x7f060010
int dimen abc_alert_dialog_button_dimen 0x7f060011
int dimen abc_button_inset_horizontal_material 0x7f060012
int dimen abc_button_inset_vertical_material 0x7f060013
int dimen abc_button_padding_horizontal_material 0x7f060014
int dimen abc_button_padding_vertical_material 0x7f060015
int dimen abc_cascading_menus_min_smallest_width 0x7f060016
int dimen abc_config_prefDialogWidth 0x7f060017
int dimen abc_control_corner_material 0x7f060018
int dimen abc_control_inset_material 0x7f060019
int dimen abc_control_padding_material 0x7f06001a
int dimen abc_dialog_corner_radius_material 0x7f06001b
int dimen abc_dialog_fixed_height_major 0x7f06001c
int dimen abc_dialog_fixed_height_minor 0x7f06001d
int dimen abc_dialog_fixed_width_major 0x7f06001e
int dimen abc_dialog_fixed_width_minor 0x7f06001f
int dimen abc_dialog_list_padding_bottom_no_buttons 0x7f060020
int dimen abc_dialog_list_padding_top_no_title 0x7f060021
int dimen abc_dialog_min_width_major 0x7f060022
int dimen abc_dialog_min_width_minor 0x7f060023
int dimen abc_dialog_padding_material 0x7f060024
int dimen abc_dialog_padding_top_material 0x7f060025
int dimen abc_dialog_title_divider_material 0x7f060026
int dimen abc_disabled_alpha_material_dark 0x7f060027
int dimen abc_disabled_alpha_material_light 0x7f060028
int dimen abc_dropdownitem_icon_width 0x7f060029
int dimen abc_dropdownitem_text_padding_left 0x7f06002a
int dimen abc_dropdownitem_text_padding_right 0x7f06002b
int dimen abc_edit_text_inset_bottom_material 0x7f06002c
int dimen abc_edit_text_inset_horizontal_material 0x7f06002d
int dimen abc_edit_text_inset_top_material 0x7f06002e
int dimen abc_floating_window_z 0x7f06002f
int dimen abc_list_item_height_large_material 0x7f060030
int dimen abc_list_item_height_material 0x7f060031
int dimen abc_list_item_height_small_material 0x7f060032
int dimen abc_list_item_padding_horizontal_material 0x7f060033
int dimen abc_panel_menu_list_width 0x7f060034
int dimen abc_progress_bar_height_material 0x7f060035
int dimen abc_search_view_preferred_height 0x7f060036
int dimen abc_search_view_preferred_width 0x7f060037
int dimen abc_seekbar_track_background_height_material 0x7f060038
int dimen abc_seekbar_track_progress_height_material 0x7f060039
int dimen abc_select_dialog_padding_start_material 0x7f06003a
int dimen abc_star_big 0x7f06003b
int dimen abc_star_medium 0x7f06003c
int dimen abc_star_small 0x7f06003d
int dimen abc_switch_padding 0x7f06003e
int dimen abc_text_size_body_1_material 0x7f06003f
int dimen abc_text_size_body_2_material 0x7f060040
int dimen abc_text_size_button_material 0x7f060041
int dimen abc_text_size_caption_material 0x7f060042
int dimen abc_text_size_display_1_material 0x7f060043
int dimen abc_text_size_display_2_material 0x7f060044
int dimen abc_text_size_display_3_material 0x7f060045
int dimen abc_text_size_display_4_material 0x7f060046
int dimen abc_text_size_headline_material 0x7f060047
int dimen abc_text_size_large_material 0x7f060048
int dimen abc_text_size_medium_material 0x7f060049
int dimen abc_text_size_menu_header_material 0x7f06004a
int dimen abc_text_size_menu_material 0x7f06004b
int dimen abc_text_size_small_material 0x7f06004c
int dimen abc_text_size_subhead_material 0x7f06004d
int dimen abc_text_size_subtitle_material_toolbar 0x7f06004e
int dimen abc_text_size_title_material 0x7f06004f
int dimen abc_text_size_title_material_toolbar 0x7f060050
int dimen app_bar_height 0x7f060051
int dimen bottom_nav_height 0x7f060052
int dimen button_corner_radius 0x7f060053
int dimen button_height 0x7f060054
int dimen card_corner_radius 0x7f060055
int dimen card_elevation 0x7f060056
int dimen compat_button_inset_horizontal_material 0x7f060057
int dimen compat_button_inset_vertical_material 0x7f060058
int dimen compat_button_padding_horizontal_material 0x7f060059
int dimen compat_button_padding_vertical_material 0x7f06005a
int dimen compat_control_corner_material 0x7f06005b
int dimen compat_notification_large_icon_max_height 0x7f06005c
int dimen compat_notification_large_icon_max_width 0x7f06005d
int dimen container_padding 0x7f06005e
int dimen desktop_breakpoint 0x7f06005f
int dimen disabled_alpha_material_dark 0x7f060060
int dimen disabled_alpha_material_light 0x7f060061
int dimen drawer_width 0x7f060062
int dimen fab_margin 0x7f060063
int dimen fastscroll_default_thickness 0x7f060064
int dimen fastscroll_margin 0x7f060065
int dimen fastscroll_minimum_range 0x7f060066
int dimen highlight_alpha_material_colored 0x7f060067
int dimen highlight_alpha_material_dark 0x7f060068
int dimen highlight_alpha_material_light 0x7f060069
int dimen hint_alpha_material_dark 0x7f06006a
int dimen hint_alpha_material_light 0x7f06006b
int dimen hint_pressed_alpha_material_dark 0x7f06006c
int dimen hint_pressed_alpha_material_light 0x7f06006d
int dimen icon_size_extra_large 0x7f06006e
int dimen icon_size_large 0x7f06006f
int dimen icon_size_medium 0x7f060070
int dimen icon_size_small 0x7f060071
int dimen item_touch_helper_max_drag_scroll_per_frame 0x7f060072
int dimen item_touch_helper_swipe_escape_max_velocity 0x7f060073
int dimen item_touch_helper_swipe_escape_velocity 0x7f060074
int dimen list_item_height 0x7f060075
int dimen list_item_height_small 0x7f060076
int dimen list_item_padding_horizontal 0x7f060077
int dimen list_item_padding_vertical 0x7f060078
int dimen margin_extra_large 0x7f060079
int dimen margin_large 0x7f06007a
int dimen margin_medium 0x7f06007b
int dimen margin_small 0x7f06007c
int dimen notification_action_icon_size 0x7f06007d
int dimen notification_action_text_size 0x7f06007e
int dimen notification_big_circle_margin 0x7f06007f
int dimen notification_content_margin_start 0x7f060080
int dimen notification_large_icon_height 0x7f060081
int dimen notification_large_icon_width 0x7f060082
int dimen notification_main_column_padding_top 0x7f060083
int dimen notification_media_narrow_margin 0x7f060084
int dimen notification_right_icon_size 0x7f060085
int dimen notification_right_side_padding_top 0x7f060086
int dimen notification_small_icon_background_padding 0x7f060087
int dimen notification_small_icon_size_as_large 0x7f060088
int dimen notification_subtext_size 0x7f060089
int dimen notification_top_pad 0x7f06008a
int dimen notification_top_pad_large_text 0x7f06008b
int dimen padding_extra_large 0x7f06008c
int dimen padding_large 0x7f06008d
int dimen padding_medium 0x7f06008e
int dimen padding_small 0x7f06008f
int dimen pdf_bottom_bar_height 0x7f060090
int dimen pdf_page_margin 0x7f060091
int dimen pdf_toolbar_height 0x7f060092
int dimen search_bar_height 0x7f060093
int dimen spacing_lg 0x7f060094
int dimen spacing_md 0x7f060095
int dimen spacing_sm 0x7f060096
int dimen spacing_xl 0x7f060097
int dimen spacing_xs 0x7f060098
int dimen spacing_xxl 0x7f060099
int dimen splash_logo_size 0x7f06009a
int dimen tablet_breakpoint 0x7f06009b
int dimen text_size_body_large 0x7f06009c
int dimen text_size_body_medium 0x7f06009d
int dimen text_size_body_small 0x7f06009e
int dimen text_size_headline_large 0x7f06009f
int dimen text_size_headline_medium 0x7f0600a0
int dimen text_size_headline_small 0x7f0600a1
int dimen text_size_label_large 0x7f0600a2
int dimen text_size_label_medium 0x7f0600a3
int dimen text_size_label_small 0x7f0600a4
int dimen text_size_title_large 0x7f0600a5
int dimen text_size_title_medium 0x7f0600a6
int dimen text_size_title_small 0x7f0600a7
int dimen toolbar_elevation 0x7f0600a8
int dimen tooltip_corner_radius 0x7f0600a9
int dimen tooltip_horizontal_padding 0x7f0600aa
int dimen tooltip_margin 0x7f0600ab
int dimen tooltip_precise_anchor_extra_offset 0x7f0600ac
int dimen tooltip_precise_anchor_threshold 0x7f0600ad
int dimen tooltip_vertical_padding 0x7f0600ae
int dimen tooltip_y_offset_non_touch 0x7f0600af
int dimen tooltip_y_offset_touch 0x7f0600b0
int drawable abc_ab_share_pack_mtrl_alpha 0x7f070000
int drawable abc_action_bar_item_background_material 0x7f070001
int drawable abc_btn_borderless_material 0x7f070002
int drawable abc_btn_check_material 0x7f070003
int drawable abc_btn_check_material_anim 0x7f070004
int drawable abc_btn_check_to_on_mtrl_000 0x7f070005
int drawable abc_btn_check_to_on_mtrl_015 0x7f070006
int drawable abc_btn_colored_material 0x7f070007
int drawable abc_btn_default_mtrl_shape 0x7f070008
int drawable abc_btn_radio_material 0x7f070009
int drawable abc_btn_radio_material_anim 0x7f07000a
int drawable abc_btn_radio_to_on_mtrl_000 0x7f07000b
int drawable abc_btn_radio_to_on_mtrl_015 0x7f07000c
int drawable abc_btn_switch_to_on_mtrl_00001 0x7f07000d
int drawable abc_btn_switch_to_on_mtrl_00012 0x7f07000e
int drawable abc_cab_background_internal_bg 0x7f07000f
int drawable abc_cab_background_top_material 0x7f070010
int drawable abc_cab_background_top_mtrl_alpha 0x7f070011
int drawable abc_control_background_material 0x7f070012
int drawable abc_dialog_material_background 0x7f070013
int drawable abc_edit_text_material 0x7f070014
int drawable abc_ic_ab_back_material 0x7f070015
int drawable abc_ic_arrow_drop_right_black_24dp 0x7f070016
int drawable abc_ic_clear_material 0x7f070017
int drawable abc_ic_commit_search_api_mtrl_alpha 0x7f070018
int drawable abc_ic_go_search_api_material 0x7f070019
int drawable abc_ic_menu_copy_mtrl_am_alpha 0x7f07001a
int drawable abc_ic_menu_cut_mtrl_alpha 0x7f07001b
int drawable abc_ic_menu_overflow_material 0x7f07001c
int drawable abc_ic_menu_paste_mtrl_am_alpha 0x7f07001d
int drawable abc_ic_menu_selectall_mtrl_alpha 0x7f07001e
int drawable abc_ic_menu_share_mtrl_alpha 0x7f07001f
int drawable abc_ic_search_api_material 0x7f070020
int drawable abc_ic_voice_search_api_material 0x7f070021
int drawable abc_item_background_holo_dark 0x7f070022
int drawable abc_item_background_holo_light 0x7f070023
int drawable abc_list_divider_material 0x7f070024
int drawable abc_list_divider_mtrl_alpha 0x7f070025
int drawable abc_list_focused_holo 0x7f070026
int drawable abc_list_longpressed_holo 0x7f070027
int drawable abc_list_pressed_holo_dark 0x7f070028
int drawable abc_list_pressed_holo_light 0x7f070029
int drawable abc_list_selector_background_transition_holo_dark 0x7f07002a
int drawable abc_list_selector_background_transition_holo_light 0x7f07002b
int drawable abc_list_selector_disabled_holo_dark 0x7f07002c
int drawable abc_list_selector_disabled_holo_light 0x7f07002d
int drawable abc_list_selector_holo_dark 0x7f07002e
int drawable abc_list_selector_holo_light 0x7f07002f
int drawable abc_menu_hardkey_panel_mtrl_mult 0x7f070030
int drawable abc_popup_background_mtrl_mult 0x7f070031
int drawable abc_ratingbar_indicator_material 0x7f070032
int drawable abc_ratingbar_material 0x7f070033
int drawable abc_ratingbar_small_material 0x7f070034
int drawable abc_scrubber_control_off_mtrl_alpha 0x7f070035
int drawable abc_scrubber_control_to_pressed_mtrl_000 0x7f070036
int drawable abc_scrubber_control_to_pressed_mtrl_005 0x7f070037
int drawable abc_scrubber_primary_mtrl_alpha 0x7f070038
int drawable abc_scrubber_track_mtrl_alpha 0x7f070039
int drawable abc_seekbar_thumb_material 0x7f07003a
int drawable abc_seekbar_tick_mark_material 0x7f07003b
int drawable abc_seekbar_track_material 0x7f07003c
int drawable abc_spinner_mtrl_am_alpha 0x7f07003d
int drawable abc_spinner_textfield_background_material 0x7f07003e
int drawable abc_star_black_48dp 0x7f07003f
int drawable abc_star_half_black_48dp 0x7f070040
int drawable abc_switch_thumb_material 0x7f070041
int drawable abc_switch_track_mtrl_alpha 0x7f070042
int drawable abc_tab_indicator_material 0x7f070043
int drawable abc_tab_indicator_mtrl_alpha 0x7f070044
int drawable abc_text_cursor_material 0x7f070045
int drawable abc_text_select_handle_left_mtrl 0x7f070046
int drawable abc_text_select_handle_middle_mtrl 0x7f070047
int drawable abc_text_select_handle_right_mtrl 0x7f070048
int drawable abc_textfield_activated_mtrl_alpha 0x7f070049
int drawable abc_textfield_default_mtrl_alpha 0x7f07004a
int drawable abc_textfield_search_activated_mtrl_alpha 0x7f07004b
int drawable abc_textfield_search_default_mtrl_alpha 0x7f07004c
int drawable abc_textfield_search_material 0x7f07004d
int drawable abc_vector_test 0x7f07004e
int drawable btn_checkbox_checked_mtrl 0x7f07004f
int drawable btn_checkbox_checked_to_unchecked_mtrl_animation 0x7f070050
int drawable btn_checkbox_unchecked_mtrl 0x7f070051
int drawable btn_checkbox_unchecked_to_checked_mtrl_animation 0x7f070052
int drawable btn_radio_off_mtrl 0x7f070053
int drawable btn_radio_off_to_on_mtrl_animation 0x7f070054
int drawable btn_radio_on_mtrl 0x7f070055
int drawable btn_radio_on_to_off_mtrl_animation 0x7f070056
int drawable ic_add 0x7f070057
int drawable ic_bookmark 0x7f070058
int drawable ic_call_answer 0x7f070059
int drawable ic_call_answer_low 0x7f07005a
int drawable ic_call_answer_video 0x7f07005b
int drawable ic_call_answer_video_low 0x7f07005c
int drawable ic_call_decline 0x7f07005d
int drawable ic_call_decline_low 0x7f07005e
int drawable ic_chevron_down 0x7f07005f
int drawable ic_chevron_left 0x7f070060
int drawable ic_chevron_right 0x7f070061
int drawable ic_chevron_up 0x7f070062
int drawable ic_close 0x7f070063
int drawable ic_delete 0x7f070064
int drawable ic_document_empty 0x7f070065
int drawable ic_error 0x7f070066
int drawable ic_favorite 0x7f070067
int drawable ic_folder 0x7f070068
int drawable ic_folder_empty 0x7f070069
int drawable ic_folder_open 0x7f07006a
int drawable ic_history 0x7f07006b
int drawable ic_home 0x7f07006c
int drawable ic_info 0x7f07006d
int drawable ic_launcher_foreground 0x7f07006e
int drawable ic_menu 0x7f07006f
int drawable ic_more_vert 0x7f070070
int drawable ic_night_mode 0x7f070071
int drawable ic_pdf_document 0x7f070072
int drawable ic_refresh 0x7f070073
int drawable ic_rotate 0x7f070074
int drawable ic_search 0x7f070075
int drawable ic_security 0x7f070076
int drawable ic_settings 0x7f070077
int drawable ic_share 0x7f070078
int drawable ic_sort 0x7f070079
int drawable ic_storage 0x7f07007a
int drawable ic_wifi_off 0x7f07007b
int drawable ic_zoom_in 0x7f07007c
int drawable ic_zoom_out 0x7f07007d
int drawable notification_action_background 0x7f07007e
int drawable notification_bg 0x7f07007f
int drawable notification_bg_low 0x7f070080
int drawable notification_bg_low_normal 0x7f070081
int drawable notification_bg_low_pressed 0x7f070082
int drawable notification_bg_normal 0x7f070083
int drawable notification_bg_normal_pressed 0x7f070084
int drawable notification_icon_background 0x7f070085
int drawable notification_oversize_large_icon_bg 0x7f070086
int drawable notification_template_icon_bg 0x7f070087
int drawable notification_template_icon_low_bg 0x7f070088
int drawable notification_tile_bg 0x7f070089
int drawable notify_panel_notification_icon_bg 0x7f07008a
int drawable test_level_drawable 0x7f07008b
int drawable tooltip_frame_dark 0x7f07008c
int drawable tooltip_frame_light 0x7f07008d
int id ALT 0x7f080000
int id CTRL 0x7f080001
int id FUNCTION 0x7f080002
int id META 0x7f080003
int id NO_DEBUG 0x7f080004
int id SHIFT 0x7f080005
int id SHOW_ALL 0x7f080006
int id SHOW_PATH 0x7f080007
int id SHOW_PROGRESS 0x7f080008
int id SYM 0x7f080009
int id accelerate 0x7f08000a
int id accessibility_action_clickable_span 0x7f08000b
int id accessibility_custom_action_0 0x7f08000c
int id accessibility_custom_action_1 0x7f08000d
int id accessibility_custom_action_10 0x7f08000e
int id accessibility_custom_action_11 0x7f08000f
int id accessibility_custom_action_12 0x7f080010
int id accessibility_custom_action_13 0x7f080011
int id accessibility_custom_action_14 0x7f080012
int id accessibility_custom_action_15 0x7f080013
int id accessibility_custom_action_16 0x7f080014
int id accessibility_custom_action_17 0x7f080015
int id accessibility_custom_action_18 0x7f080016
int id accessibility_custom_action_19 0x7f080017
int id accessibility_custom_action_2 0x7f080018
int id accessibility_custom_action_20 0x7f080019
int id accessibility_custom_action_21 0x7f08001a
int id accessibility_custom_action_22 0x7f08001b
int id accessibility_custom_action_23 0x7f08001c
int id accessibility_custom_action_24 0x7f08001d
int id accessibility_custom_action_25 0x7f08001e
int id accessibility_custom_action_26 0x7f08001f
int id accessibility_custom_action_27 0x7f080020
int id accessibility_custom_action_28 0x7f080021
int id accessibility_custom_action_29 0x7f080022
int id accessibility_custom_action_3 0x7f080023
int id accessibility_custom_action_30 0x7f080024
int id accessibility_custom_action_31 0x7f080025
int id accessibility_custom_action_4 0x7f080026
int id accessibility_custom_action_5 0x7f080027
int id accessibility_custom_action_6 0x7f080028
int id accessibility_custom_action_7 0x7f080029
int id accessibility_custom_action_8 0x7f08002a
int id accessibility_custom_action_9 0x7f08002b
int id actionDown 0x7f08002c
int id actionDownUp 0x7f08002d
int id actionUp 0x7f08002e
int id action_about 0x7f08002f
int id action_bar 0x7f080030
int id action_bar_activity_content 0x7f080031
int id action_bar_container 0x7f080032
int id action_bar_root 0x7f080033
int id action_bar_spinner 0x7f080034
int id action_bar_subtitle 0x7f080035
int id action_bar_title 0x7f080036
int id action_bookmark 0x7f080037
int id action_bookmarks 0x7f080038
int id action_clear_all 0x7f080039
int id action_container 0x7f08003a
int id action_context_bar 0x7f08003b
int id action_divider 0x7f08003c
int id action_fit_height 0x7f08003d
int id action_fit_width 0x7f08003e
int id action_home 0x7f08003f
int id action_image 0x7f080040
int id action_menu_divider 0x7f080041
int id action_menu_presenter 0x7f080042
int id action_mode_bar 0x7f080043
int id action_mode_bar_stub 0x7f080044
int id action_mode_close_button 0x7f080045
int id action_night_mode 0x7f080046
int id action_refresh 0x7f080047
int id action_rotate 0x7f080048
int id action_search 0x7f080049
int id action_settings 0x7f08004a
int id action_share 0x7f08004b
int id action_sort 0x7f08004c
int id action_text 0x7f08004d
int id actions 0x7f08004e
int id activity_chooser_view_content 0x7f08004f
int id add 0x7f080050
int id alertTitle 0x7f080051
int id aligned 0x7f080052
int id allStates 0x7f080053
int id always 0x7f080054
int id animateToEnd 0x7f080055
int id animateToStart 0x7f080056
int id antiClockwise 0x7f080057
int id anticipate 0x7f080058
int id asConfigured 0x7f080059
int id async 0x7f08005a
int id auto 0x7f08005b
int id autoComplete 0x7f08005c
int id autoCompleteToEnd 0x7f08005d
int id autoCompleteToStart 0x7f08005e
int id barrier 0x7f08005f
int id baseline 0x7f080060
int id beginOnFirstDraw 0x7f080061
int id beginning 0x7f080062
int id bestChoice 0x7f080063
int id blocking 0x7f080064
int id bottom 0x7f080065
int id bounce 0x7f080066
int id bounceBoth 0x7f080067
int id bounceEnd 0x7f080068
int id bounceStart 0x7f080069
int id buttonPanel 0x7f08006a
int id cache_measures 0x7f08006b
int id callMeasure 0x7f08006c
int id carryVelocity 0x7f08006d
int id center 0x7f08006e
int id center_vertical 0x7f08006f
int id chain 0x7f080070
int id chain2 0x7f080071
int id chains 0x7f080072
int id checkbox 0x7f080073
int id checked 0x7f080074
int id chronometer 0x7f080075
int id clockwise 0x7f080076
int id closest 0x7f080077
int id collapseActionView 0x7f080078
int id constraint 0x7f080079
int id content 0x7f08007a
int id contentPanel 0x7f08007b
int id continuousVelocity 0x7f08007c
int id cos 0x7f08007d
int id currentState 0x7f08007e
int id custom 0x7f08007f
int id customPanel 0x7f080080
int id decelerate 0x7f080081
int id decelerateAndComplete 0x7f080082
int id decor_content_parent 0x7f080083
int id default_activity_button 0x7f080084
int id deltaRelative 0x7f080085
int id dependency_ordering 0x7f080086
int id dialog_button 0x7f080087
int id dimensions 0x7f080088
int id direct 0x7f080089
int id disableHome 0x7f08008a
int id disableIntraAutoTransition 0x7f08008b
int id disablePostScroll 0x7f08008c
int id disableScroll 0x7f08008d
int id dragAnticlockwise 0x7f08008e
int id dragClockwise 0x7f08008f
int id dragDown 0x7f080090
int id dragEnd 0x7f080091
int id dragLeft 0x7f080092
int id dragRight 0x7f080093
int id dragStart 0x7f080094
int id dragUp 0x7f080095
int id easeIn 0x7f080096
int id easeInOut 0x7f080097
int id easeOut 0x7f080098
int id east 0x7f080099
int id edit_query 0x7f08009a
int id edit_text_id 0x7f08009b
int id emptyStateLayout 0x7f08009c
int id end 0x7f08009d
int id expand_activities_button 0x7f08009e
int id expanded_menu 0x7f08009f
int id flip 0x7f0800a0
int id forever 0x7f0800a1
int id fragment_container_view_tag 0x7f0800a2
int id frost 0x7f0800a3
int id gone 0x7f0800a4
int id graph 0x7f0800a5
int id graph_wrap 0x7f0800a6
int id group_divider 0x7f0800a7
int id grouping 0x7f0800a8
int id groups 0x7f0800a9
int id hide_ime_id 0x7f0800aa
int id home 0x7f0800ab
int id homeAsUp 0x7f0800ac
int id honorRequest 0x7f0800ad
int id horizontal_only 0x7f0800ae
int id icon 0x7f0800af
int id icon_group 0x7f0800b0
int id ifRoom 0x7f0800b1
int id ignore 0x7f0800b2
int id ignoreRequest 0x7f0800b3
int id image 0x7f0800b4
int id immediateStop 0x7f0800b5
int id included 0x7f0800b6
int id info 0x7f0800b7
int id invisible 0x7f0800b8
int id is_pooling_container_tag 0x7f0800b9
int id italic 0x7f0800ba
int id item_touch_helper_previous_elevation 0x7f0800bb
int id jumpToEnd 0x7f0800bc
int id jumpToStart 0x7f0800bd
int id layout 0x7f0800be
int id left 0x7f0800bf
int id legacy 0x7f0800c0
int id line1 0x7f0800c1
int id line3 0x7f0800c2
int id linear 0x7f0800c3
int id listMode 0x7f0800c4
int id list_item 0x7f0800c5
int id match_constraint 0x7f0800c6
int id match_parent 0x7f0800c7
int id message 0x7f0800c8
int id middle 0x7f0800c9
int id motion_base 0x7f0800ca
int id multiply 0x7f0800cb
int id nav_favorites 0x7f0800cc
int id nav_home 0x7f0800cd
int id nav_recent 0x7f0800ce
int id nav_settings 0x7f0800cf
int id never 0x7f0800d0
int id neverCompleteToEnd 0x7f0800d1
int id neverCompleteToStart 0x7f0800d2
int id noState 0x7f0800d3
int id none 0x7f0800d4
int id normal 0x7f0800d5
int id north 0x7f0800d6
int id notification_background 0x7f0800d7
int id notification_main_column 0x7f0800d8
int id notification_main_column_container 0x7f0800d9
int id off 0x7f0800da
int id on 0x7f0800db
int id onInterceptTouchReturnSwipe 0x7f0800dc
int id overshoot 0x7f0800dd
int id packed 0x7f0800de
int id parent 0x7f0800df
int id parentPanel 0x7f0800e0
int id parentRelative 0x7f0800e1
int id path 0x7f0800e2
int id pathRelative 0x7f0800e3
int id pdfContainer 0x7f0800e4
int id percent 0x7f0800e5
int id pooling_container_listener_holder_tag 0x7f0800e6
int id position 0x7f0800e7
int id postLayout 0x7f0800e8
int id progress_circular 0x7f0800e9
int id progress_horizontal 0x7f0800ea
int id progress_indicator 0x7f0800eb
int id radio 0x7f0800ec
int id ratio 0x7f0800ed
int id rectangles 0x7f0800ee
int id recyclerViewBookmarks 0x7f0800ef
int id recyclerViewFiles 0x7f0800f0
int id recyclerViewRecentFiles 0x7f0800f1
int id reverseSawtooth 0x7f0800f2
int id right 0x7f0800f3
int id right_icon 0x7f0800f4
int id right_side 0x7f0800f5
int id sawtooth 0x7f0800f6
int id screen 0x7f0800f7
int id scrollIndicatorDown 0x7f0800f8
int id scrollIndicatorUp 0x7f0800f9
int id scrollView 0x7f0800fa
int id search_badge 0x7f0800fb
int id search_bar 0x7f0800fc
int id search_button 0x7f0800fd
int id search_close_btn 0x7f0800fe
int id search_edit_frame 0x7f0800ff
int id search_go_btn 0x7f080100
int id search_mag_icon 0x7f080101
int id search_plate 0x7f080102
int id search_src_text 0x7f080103
int id search_voice_btn 0x7f080104
int id select_dialog_listview 0x7f080105
int id sharedValueSet 0x7f080106
int id sharedValueUnset 0x7f080107
int id shortcut 0x7f080108
int id showCustom 0x7f080109
int id showHome 0x7f08010a
int id showTitle 0x7f08010b
int id sin 0x7f08010c
int id skipped 0x7f08010d
int id south 0x7f08010e
int id spacer 0x7f08010f
int id special_effects_controller_view_tag 0x7f080110
int id spline 0x7f080111
int id split_action_bar 0x7f080112
int id spread 0x7f080113
int id spread_inside 0x7f080114
int id spring 0x7f080115
int id square 0x7f080116
int id src_atop 0x7f080117
int id src_in 0x7f080118
int id src_over 0x7f080119
int id standard 0x7f08011a
int id start 0x7f08011b
int id startHorizontal 0x7f08011c
int id startVertical 0x7f08011d
int id staticLayout 0x7f08011e
int id staticPostLayout 0x7f08011f
int id stop 0x7f080120
int id submenuarrow 0x7f080121
int id submit_area 0x7f080122
int id supportScrollUp 0x7f080123
int id tabMode 0x7f080124
int id tag_accessibility_actions 0x7f080125
int id tag_accessibility_clickable_spans 0x7f080126
int id tag_accessibility_heading 0x7f080127
int id tag_accessibility_pane_title 0x7f080128
int id tag_on_apply_window_listener 0x7f080129
int id tag_on_receive_content_listener 0x7f08012a
int id tag_on_receive_content_mime_types 0x7f08012b
int id tag_screen_reader_focusable 0x7f08012c
int id tag_state_description 0x7f08012d
int id tag_transition_group 0x7f08012e
int id tag_unhandled_key_event_manager 0x7f08012f
int id tag_unhandled_key_listeners 0x7f080130
int id tag_window_insets_animation_callback 0x7f080131
int id text 0x7f080132
int id text2 0x7f080133
int id textSpacerNoButtons 0x7f080134
int id textSpacerNoTitle 0x7f080135
int id textViewBookmarkPage 0x7f080136
int id textViewBookmarkTitle 0x7f080137
int id textViewCurrentPath 0x7f080138
int id textViewFileName 0x7f080139
int id textViewFileSize 0x7f08013a
int id textViewPdfPath 0x7f08013b
int id textViewPdfPlaceholder 0x7f08013c
int id textViewPdfTitle 0x7f08013d
int id time 0x7f08013e
int id title 0x7f08013f
int id titleDividerNoCustom 0x7f080140
int id title_template 0x7f080141
int id toggle 0x7f080142
int id toolbar 0x7f080143
int id top 0x7f080144
int id topPanel 0x7f080145
int id transitionToEnd 0x7f080146
int id transitionToStart 0x7f080147
int id triangle 0x7f080148
int id tv_loading_message 0x7f080149
int id unchecked 0x7f08014a
int id uniform 0x7f08014b
int id up 0x7f08014c
int id useLogo 0x7f08014d
int id vertical_only 0x7f08014e
int id view_transition 0x7f08014f
int id view_tree_lifecycle_owner 0x7f080150
int id view_tree_on_back_pressed_dispatcher_owner 0x7f080151
int id view_tree_saved_state_registry_owner 0x7f080152
int id view_tree_view_model_store_owner 0x7f080153
int id visible 0x7f080154
int id visible_removing_fragment_view_tag 0x7f080155
int id west 0x7f080156
int id withText 0x7f080157
int id wrap 0x7f080158
int id wrap_content 0x7f080159
int id wrap_content_constrained 0x7f08015a
int id x_left 0x7f08015b
int id x_right 0x7f08015c
int integer abc_config_activityDefaultDur 0x7f090000
int integer abc_config_activityShortDur 0x7f090001
int integer cancel_button_image_alpha 0x7f090002
int integer config_tooltipAnimTime 0x7f090003
int integer status_bar_notification_info_maxnum 0x7f090004
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_0 0x7f0a0000
int interpolator btn_checkbox_checked_mtrl_animation_interpolator_1 0x7f0a0001
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_0 0x7f0a0002
int interpolator btn_checkbox_unchecked_mtrl_animation_interpolator_1 0x7f0a0003
int interpolator btn_radio_to_off_mtrl_animation_interpolator_0 0x7f0a0004
int interpolator btn_radio_to_on_mtrl_animation_interpolator_0 0x7f0a0005
int interpolator fast_out_slow_in 0x7f0a0006
int layout abc_action_bar_title_item 0x7f0b0000
int layout abc_action_bar_up_container 0x7f0b0001
int layout abc_action_menu_item_layout 0x7f0b0002
int layout abc_action_menu_layout 0x7f0b0003
int layout abc_action_mode_bar 0x7f0b0004
int layout abc_action_mode_close_item_material 0x7f0b0005
int layout abc_activity_chooser_view 0x7f0b0006
int layout abc_activity_chooser_view_list_item 0x7f0b0007
int layout abc_alert_dialog_button_bar_material 0x7f0b0008
int layout abc_alert_dialog_material 0x7f0b0009
int layout abc_alert_dialog_title_material 0x7f0b000a
int layout abc_cascading_menu_item_layout 0x7f0b000b
int layout abc_dialog_title_material 0x7f0b000c
int layout abc_expanded_menu_layout 0x7f0b000d
int layout abc_list_menu_item_checkbox 0x7f0b000e
int layout abc_list_menu_item_icon 0x7f0b000f
int layout abc_list_menu_item_layout 0x7f0b0010
int layout abc_list_menu_item_radio 0x7f0b0011
int layout abc_popup_menu_header_item_layout 0x7f0b0012
int layout abc_popup_menu_item_layout 0x7f0b0013
int layout abc_screen_content_include 0x7f0b0014
int layout abc_screen_simple 0x7f0b0015
int layout abc_screen_simple_overlay_action_mode 0x7f0b0016
int layout abc_screen_toolbar 0x7f0b0017
int layout abc_search_dropdown_item_icons_2line 0x7f0b0018
int layout abc_search_view 0x7f0b0019
int layout abc_select_dialog_material 0x7f0b001a
int layout abc_tooltip 0x7f0b001b
int layout activity_bookmarks 0x7f0b001c
int layout activity_file_explorer 0x7f0b001d
int layout activity_main 0x7f0b001e
int layout activity_pdf_reader 0x7f0b001f
int layout activity_settings 0x7f0b0020
int layout custom_dialog 0x7f0b0021
int layout ime_base_split_test_activity 0x7f0b0022
int layout ime_secondary_split_test_activity 0x7f0b0023
int layout item_bookmark 0x7f0b0024
int layout item_file 0x7f0b0025
int layout item_pdf_document 0x7f0b0026
int layout layout_loading 0x7f0b0027
int layout notification_action 0x7f0b0028
int layout notification_action_tombstone 0x7f0b0029
int layout notification_template_custom_big 0x7f0b002a
int layout notification_template_icon_group 0x7f0b002b
int layout notification_template_part_chronometer 0x7f0b002c
int layout notification_template_part_time 0x7f0b002d
int layout select_dialog_item_material 0x7f0b002e
int layout select_dialog_multichoice_material 0x7f0b002f
int layout select_dialog_singlechoice_material 0x7f0b0030
int layout support_simple_spinner_dropdown_item 0x7f0b0031
int menu bookmarks_menu 0x7f0c0000
int menu bottom_navigation_menu 0x7f0c0001
int menu file_explorer_menu 0x7f0c0002
int menu main_menu 0x7f0c0003
int menu pdf_reader_menu 0x7f0c0004
int mipmap ic_launcher 0x7f0d0000
int mipmap ic_launcher_round 0x7f0d0001
int string abc_action_bar_home_description 0x7f0e0000
int string abc_action_bar_up_description 0x7f0e0001
int string abc_action_menu_overflow_description 0x7f0e0002
int string abc_action_mode_done 0x7f0e0003
int string abc_activity_chooser_view_see_all 0x7f0e0004
int string abc_activitychooserview_choose_application 0x7f0e0005
int string abc_capital_off 0x7f0e0006
int string abc_capital_on 0x7f0e0007
int string abc_menu_alt_shortcut_label 0x7f0e0008
int string abc_menu_ctrl_shortcut_label 0x7f0e0009
int string abc_menu_delete_shortcut_label 0x7f0e000a
int string abc_menu_enter_shortcut_label 0x7f0e000b
int string abc_menu_function_shortcut_label 0x7f0e000c
int string abc_menu_meta_shortcut_label 0x7f0e000d
int string abc_menu_shift_shortcut_label 0x7f0e000e
int string abc_menu_space_shortcut_label 0x7f0e000f
int string abc_menu_sym_shortcut_label 0x7f0e0010
int string abc_prepend_shortcut_label 0x7f0e0011
int string abc_search_hint 0x7f0e0012
int string abc_searchview_description_clear 0x7f0e0013
int string abc_searchview_description_query 0x7f0e0014
int string abc_searchview_description_search 0x7f0e0015
int string abc_searchview_description_submit 0x7f0e0016
int string abc_searchview_description_voice 0x7f0e0017
int string abc_shareactionprovider_share_with 0x7f0e0018
int string abc_shareactionprovider_share_with_application 0x7f0e0019
int string abc_toolbar_collapse_description 0x7f0e001a
int string action_bookmark 0x7f0e001b
int string action_close 0x7f0e001c
int string action_refresh 0x7f0e001d
int string action_search 0x7f0e001e
int string action_settings 0x7f0e001f
int string action_share 0x7f0e0020
int string add_to_favorites 0x7f0e0021
int string androidx_startup 0x7f0e0022
int string app_description 0x7f0e0023
int string app_name 0x7f0e0024
int string bookmarks 0x7f0e0025
int string browse_files 0x7f0e0026
int string call_notification_answer_action 0x7f0e0027
int string call_notification_answer_video_action 0x7f0e0028
int string call_notification_decline_action 0x7f0e0029
int string call_notification_hang_up_action 0x7f0e002a
int string call_notification_incoming_text 0x7f0e002b
int string call_notification_ongoing_text 0x7f0e002c
int string call_notification_screening_text 0x7f0e002d
int string cancel 0x7f0e002e
int string error 0x7f0e002f
int string error_file_access 0x7f0e0030
int string error_generic 0x7f0e0031
int string error_network 0x7f0e0032
int string error_permission_denied 0x7f0e0033
int string file_cannot_open 0x7f0e0034
int string file_corrupted 0x7f0e0035
int string file_explorer 0x7f0e0036
int string file_not_found 0x7f0e0037
int string loading 0x7f0e0038
int string loading_document 0x7f0e0039
int string nav_favorites 0x7f0e003a
int string nav_home 0x7f0e003b
int string nav_recent 0x7f0e003c
int string nav_settings 0x7f0e003d
int string no 0x7f0e003e
int string no_bookmarks 0x7f0e003f
int string no_files_found 0x7f0e0040
int string no_recent_files 0x7f0e0041
int string no_recent_files_desc 0x7f0e0042
int string no_search_results 0x7f0e0043
int string ok 0x7f0e0044
int string open_pdf 0x7f0e0045
int string page_indicator 0x7f0e0046
int string permission_deny 0x7f0e0047
int string permission_grant 0x7f0e0048
int string permission_storage_message 0x7f0e0049
int string permission_storage_title 0x7f0e004a
int string recent_files 0x7f0e004b
int string remove_from_favorites 0x7f0e004c
int string retry 0x7f0e004d
int string search_hint 0x7f0e004e
int string search_menu_title 0x7f0e004f
int string search_results 0x7f0e0050
int string settings_about 0x7f0e0051
int string settings_appearance 0x7f0e0052
int string settings_reading 0x7f0e0053
int string settings_theme 0x7f0e0054
int string share_pdf 0x7f0e0055
int string status_bar_notification_info_overflow 0x7f0e0056
int string success 0x7f0e0057
int string theme_dark 0x7f0e0058
int string theme_light 0x7f0e0059
int string theme_system 0x7f0e005a
int string welcome_message 0x7f0e005b
int string welcome_subtitle 0x7f0e005c
int string welcome_title 0x7f0e005d
int string yes 0x7f0e005e
int string zoom_level 0x7f0e005f
int style AlertDialog_AppCompat 0x7f0f0000
int style AlertDialog_AppCompat_Light 0x7f0f0001
int style Animation_AppCompat_Dialog 0x7f0f0002
int style Animation_AppCompat_DropDownUp 0x7f0f0003
int style Animation_AppCompat_Tooltip 0x7f0f0004
int style Base_AlertDialog_AppCompat 0x7f0f0005
int style Base_AlertDialog_AppCompat_Light 0x7f0f0006
int style Base_Animation_AppCompat_Dialog 0x7f0f0007
int style Base_Animation_AppCompat_DropDownUp 0x7f0f0008
int style Base_Animation_AppCompat_Tooltip 0x7f0f0009
int style Base_DialogWindowTitle_AppCompat 0x7f0f000a
int style Base_DialogWindowTitleBackground_AppCompat 0x7f0f000b
int style Base_TextAppearance_AppCompat 0x7f0f000c
int style Base_TextAppearance_AppCompat_Body1 0x7f0f000d
int style Base_TextAppearance_AppCompat_Body2 0x7f0f000e
int style Base_TextAppearance_AppCompat_Button 0x7f0f000f
int style Base_TextAppearance_AppCompat_Caption 0x7f0f0010
int style Base_TextAppearance_AppCompat_Display1 0x7f0f0011
int style Base_TextAppearance_AppCompat_Display2 0x7f0f0012
int style Base_TextAppearance_AppCompat_Display3 0x7f0f0013
int style Base_TextAppearance_AppCompat_Display4 0x7f0f0014
int style Base_TextAppearance_AppCompat_Headline 0x7f0f0015
int style Base_TextAppearance_AppCompat_Inverse 0x7f0f0016
int style Base_TextAppearance_AppCompat_Large 0x7f0f0017
int style Base_TextAppearance_AppCompat_Large_Inverse 0x7f0f0018
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0f0019
int style Base_TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0f001a
int style Base_TextAppearance_AppCompat_Medium 0x7f0f001b
int style Base_TextAppearance_AppCompat_Medium_Inverse 0x7f0f001c
int style Base_TextAppearance_AppCompat_Menu 0x7f0f001d
int style Base_TextAppearance_AppCompat_SearchResult 0x7f0f001e
int style Base_TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0f001f
int style Base_TextAppearance_AppCompat_SearchResult_Title 0x7f0f0020
int style Base_TextAppearance_AppCompat_Small 0x7f0f0021
int style Base_TextAppearance_AppCompat_Small_Inverse 0x7f0f0022
int style Base_TextAppearance_AppCompat_Subhead 0x7f0f0023
int style Base_TextAppearance_AppCompat_Subhead_Inverse 0x7f0f0024
int style Base_TextAppearance_AppCompat_Title 0x7f0f0025
int style Base_TextAppearance_AppCompat_Title_Inverse 0x7f0f0026
int style Base_TextAppearance_AppCompat_Tooltip 0x7f0f0027
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0f0028
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0f0029
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0f002a
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0f002b
int style Base_TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0f002c
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0f002d
int style Base_TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0f002e
int style Base_TextAppearance_AppCompat_Widget_Button 0x7f0f002f
int style Base_TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0f0030
int style Base_TextAppearance_AppCompat_Widget_Button_Colored 0x7f0f0031
int style Base_TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0f0032
int style Base_TextAppearance_AppCompat_Widget_DropDownItem 0x7f0f0033
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0f0034
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0f0035
int style Base_TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0f0036
int style Base_TextAppearance_AppCompat_Widget_Switch 0x7f0f0037
int style Base_TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0f0038
int style Base_TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0f0039
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0f003a
int style Base_TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0f003b
int style Base_Theme_AppCompat 0x7f0f003c
int style Base_Theme_AppCompat_CompactMenu 0x7f0f003d
int style Base_Theme_AppCompat_Dialog 0x7f0f003e
int style Base_Theme_AppCompat_Dialog_Alert 0x7f0f003f
int style Base_Theme_AppCompat_Dialog_FixedSize 0x7f0f0040
int style Base_Theme_AppCompat_Dialog_MinWidth 0x7f0f0041
int style Base_Theme_AppCompat_DialogWhenLarge 0x7f0f0042
int style Base_Theme_AppCompat_Light 0x7f0f0043
int style Base_Theme_AppCompat_Light_DarkActionBar 0x7f0f0044
int style Base_Theme_AppCompat_Light_Dialog 0x7f0f0045
int style Base_Theme_AppCompat_Light_Dialog_Alert 0x7f0f0046
int style Base_Theme_AppCompat_Light_Dialog_FixedSize 0x7f0f0047
int style Base_Theme_AppCompat_Light_Dialog_MinWidth 0x7f0f0048
int style Base_Theme_AppCompat_Light_DialogWhenLarge 0x7f0f0049
int style Base_ThemeOverlay_AppCompat 0x7f0f004a
int style Base_ThemeOverlay_AppCompat_ActionBar 0x7f0f004b
int style Base_ThemeOverlay_AppCompat_Dark 0x7f0f004c
int style Base_ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0f004d
int style Base_ThemeOverlay_AppCompat_Dialog 0x7f0f004e
int style Base_ThemeOverlay_AppCompat_Dialog_Alert 0x7f0f004f
int style Base_ThemeOverlay_AppCompat_Light 0x7f0f0050
int style Base_V21_Theme_AppCompat 0x7f0f0051
int style Base_V21_Theme_AppCompat_Dialog 0x7f0f0052
int style Base_V21_Theme_AppCompat_Light 0x7f0f0053
int style Base_V21_Theme_AppCompat_Light_Dialog 0x7f0f0054
int style Base_V21_ThemeOverlay_AppCompat_Dialog 0x7f0f0055
int style Base_V22_Theme_AppCompat 0x7f0f0056
int style Base_V22_Theme_AppCompat_Light 0x7f0f0057
int style Base_V23_Theme_AppCompat 0x7f0f0058
int style Base_V23_Theme_AppCompat_Light 0x7f0f0059
int style Base_V26_Theme_AppCompat 0x7f0f005a
int style Base_V26_Theme_AppCompat_Light 0x7f0f005b
int style Base_V26_Widget_AppCompat_Toolbar 0x7f0f005c
int style Base_V28_Theme_AppCompat 0x7f0f005d
int style Base_V28_Theme_AppCompat_Light 0x7f0f005e
int style Base_V7_Theme_AppCompat 0x7f0f005f
int style Base_V7_Theme_AppCompat_Dialog 0x7f0f0060
int style Base_V7_Theme_AppCompat_Light 0x7f0f0061
int style Base_V7_Theme_AppCompat_Light_Dialog 0x7f0f0062
int style Base_V7_ThemeOverlay_AppCompat_Dialog 0x7f0f0063
int style Base_V7_Widget_AppCompat_AutoCompleteTextView 0x7f0f0064
int style Base_V7_Widget_AppCompat_EditText 0x7f0f0065
int style Base_V7_Widget_AppCompat_Toolbar 0x7f0f0066
int style Base_Widget_AppCompat_ActionBar 0x7f0f0067
int style Base_Widget_AppCompat_ActionBar_Solid 0x7f0f0068
int style Base_Widget_AppCompat_ActionBar_TabBar 0x7f0f0069
int style Base_Widget_AppCompat_ActionBar_TabText 0x7f0f006a
int style Base_Widget_AppCompat_ActionBar_TabView 0x7f0f006b
int style Base_Widget_AppCompat_ActionButton 0x7f0f006c
int style Base_Widget_AppCompat_ActionButton_CloseMode 0x7f0f006d
int style Base_Widget_AppCompat_ActionButton_Overflow 0x7f0f006e
int style Base_Widget_AppCompat_ActionMode 0x7f0f006f
int style Base_Widget_AppCompat_ActivityChooserView 0x7f0f0070
int style Base_Widget_AppCompat_AutoCompleteTextView 0x7f0f0071
int style Base_Widget_AppCompat_Button 0x7f0f0072
int style Base_Widget_AppCompat_Button_Borderless 0x7f0f0073
int style Base_Widget_AppCompat_Button_Borderless_Colored 0x7f0f0074
int style Base_Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0f0075
int style Base_Widget_AppCompat_Button_Colored 0x7f0f0076
int style Base_Widget_AppCompat_Button_Small 0x7f0f0077
int style Base_Widget_AppCompat_ButtonBar 0x7f0f0078
int style Base_Widget_AppCompat_ButtonBar_AlertDialog 0x7f0f0079
int style Base_Widget_AppCompat_CompoundButton_CheckBox 0x7f0f007a
int style Base_Widget_AppCompat_CompoundButton_RadioButton 0x7f0f007b
int style Base_Widget_AppCompat_CompoundButton_Switch 0x7f0f007c
int style Base_Widget_AppCompat_DrawerArrowToggle 0x7f0f007d
int style Base_Widget_AppCompat_DrawerArrowToggle_Common 0x7f0f007e
int style Base_Widget_AppCompat_DropDownItem_Spinner 0x7f0f007f
int style Base_Widget_AppCompat_EditText 0x7f0f0080
int style Base_Widget_AppCompat_ImageButton 0x7f0f0081
int style Base_Widget_AppCompat_Light_ActionBar 0x7f0f0082
int style Base_Widget_AppCompat_Light_ActionBar_Solid 0x7f0f0083
int style Base_Widget_AppCompat_Light_ActionBar_TabBar 0x7f0f0084
int style Base_Widget_AppCompat_Light_ActionBar_TabText 0x7f0f0085
int style Base_Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0f0086
int style Base_Widget_AppCompat_Light_ActionBar_TabView 0x7f0f0087
int style Base_Widget_AppCompat_Light_PopupMenu 0x7f0f0088
int style Base_Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0f0089
int style Base_Widget_AppCompat_ListMenuView 0x7f0f008a
int style Base_Widget_AppCompat_ListPopupWindow 0x7f0f008b
int style Base_Widget_AppCompat_ListView 0x7f0f008c
int style Base_Widget_AppCompat_ListView_DropDown 0x7f0f008d
int style Base_Widget_AppCompat_ListView_Menu 0x7f0f008e
int style Base_Widget_AppCompat_PopupMenu 0x7f0f008f
int style Base_Widget_AppCompat_PopupMenu_Overflow 0x7f0f0090
int style Base_Widget_AppCompat_PopupWindow 0x7f0f0091
int style Base_Widget_AppCompat_ProgressBar 0x7f0f0092
int style Base_Widget_AppCompat_ProgressBar_Horizontal 0x7f0f0093
int style Base_Widget_AppCompat_RatingBar 0x7f0f0094
int style Base_Widget_AppCompat_RatingBar_Indicator 0x7f0f0095
int style Base_Widget_AppCompat_RatingBar_Small 0x7f0f0096
int style Base_Widget_AppCompat_SearchView 0x7f0f0097
int style Base_Widget_AppCompat_SearchView_ActionBar 0x7f0f0098
int style Base_Widget_AppCompat_SeekBar 0x7f0f0099
int style Base_Widget_AppCompat_SeekBar_Discrete 0x7f0f009a
int style Base_Widget_AppCompat_Spinner 0x7f0f009b
int style Base_Widget_AppCompat_Spinner_Underlined 0x7f0f009c
int style Base_Widget_AppCompat_TextView 0x7f0f009d
int style Base_Widget_AppCompat_TextView_SpinnerItem 0x7f0f009e
int style Base_Widget_AppCompat_Toolbar 0x7f0f009f
int style Base_Widget_AppCompat_Toolbar_Button_Navigation 0x7f0f00a0
int style Platform_AppCompat 0x7f0f00a1
int style Platform_AppCompat_Light 0x7f0f00a2
int style Platform_ThemeOverlay_AppCompat 0x7f0f00a3
int style Platform_ThemeOverlay_AppCompat_Dark 0x7f0f00a4
int style Platform_ThemeOverlay_AppCompat_Light 0x7f0f00a5
int style Platform_V21_AppCompat 0x7f0f00a6
int style Platform_V21_AppCompat_Light 0x7f0f00a7
int style Platform_V25_AppCompat 0x7f0f00a8
int style Platform_V25_AppCompat_Light 0x7f0f00a9
int style Platform_Widget_AppCompat_Spinner 0x7f0f00aa
int style RtlOverlay_DialogWindowTitle_AppCompat 0x7f0f00ab
int style RtlOverlay_Widget_AppCompat_ActionBar_TitleItem 0x7f0f00ac
int style RtlOverlay_Widget_AppCompat_DialogTitle_Icon 0x7f0f00ad
int style RtlOverlay_Widget_AppCompat_PopupMenuItem 0x7f0f00ae
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_InternalGroup 0x7f0f00af
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Shortcut 0x7f0f00b0
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_SubmenuArrow 0x7f0f00b1
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Text 0x7f0f00b2
int style RtlOverlay_Widget_AppCompat_PopupMenuItem_Title 0x7f0f00b3
int style RtlOverlay_Widget_AppCompat_Search_DropDown 0x7f0f00b4
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon1 0x7f0f00b5
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Icon2 0x7f0f00b6
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Query 0x7f0f00b7
int style RtlOverlay_Widget_AppCompat_Search_DropDown_Text 0x7f0f00b8
int style RtlOverlay_Widget_AppCompat_SearchView_MagIcon 0x7f0f00b9
int style RtlUnderlay_Widget_AppCompat_ActionButton 0x7f0f00ba
int style RtlUnderlay_Widget_AppCompat_ActionButton_Overflow 0x7f0f00bb
int style TextAppearance_AppCompat 0x7f0f00bc
int style TextAppearance_AppCompat_Body1 0x7f0f00bd
int style TextAppearance_AppCompat_Body2 0x7f0f00be
int style TextAppearance_AppCompat_Button 0x7f0f00bf
int style TextAppearance_AppCompat_Caption 0x7f0f00c0
int style TextAppearance_AppCompat_Display1 0x7f0f00c1
int style TextAppearance_AppCompat_Display2 0x7f0f00c2
int style TextAppearance_AppCompat_Display3 0x7f0f00c3
int style TextAppearance_AppCompat_Display4 0x7f0f00c4
int style TextAppearance_AppCompat_Headline 0x7f0f00c5
int style TextAppearance_AppCompat_Inverse 0x7f0f00c6
int style TextAppearance_AppCompat_Large 0x7f0f00c7
int style TextAppearance_AppCompat_Large_Inverse 0x7f0f00c8
int style TextAppearance_AppCompat_Light_SearchResult_Subtitle 0x7f0f00c9
int style TextAppearance_AppCompat_Light_SearchResult_Title 0x7f0f00ca
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Large 0x7f0f00cb
int style TextAppearance_AppCompat_Light_Widget_PopupMenu_Small 0x7f0f00cc
int style TextAppearance_AppCompat_Medium 0x7f0f00cd
int style TextAppearance_AppCompat_Medium_Inverse 0x7f0f00ce
int style TextAppearance_AppCompat_Menu 0x7f0f00cf
int style TextAppearance_AppCompat_SearchResult_Subtitle 0x7f0f00d0
int style TextAppearance_AppCompat_SearchResult_Title 0x7f0f00d1
int style TextAppearance_AppCompat_Small 0x7f0f00d2
int style TextAppearance_AppCompat_Small_Inverse 0x7f0f00d3
int style TextAppearance_AppCompat_Subhead 0x7f0f00d4
int style TextAppearance_AppCompat_Subhead_Inverse 0x7f0f00d5
int style TextAppearance_AppCompat_Title 0x7f0f00d6
int style TextAppearance_AppCompat_Title_Inverse 0x7f0f00d7
int style TextAppearance_AppCompat_Tooltip 0x7f0f00d8
int style TextAppearance_AppCompat_Widget_ActionBar_Menu 0x7f0f00d9
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle 0x7f0f00da
int style TextAppearance_AppCompat_Widget_ActionBar_Subtitle_Inverse 0x7f0f00db
int style TextAppearance_AppCompat_Widget_ActionBar_Title 0x7f0f00dc
int style TextAppearance_AppCompat_Widget_ActionBar_Title_Inverse 0x7f0f00dd
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle 0x7f0f00de
int style TextAppearance_AppCompat_Widget_ActionMode_Subtitle_Inverse 0x7f0f00df
int style TextAppearance_AppCompat_Widget_ActionMode_Title 0x7f0f00e0
int style TextAppearance_AppCompat_Widget_ActionMode_Title_Inverse 0x7f0f00e1
int style TextAppearance_AppCompat_Widget_Button 0x7f0f00e2
int style TextAppearance_AppCompat_Widget_Button_Borderless_Colored 0x7f0f00e3
int style TextAppearance_AppCompat_Widget_Button_Colored 0x7f0f00e4
int style TextAppearance_AppCompat_Widget_Button_Inverse 0x7f0f00e5
int style TextAppearance_AppCompat_Widget_DropDownItem 0x7f0f00e6
int style TextAppearance_AppCompat_Widget_PopupMenu_Header 0x7f0f00e7
int style TextAppearance_AppCompat_Widget_PopupMenu_Large 0x7f0f00e8
int style TextAppearance_AppCompat_Widget_PopupMenu_Small 0x7f0f00e9
int style TextAppearance_AppCompat_Widget_Switch 0x7f0f00ea
int style TextAppearance_AppCompat_Widget_TextView_SpinnerItem 0x7f0f00eb
int style TextAppearance_Compat_Notification 0x7f0f00ec
int style TextAppearance_Compat_Notification_Info 0x7f0f00ed
int style TextAppearance_Compat_Notification_Line2 0x7f0f00ee
int style TextAppearance_Compat_Notification_Time 0x7f0f00ef
int style TextAppearance_Compat_Notification_Title 0x7f0f00f0
int style TextAppearance_Widget_AppCompat_ExpandedMenu_Item 0x7f0f00f1
int style TextAppearance_Widget_AppCompat_Toolbar_Subtitle 0x7f0f00f2
int style TextAppearance_Widget_AppCompat_Toolbar_Title 0x7f0f00f3
int style Theme_AppCompat 0x7f0f00f4
int style Theme_AppCompat_CompactMenu 0x7f0f00f5
int style Theme_AppCompat_DayNight 0x7f0f00f6
int style Theme_AppCompat_DayNight_DarkActionBar 0x7f0f00f7
int style Theme_AppCompat_DayNight_Dialog 0x7f0f00f8
int style Theme_AppCompat_DayNight_Dialog_Alert 0x7f0f00f9
int style Theme_AppCompat_DayNight_Dialog_MinWidth 0x7f0f00fa
int style Theme_AppCompat_DayNight_DialogWhenLarge 0x7f0f00fb
int style Theme_AppCompat_DayNight_NoActionBar 0x7f0f00fc
int style Theme_AppCompat_Dialog 0x7f0f00fd
int style Theme_AppCompat_Dialog_Alert 0x7f0f00fe
int style Theme_AppCompat_Dialog_MinWidth 0x7f0f00ff
int style Theme_AppCompat_DialogWhenLarge 0x7f0f0100
int style Theme_AppCompat_Empty 0x7f0f0101
int style Theme_AppCompat_Light 0x7f0f0102
int style Theme_AppCompat_Light_DarkActionBar 0x7f0f0103
int style Theme_AppCompat_Light_Dialog 0x7f0f0104
int style Theme_AppCompat_Light_Dialog_Alert 0x7f0f0105
int style Theme_AppCompat_Light_Dialog_MinWidth 0x7f0f0106
int style Theme_AppCompat_Light_DialogWhenLarge 0x7f0f0107
int style Theme_AppCompat_Light_NoActionBar 0x7f0f0108
int style Theme_AppCompat_NoActionBar 0x7f0f0109
int style Theme_CCPPDFReader 0x7f0f010a
int style Theme_CCPPDFReader_FullScreen 0x7f0f010b
int style Theme_CCPPDFReader_Splash 0x7f0f010c
int style ThemeOverlay_AppCompat 0x7f0f010d
int style ThemeOverlay_AppCompat_ActionBar 0x7f0f010e
int style ThemeOverlay_AppCompat_Dark 0x7f0f010f
int style ThemeOverlay_AppCompat_Dark_ActionBar 0x7f0f0110
int style ThemeOverlay_AppCompat_DayNight 0x7f0f0111
int style ThemeOverlay_AppCompat_DayNight_ActionBar 0x7f0f0112
int style ThemeOverlay_AppCompat_Dialog 0x7f0f0113
int style ThemeOverlay_AppCompat_Dialog_Alert 0x7f0f0114
int style ThemeOverlay_AppCompat_Light 0x7f0f0115
int style Widget_AppCompat_ActionBar 0x7f0f0116
int style Widget_AppCompat_ActionBar_Solid 0x7f0f0117
int style Widget_AppCompat_ActionBar_TabBar 0x7f0f0118
int style Widget_AppCompat_ActionBar_TabText 0x7f0f0119
int style Widget_AppCompat_ActionBar_TabView 0x7f0f011a
int style Widget_AppCompat_ActionButton 0x7f0f011b
int style Widget_AppCompat_ActionButton_CloseMode 0x7f0f011c
int style Widget_AppCompat_ActionButton_Overflow 0x7f0f011d
int style Widget_AppCompat_ActionMode 0x7f0f011e
int style Widget_AppCompat_ActivityChooserView 0x7f0f011f
int style Widget_AppCompat_AutoCompleteTextView 0x7f0f0120
int style Widget_AppCompat_Button 0x7f0f0121
int style Widget_AppCompat_Button_Borderless 0x7f0f0122
int style Widget_AppCompat_Button_Borderless_Colored 0x7f0f0123
int style Widget_AppCompat_Button_ButtonBar_AlertDialog 0x7f0f0124
int style Widget_AppCompat_Button_Colored 0x7f0f0125
int style Widget_AppCompat_Button_Small 0x7f0f0126
int style Widget_AppCompat_ButtonBar 0x7f0f0127
int style Widget_AppCompat_ButtonBar_AlertDialog 0x7f0f0128
int style Widget_AppCompat_CompoundButton_CheckBox 0x7f0f0129
int style Widget_AppCompat_CompoundButton_RadioButton 0x7f0f012a
int style Widget_AppCompat_CompoundButton_Switch 0x7f0f012b
int style Widget_AppCompat_DrawerArrowToggle 0x7f0f012c
int style Widget_AppCompat_DropDownItem_Spinner 0x7f0f012d
int style Widget_AppCompat_EditText 0x7f0f012e
int style Widget_AppCompat_ImageButton 0x7f0f012f
int style Widget_AppCompat_Light_ActionBar 0x7f0f0130
int style Widget_AppCompat_Light_ActionBar_Solid 0x7f0f0131
int style Widget_AppCompat_Light_ActionBar_Solid_Inverse 0x7f0f0132
int style Widget_AppCompat_Light_ActionBar_TabBar 0x7f0f0133
int style Widget_AppCompat_Light_ActionBar_TabBar_Inverse 0x7f0f0134
int style Widget_AppCompat_Light_ActionBar_TabText 0x7f0f0135
int style Widget_AppCompat_Light_ActionBar_TabText_Inverse 0x7f0f0136
int style Widget_AppCompat_Light_ActionBar_TabView 0x7f0f0137
int style Widget_AppCompat_Light_ActionBar_TabView_Inverse 0x7f0f0138
int style Widget_AppCompat_Light_ActionButton 0x7f0f0139
int style Widget_AppCompat_Light_ActionButton_CloseMode 0x7f0f013a
int style Widget_AppCompat_Light_ActionButton_Overflow 0x7f0f013b
int style Widget_AppCompat_Light_ActionMode_Inverse 0x7f0f013c
int style Widget_AppCompat_Light_ActivityChooserView 0x7f0f013d
int style Widget_AppCompat_Light_AutoCompleteTextView 0x7f0f013e
int style Widget_AppCompat_Light_DropDownItem_Spinner 0x7f0f013f
int style Widget_AppCompat_Light_ListPopupWindow 0x7f0f0140
int style Widget_AppCompat_Light_ListView_DropDown 0x7f0f0141
int style Widget_AppCompat_Light_PopupMenu 0x7f0f0142
int style Widget_AppCompat_Light_PopupMenu_Overflow 0x7f0f0143
int style Widget_AppCompat_Light_SearchView 0x7f0f0144
int style Widget_AppCompat_Light_Spinner_DropDown_ActionBar 0x7f0f0145
int style Widget_AppCompat_ListMenuView 0x7f0f0146
int style Widget_AppCompat_ListPopupWindow 0x7f0f0147
int style Widget_AppCompat_ListView 0x7f0f0148
int style Widget_AppCompat_ListView_DropDown 0x7f0f0149
int style Widget_AppCompat_ListView_Menu 0x7f0f014a
int style Widget_AppCompat_PopupMenu 0x7f0f014b
int style Widget_AppCompat_PopupMenu_Overflow 0x7f0f014c
int style Widget_AppCompat_PopupWindow 0x7f0f014d
int style Widget_AppCompat_ProgressBar 0x7f0f014e
int style Widget_AppCompat_ProgressBar_Horizontal 0x7f0f014f
int style Widget_AppCompat_RatingBar 0x7f0f0150
int style Widget_AppCompat_RatingBar_Indicator 0x7f0f0151
int style Widget_AppCompat_RatingBar_Small 0x7f0f0152
int style Widget_AppCompat_SearchView 0x7f0f0153
int style Widget_AppCompat_SearchView_ActionBar 0x7f0f0154
int style Widget_AppCompat_SeekBar 0x7f0f0155
int style Widget_AppCompat_SeekBar_Discrete 0x7f0f0156
int style Widget_AppCompat_Spinner 0x7f0f0157
int style Widget_AppCompat_Spinner_DropDown 0x7f0f0158
int style Widget_AppCompat_Spinner_DropDown_ActionBar 0x7f0f0159
int style Widget_AppCompat_Spinner_Underlined 0x7f0f015a
int style Widget_AppCompat_TextView 0x7f0f015b
int style Widget_AppCompat_TextView_SpinnerItem 0x7f0f015c
int style Widget_AppCompat_Toolbar 0x7f0f015d
int style Widget_AppCompat_Toolbar_Button_Navigation 0x7f0f015e
int style Widget_Compat_NotificationActionContainer 0x7f0f015f
int style Widget_Compat_NotificationActionText 0x7f0f0160
int[] styleable ActionBar { 0x7f03003d, 0x7f03003e, 0x7f03003f, 0x7f030089, 0x7f03008a, 0x7f03008b, 0x7f03008c, 0x7f03008d, 0x7f03008e, 0x7f03009a, 0x7f0300a7, 0x7f0300a8, 0x7f0300c0, 0x7f0300ec, 0x7f0300ed, 0x7f0300ee, 0x7f0300ef, 0x7f0300f0, 0x7f0300fb, 0x7f0300fe, 0x7f030151, 0x7f030177, 0x7f030196, 0x7f030199, 0x7f03019a, 0x7f0301d3, 0x7f0301d6, 0x7f030204, 0x7f03020d }
int styleable ActionBar_background 0
int styleable ActionBar_backgroundSplit 1
int styleable ActionBar_backgroundStacked 2
int styleable ActionBar_contentInsetEnd 3
int styleable ActionBar_contentInsetEndWithActions 4
int styleable ActionBar_contentInsetLeft 5
int styleable ActionBar_contentInsetRight 6
int styleable ActionBar_contentInsetStart 7
int styleable ActionBar_contentInsetStartWithNavigation 8
int styleable ActionBar_customNavigationLayout 9
int styleable ActionBar_displayOptions 10
int styleable ActionBar_divider 11
int styleable ActionBar_elevation 12
int styleable ActionBar_height 13
int styleable ActionBar_hideOnContentScroll 14
int styleable ActionBar_homeAsUpIndicator 15
int styleable ActionBar_homeLayout 16
int styleable ActionBar_icon 17
int styleable ActionBar_indeterminateProgressStyle 18
int styleable ActionBar_itemPadding 19
int styleable ActionBar_logo 20
int styleable ActionBar_navigationMode 21
int styleable ActionBar_popupTheme 22
int styleable ActionBar_progressBarPadding 23
int styleable ActionBar_progressBarStyle 24
int styleable ActionBar_subtitle 25
int styleable ActionBar_subtitleTextStyle 26
int styleable ActionBar_title 27
int styleable ActionBar_titleTextStyle 28
int[] styleable ActionBarLayout { 0x010100b3 }
int styleable ActionBarLayout_android_layout_gravity 0
int[] styleable ActionMenuItemView { 0x0101013f }
int styleable ActionMenuItemView_android_minWidth 0
int[] styleable ActionMenuView { }
int[] styleable ActionMode { 0x7f03003d, 0x7f03003e, 0x7f030071, 0x7f0300ec, 0x7f0301d6, 0x7f03020d }
int styleable ActionMode_background 0
int styleable ActionMode_backgroundSplit 1
int styleable ActionMode_closeItemLayout 2
int styleable ActionMode_height 3
int styleable ActionMode_subtitleTextStyle 4
int styleable ActionMode_titleTextStyle 5
int[] styleable ActivityChooserView { 0x7f0300c2, 0x7f0300fc }
int styleable ActivityChooserView_expandActivityOverflowButtonDrawable 0
int styleable ActivityChooserView_initialActivityCount 1
int[] styleable AlertDialog { 0x010100f2, 0x7f030052, 0x7f030053, 0x7f030146, 0x7f030147, 0x7f030174, 0x7f0301c0, 0x7f0301c1 }
int styleable AlertDialog_android_layout 0
int styleable AlertDialog_buttonIconDimen 1
int styleable AlertDialog_buttonPanelSideLayout 2
int styleable AlertDialog_listItemLayout 3
int styleable AlertDialog_listLayout 4
int styleable AlertDialog_multiChoiceItemLayout 5
int styleable AlertDialog_showTitle 6
int styleable AlertDialog_singleChoiceItemLayout 7
int[] styleable AnimatedStateListDrawableCompat { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable AnimatedStateListDrawableCompat_android_dither 0
int styleable AnimatedStateListDrawableCompat_android_visible 1
int styleable AnimatedStateListDrawableCompat_android_variablePadding 2
int styleable AnimatedStateListDrawableCompat_android_constantSize 3
int styleable AnimatedStateListDrawableCompat_android_enterFadeDuration 4
int styleable AnimatedStateListDrawableCompat_android_exitFadeDuration 5
int[] styleable AnimatedStateListDrawableItem { 0x010100d0, 0x01010199 }
int styleable AnimatedStateListDrawableItem_android_id 0
int styleable AnimatedStateListDrawableItem_android_drawable 1
int[] styleable AnimatedStateListDrawableTransition { 0x01010199, 0x01010449, 0x0101044a, 0x0101044b }
int styleable AnimatedStateListDrawableTransition_android_drawable 0
int styleable AnimatedStateListDrawableTransition_android_toId 1
int styleable AnimatedStateListDrawableTransition_android_fromId 2
int styleable AnimatedStateListDrawableTransition_android_reversible 3
int[] styleable AppCompatEmojiHelper { }
int[] styleable AppCompatImageView { 0x01010119, 0x7f0301cd, 0x7f030202, 0x7f030203 }
int styleable AppCompatImageView_android_src 0
int styleable AppCompatImageView_srcCompat 1
int styleable AppCompatImageView_tint 2
int styleable AppCompatImageView_tintMode 3
int[] styleable AppCompatSeekBar { 0x01010142, 0x7f0301ff, 0x7f030200, 0x7f030201 }
int styleable AppCompatSeekBar_android_thumb 0
int styleable AppCompatSeekBar_tickMark 1
int styleable AppCompatSeekBar_tickMarkTint 2
int styleable AppCompatSeekBar_tickMarkTintMode 3
int[] styleable AppCompatTextHelper { 0x01010034, 0x0101016d, 0x0101016e, 0x0101016f, 0x01010170, 0x01010392, 0x01010393 }
int styleable AppCompatTextHelper_android_textAppearance 0
int styleable AppCompatTextHelper_android_drawableTop 1
int styleable AppCompatTextHelper_android_drawableBottom 2
int styleable AppCompatTextHelper_android_drawableLeft 3
int styleable AppCompatTextHelper_android_drawableRight 4
int styleable AppCompatTextHelper_android_drawableStart 5
int styleable AppCompatTextHelper_android_drawableEnd 6
int[] styleable AppCompatTextView { 0x01010034, 0x7f030037, 0x7f030038, 0x7f030039, 0x7f03003a, 0x7f03003b, 0x7f0300b0, 0x7f0300b1, 0x7f0300b2, 0x7f0300b3, 0x7f0300b5, 0x7f0300b6, 0x7f0300b7, 0x7f0300b8, 0x7f0300c1, 0x7f0300c8, 0x7f0300dd, 0x7f0300e6, 0x7f030101, 0x7f030141, 0x7f0301e0, 0x7f0301f1 }
int styleable AppCompatTextView_android_textAppearance 0
int styleable AppCompatTextView_autoSizeMaxTextSize 1
int styleable AppCompatTextView_autoSizeMinTextSize 2
int styleable AppCompatTextView_autoSizePresetSizes 3
int styleable AppCompatTextView_autoSizeStepGranularity 4
int styleable AppCompatTextView_autoSizeTextType 5
int styleable AppCompatTextView_drawableBottomCompat 6
int styleable AppCompatTextView_drawableEndCompat 7
int styleable AppCompatTextView_drawableLeftCompat 8
int styleable AppCompatTextView_drawableRightCompat 9
int styleable AppCompatTextView_drawableStartCompat 10
int styleable AppCompatTextView_drawableTint 11
int styleable AppCompatTextView_drawableTintMode 12
int styleable AppCompatTextView_drawableTopCompat 13
int styleable AppCompatTextView_emojiCompatEnabled 14
int styleable AppCompatTextView_firstBaselineToTopHeight 15
int styleable AppCompatTextView_fontFamily 16
int styleable AppCompatTextView_fontVariationSettings 17
int styleable AppCompatTextView_lastBaselineToBottomHeight 18
int styleable AppCompatTextView_lineHeight 19
int styleable AppCompatTextView_textAllCaps 20
int styleable AppCompatTextView_textLocale 21
int[] styleable AppCompatTheme { 0x01010057, 0x010100ae, 0x7f030002, 0x7f030003, 0x7f030004, 0x7f030005, 0x7f030006, 0x7f030007, 0x7f030008, 0x7f030009, 0x7f03000a, 0x7f03000b, 0x7f03000c, 0x7f03000d, 0x7f03000e, 0x7f030010, 0x7f030011, 0x7f030012, 0x7f030013, 0x7f030014, 0x7f030015, 0x7f030016, 0x7f030017, 0x7f030018, 0x7f030019, 0x7f03001a, 0x7f03001b, 0x7f03001c, 0x7f03001d, 0x7f03001e, 0x7f03001f, 0x7f030020, 0x7f030021, 0x7f030022, 0x7f030025, 0x7f030026, 0x7f030027, 0x7f030028, 0x7f030029, 0x7f030036, 0x7f030049, 0x7f03004b, 0x7f03004c, 0x7f03004d, 0x7f03004e, 0x7f03004f, 0x7f030054, 0x7f030055, 0x7f030066, 0x7f030067, 0x7f030075, 0x7f030076, 0x7f030077, 0x7f030078, 0x7f030079, 0x7f03007a, 0x7f03007b, 0x7f03007c, 0x7f03007d, 0x7f03007e, 0x7f030090, 0x7f0300a4, 0x7f0300a5, 0x7f0300a6, 0x7f0300a9, 0x7f0300ab, 0x7f0300ba, 0x7f0300bb, 0x7f0300bd, 0x7f0300be, 0x7f0300bf, 0x7f0300ee, 0x7f0300f6, 0x7f030142, 0x7f030143, 0x7f030144, 0x7f030145, 0x7f030148, 0x7f030149, 0x7f03014a, 0x7f03014b, 0x7f03014c, 0x7f03014d, 0x7f03014e, 0x7f03014f, 0x7f030150, 0x7f030188, 0x7f030189, 0x7f03018a, 0x7f030195, 0x7f030197, 0x7f0301a1, 0x7f0301a2, 0x7f0301a3, 0x7f0301a4, 0x7f0301b6, 0x7f0301b7, 0x7f0301b8, 0x7f0301b9, 0x7f0301c5, 0x7f0301c6, 0x7f0301da, 0x7f0301e1, 0x7f0301e2, 0x7f0301e3, 0x7f0301e4, 0x7f0301e5, 0x7f0301e6, 0x7f0301e7, 0x7f0301e8, 0x7f0301ee, 0x7f0301ef, 0x7f03020e, 0x7f03020f, 0x7f030210, 0x7f030211, 0x7f030223, 0x7f030231, 0x7f030232, 0x7f030233, 0x7f030234, 0x7f030235, 0x7f030236, 0x7f030237, 0x7f030238, 0x7f030239, 0x7f03023a }
int styleable AppCompatTheme_android_windowIsFloating 0
int styleable AppCompatTheme_android_windowAnimationStyle 1
int styleable AppCompatTheme_actionBarDivider 2
int styleable AppCompatTheme_actionBarItemBackground 3
int styleable AppCompatTheme_actionBarPopupTheme 4
int styleable AppCompatTheme_actionBarSize 5
int styleable AppCompatTheme_actionBarSplitStyle 6
int styleable AppCompatTheme_actionBarStyle 7
int styleable AppCompatTheme_actionBarTabBarStyle 8
int styleable AppCompatTheme_actionBarTabStyle 9
int styleable AppCompatTheme_actionBarTabTextStyle 10
int styleable AppCompatTheme_actionBarTheme 11
int styleable AppCompatTheme_actionBarWidgetTheme 12
int styleable AppCompatTheme_actionButtonStyle 13
int styleable AppCompatTheme_actionDropDownStyle 14
int styleable AppCompatTheme_actionMenuTextAppearance 15
int styleable AppCompatTheme_actionMenuTextColor 16
int styleable AppCompatTheme_actionModeBackground 17
int styleable AppCompatTheme_actionModeCloseButtonStyle 18
int styleable AppCompatTheme_actionModeCloseContentDescription 19
int styleable AppCompatTheme_actionModeCloseDrawable 20
int styleable AppCompatTheme_actionModeCopyDrawable 21
int styleable AppCompatTheme_actionModeCutDrawable 22
int styleable AppCompatTheme_actionModeFindDrawable 23
int styleable AppCompatTheme_actionModePasteDrawable 24
int styleable AppCompatTheme_actionModePopupWindowStyle 25
int styleable AppCompatTheme_actionModeSelectAllDrawable 26
int styleable AppCompatTheme_actionModeShareDrawable 27
int styleable AppCompatTheme_actionModeSplitBackground 28
int styleable AppCompatTheme_actionModeStyle 29
int styleable AppCompatTheme_actionModeTheme 30
int styleable AppCompatTheme_actionModeWebSearchDrawable 31
int styleable AppCompatTheme_actionOverflowButtonStyle 32
int styleable AppCompatTheme_actionOverflowMenuStyle 33
int styleable AppCompatTheme_activityChooserViewStyle 34
int styleable AppCompatTheme_alertDialogButtonGroupStyle 35
int styleable AppCompatTheme_alertDialogCenterButtons 36
int styleable AppCompatTheme_alertDialogStyle 37
int styleable AppCompatTheme_alertDialogTheme 38
int styleable AppCompatTheme_autoCompleteTextViewStyle 39
int styleable AppCompatTheme_borderlessButtonStyle 40
int styleable AppCompatTheme_buttonBarButtonStyle 41
int styleable AppCompatTheme_buttonBarNegativeButtonStyle 42
int styleable AppCompatTheme_buttonBarNeutralButtonStyle 43
int styleable AppCompatTheme_buttonBarPositiveButtonStyle 44
int styleable AppCompatTheme_buttonBarStyle 45
int styleable AppCompatTheme_buttonStyle 46
int styleable AppCompatTheme_buttonStyleSmall 47
int styleable AppCompatTheme_checkboxStyle 48
int styleable AppCompatTheme_checkedTextViewStyle 49
int styleable AppCompatTheme_colorAccent 50
int styleable AppCompatTheme_colorBackgroundFloating 51
int styleable AppCompatTheme_colorButtonNormal 52
int styleable AppCompatTheme_colorControlActivated 53
int styleable AppCompatTheme_colorControlHighlight 54
int styleable AppCompatTheme_colorControlNormal 55
int styleable AppCompatTheme_colorError 56
int styleable AppCompatTheme_colorPrimary 57
int styleable AppCompatTheme_colorPrimaryDark 58
int styleable AppCompatTheme_colorSwitchThumbNormal 59
int styleable AppCompatTheme_controlBackground 60
int styleable AppCompatTheme_dialogCornerRadius 61
int styleable AppCompatTheme_dialogPreferredPadding 62
int styleable AppCompatTheme_dialogTheme 63
int styleable AppCompatTheme_dividerHorizontal 64
int styleable AppCompatTheme_dividerVertical 65
int styleable AppCompatTheme_dropDownListViewStyle 66
int styleable AppCompatTheme_dropdownListPreferredItemHeight 67
int styleable AppCompatTheme_editTextBackground 68
int styleable AppCompatTheme_editTextColor 69
int styleable AppCompatTheme_editTextStyle 70
int styleable AppCompatTheme_homeAsUpIndicator 71
int styleable AppCompatTheme_imageButtonStyle 72
int styleable AppCompatTheme_listChoiceBackgroundIndicator 73
int styleable AppCompatTheme_listChoiceIndicatorMultipleAnimated 74
int styleable AppCompatTheme_listChoiceIndicatorSingleAnimated 75
int styleable AppCompatTheme_listDividerAlertDialog 76
int styleable AppCompatTheme_listMenuViewStyle 77
int styleable AppCompatTheme_listPopupWindowStyle 78
int styleable AppCompatTheme_listPreferredItemHeight 79
int styleable AppCompatTheme_listPreferredItemHeightLarge 80
int styleable AppCompatTheme_listPreferredItemHeightSmall 81
int styleable AppCompatTheme_listPreferredItemPaddingEnd 82
int styleable AppCompatTheme_listPreferredItemPaddingLeft 83
int styleable AppCompatTheme_listPreferredItemPaddingRight 84
int styleable AppCompatTheme_listPreferredItemPaddingStart 85
int styleable AppCompatTheme_panelBackground 86
int styleable AppCompatTheme_panelMenuListTheme 87
int styleable AppCompatTheme_panelMenuListWidth 88
int styleable AppCompatTheme_popupMenuStyle 89
int styleable AppCompatTheme_popupWindowStyle 90
int styleable AppCompatTheme_radioButtonStyle 91
int styleable AppCompatTheme_ratingBarStyle 92
int styleable AppCompatTheme_ratingBarStyleIndicator 93
int styleable AppCompatTheme_ratingBarStyleSmall 94
int styleable AppCompatTheme_searchViewStyle 95
int styleable AppCompatTheme_seekBarStyle 96
int styleable AppCompatTheme_selectableItemBackground 97
int styleable AppCompatTheme_selectableItemBackgroundBorderless 98
int styleable AppCompatTheme_spinnerDropDownItemStyle 99
int styleable AppCompatTheme_spinnerStyle 100
int styleable AppCompatTheme_switchStyle 101
int styleable AppCompatTheme_textAppearanceLargePopupMenu 102
int styleable AppCompatTheme_textAppearanceListItem 103
int styleable AppCompatTheme_textAppearanceListItemSecondary 104
int styleable AppCompatTheme_textAppearanceListItemSmall 105
int styleable AppCompatTheme_textAppearancePopupMenuHeader 106
int styleable AppCompatTheme_textAppearanceSearchResultSubtitle 107
int styleable AppCompatTheme_textAppearanceSearchResultTitle 108
int styleable AppCompatTheme_textAppearanceSmallPopupMenu 109
int styleable AppCompatTheme_textColorAlertDialogListItem 110
int styleable AppCompatTheme_textColorSearchUrl 111
int styleable AppCompatTheme_toolbarNavigationButtonStyle 112
int styleable AppCompatTheme_toolbarStyle 113
int styleable AppCompatTheme_tooltipForegroundColor 114
int styleable AppCompatTheme_tooltipFrameBackground 115
int styleable AppCompatTheme_viewInflaterClass 116
int styleable AppCompatTheme_windowActionBar 117
int styleable AppCompatTheme_windowActionBarOverlay 118
int styleable AppCompatTheme_windowActionModeOverlay 119
int styleable AppCompatTheme_windowFixedHeightMajor 120
int styleable AppCompatTheme_windowFixedHeightMinor 121
int styleable AppCompatTheme_windowFixedWidthMajor 122
int styleable AppCompatTheme_windowFixedWidthMinor 123
int styleable AppCompatTheme_windowMinWidthMajor 124
int styleable AppCompatTheme_windowMinWidthMinor 125
int styleable AppCompatTheme_windowNoTitle 126
int[] styleable ButtonBarLayout { 0x7f03002a }
int styleable ButtonBarLayout_allowStacking 0
int[] styleable Capability { 0x7f0301a0, 0x7f0301bb }
int styleable Capability_queryPatterns 0
int styleable Capability_shortcutMatchRequired 1
int[] styleable Carousel { 0x7f030058, 0x7f030059, 0x7f03005a, 0x7f03005b, 0x7f03005c, 0x7f03005d, 0x7f03005e, 0x7f03005f, 0x7f030060, 0x7f030061 }
int styleable Carousel_carousel_backwardTransition 0
int styleable Carousel_carousel_emptyViewsBehavior 1
int styleable Carousel_carousel_firstView 2
int styleable Carousel_carousel_forwardTransition 3
int styleable Carousel_carousel_infinite 4
int styleable Carousel_carousel_nextState 5
int styleable Carousel_carousel_previousState 6
int styleable Carousel_carousel_touchUpMode 7
int styleable Carousel_carousel_touchUp_dampeningFactor 8
int styleable Carousel_carousel_touchUp_velocityThreshold 9
int[] styleable CheckedTextView { 0x01010108, 0x7f030063, 0x7f030064, 0x7f030065 }
int styleable CheckedTextView_android_checkMark 0
int styleable CheckedTextView_checkMarkCompat 1
int styleable CheckedTextView_checkMarkTint 2
int styleable CheckedTextView_checkMarkTintMode 3
int[] styleable ColorStateListItem { 0x010101a5, 0x0101031f, 0x01010647, 0x7f03002b, 0x7f030100 }
int styleable ColorStateListItem_android_color 0
int styleable ColorStateListItem_android_alpha 1
int styleable ColorStateListItem_android_lStar 2
int styleable ColorStateListItem_alpha 3
int styleable ColorStateListItem_lStar 4
int[] styleable CompoundButton { 0x01010107, 0x7f030050, 0x7f030056, 0x7f030057 }
int styleable CompoundButton_android_button 0
int styleable CompoundButton_buttonCompat 1
int styleable CompoundButton_buttonTint 2
int styleable CompoundButton_buttonTintMode 3
int[] styleable Constraint { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f03002e, 0x7f03002f, 0x7f030043, 0x7f030044, 0x7f030045, 0x7f030062, 0x7f030084, 0x7f030085, 0x7f0300af, 0x7f0300c9, 0x7f0300ca, 0x7f0300cb, 0x7f0300cc, 0x7f0300cd, 0x7f0300ce, 0x7f0300cf, 0x7f0300d0, 0x7f0300d1, 0x7f0300d2, 0x7f0300d3, 0x7f0300d4, 0x7f0300d5, 0x7f0300d7, 0x7f0300d8, 0x7f0300d9, 0x7f0300da, 0x7f0300db, 0x7f0300eb, 0x7f030106, 0x7f030107, 0x7f030108, 0x7f030109, 0x7f03010a, 0x7f03010b, 0x7f03010c, 0x7f03010d, 0x7f03010e, 0x7f03010f, 0x7f030110, 0x7f030111, 0x7f030112, 0x7f030113, 0x7f030114, 0x7f030115, 0x7f030116, 0x7f030117, 0x7f030118, 0x7f030119, 0x7f03011a, 0x7f03011b, 0x7f03011c, 0x7f03011d, 0x7f03011e, 0x7f03011f, 0x7f030120, 0x7f030121, 0x7f030122, 0x7f030123, 0x7f030124, 0x7f030125, 0x7f030126, 0x7f030127, 0x7f030128, 0x7f030129, 0x7f03012a, 0x7f03012b, 0x7f03012c, 0x7f03012d, 0x7f03012e, 0x7f03012f, 0x7f030130, 0x7f030131, 0x7f030132, 0x7f030133, 0x7f030134, 0x7f030135, 0x7f030136, 0x7f030137, 0x7f030138, 0x7f030139, 0x7f03013a, 0x7f03013b, 0x7f03013c, 0x7f03013d, 0x7f03013f, 0x7f03016e, 0x7f03016f, 0x7f03018b, 0x7f030192, 0x7f030194, 0x7f03019b, 0x7f03019c, 0x7f03019d, 0x7f030219, 0x7f03021b, 0x7f03021d, 0x7f030228 }
int styleable Constraint_android_orientation 0
int styleable Constraint_android_id 1
int styleable Constraint_android_visibility 2
int styleable Constraint_android_layout_width 3
int styleable Constraint_android_layout_height 4
int styleable Constraint_android_layout_marginLeft 5
int styleable Constraint_android_layout_marginTop 6
int styleable Constraint_android_layout_marginRight 7
int styleable Constraint_android_layout_marginBottom 8
int styleable Constraint_android_maxWidth 9
int styleable Constraint_android_maxHeight 10
int styleable Constraint_android_minWidth 11
int styleable Constraint_android_minHeight 12
int styleable Constraint_android_alpha 13
int styleable Constraint_android_transformPivotX 14
int styleable Constraint_android_transformPivotY 15
int styleable Constraint_android_translationX 16
int styleable Constraint_android_translationY 17
int styleable Constraint_android_scaleX 18
int styleable Constraint_android_scaleY 19
int styleable Constraint_android_rotation 20
int styleable Constraint_android_rotationX 21
int styleable Constraint_android_rotationY 22
int styleable Constraint_android_layout_marginStart 23
int styleable Constraint_android_layout_marginEnd 24
int styleable Constraint_android_translationZ 25
int styleable Constraint_android_elevation 26
int styleable Constraint_animateCircleAngleTo 27
int styleable Constraint_animateRelativeTo 28
int styleable Constraint_barrierAllowsGoneWidgets 29
int styleable Constraint_barrierDirection 30
int styleable Constraint_barrierMargin 31
int styleable Constraint_chainUseRtl 32
int styleable Constraint_constraint_referenced_ids 33
int styleable Constraint_constraint_referenced_tags 34
int styleable Constraint_drawPath 35
int styleable Constraint_flow_firstHorizontalBias 36
int styleable Constraint_flow_firstHorizontalStyle 37
int styleable Constraint_flow_firstVerticalBias 38
int styleable Constraint_flow_firstVerticalStyle 39
int styleable Constraint_flow_horizontalAlign 40
int styleable Constraint_flow_horizontalBias 41
int styleable Constraint_flow_horizontalGap 42
int styleable Constraint_flow_horizontalStyle 43
int styleable Constraint_flow_lastHorizontalBias 44
int styleable Constraint_flow_lastHorizontalStyle 45
int styleable Constraint_flow_lastVerticalBias 46
int styleable Constraint_flow_lastVerticalStyle 47
int styleable Constraint_flow_maxElementsWrap 48
int styleable Constraint_flow_verticalAlign 49
int styleable Constraint_flow_verticalBias 50
int styleable Constraint_flow_verticalGap 51
int styleable Constraint_flow_verticalStyle 52
int styleable Constraint_flow_wrapMode 53
int styleable Constraint_guidelineUseRtl 54
int styleable Constraint_layout_constrainedHeight 55
int styleable Constraint_layout_constrainedWidth 56
int styleable Constraint_layout_constraintBaseline_creator 57
int styleable Constraint_layout_constraintBaseline_toBaselineOf 58
int styleable Constraint_layout_constraintBaseline_toBottomOf 59
int styleable Constraint_layout_constraintBaseline_toTopOf 60
int styleable Constraint_layout_constraintBottom_creator 61
int styleable Constraint_layout_constraintBottom_toBottomOf 62
int styleable Constraint_layout_constraintBottom_toTopOf 63
int styleable Constraint_layout_constraintCircle 64
int styleable Constraint_layout_constraintCircleAngle 65
int styleable Constraint_layout_constraintCircleRadius 66
int styleable Constraint_layout_constraintDimensionRatio 67
int styleable Constraint_layout_constraintEnd_toEndOf 68
int styleable Constraint_layout_constraintEnd_toStartOf 69
int styleable Constraint_layout_constraintGuide_begin 70
int styleable Constraint_layout_constraintGuide_end 71
int styleable Constraint_layout_constraintGuide_percent 72
int styleable Constraint_layout_constraintHeight 73
int styleable Constraint_layout_constraintHeight_default 74
int styleable Constraint_layout_constraintHeight_max 75
int styleable Constraint_layout_constraintHeight_min 76
int styleable Constraint_layout_constraintHeight_percent 77
int styleable Constraint_layout_constraintHorizontal_bias 78
int styleable Constraint_layout_constraintHorizontal_chainStyle 79
int styleable Constraint_layout_constraintHorizontal_weight 80
int styleable Constraint_layout_constraintLeft_creator 81
int styleable Constraint_layout_constraintLeft_toLeftOf 82
int styleable Constraint_layout_constraintLeft_toRightOf 83
int styleable Constraint_layout_constraintRight_creator 84
int styleable Constraint_layout_constraintRight_toLeftOf 85
int styleable Constraint_layout_constraintRight_toRightOf 86
int styleable Constraint_layout_constraintStart_toEndOf 87
int styleable Constraint_layout_constraintStart_toStartOf 88
int styleable Constraint_layout_constraintTag 89
int styleable Constraint_layout_constraintTop_creator 90
int styleable Constraint_layout_constraintTop_toBottomOf 91
int styleable Constraint_layout_constraintTop_toTopOf 92
int styleable Constraint_layout_constraintVertical_bias 93
int styleable Constraint_layout_constraintVertical_chainStyle 94
int styleable Constraint_layout_constraintVertical_weight 95
int styleable Constraint_layout_constraintWidth 96
int styleable Constraint_layout_constraintWidth_default 97
int styleable Constraint_layout_constraintWidth_max 98
int styleable Constraint_layout_constraintWidth_min 99
int styleable Constraint_layout_constraintWidth_percent 100
int styleable Constraint_layout_editor_absoluteX 101
int styleable Constraint_layout_editor_absoluteY 102
int styleable Constraint_layout_goneMarginBaseline 103
int styleable Constraint_layout_goneMarginBottom 104
int styleable Constraint_layout_goneMarginEnd 105
int styleable Constraint_layout_goneMarginLeft 106
int styleable Constraint_layout_goneMarginRight 107
int styleable Constraint_layout_goneMarginStart 108
int styleable Constraint_layout_goneMarginTop 109
int styleable Constraint_layout_marginBaseline 110
int styleable Constraint_layout_wrapBehaviorInParent 111
int styleable Constraint_motionProgress 112
int styleable Constraint_motionStagger 113
int styleable Constraint_pathMotionArc 114
int styleable Constraint_pivotAnchor 115
int styleable Constraint_polarRelativeTo 116
int styleable Constraint_quantizeMotionInterpolator 117
int styleable Constraint_quantizeMotionPhase 118
int styleable Constraint_quantizeMotionSteps 119
int styleable Constraint_transformPivotTarget 120
int styleable Constraint_transitionEasing 121
int styleable Constraint_transitionPathRotate 122
int styleable Constraint_visibilityMode 123
int[] styleable ConstraintLayout_Layout { 0x010100c4, 0x010100d5, 0x010100d6, 0x010100d7, 0x010100d8, 0x010100d9, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f6, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010103b3, 0x010103b4, 0x010103b5, 0x010103b6, 0x01010440, 0x0101053b, 0x0101053c, 0x7f030043, 0x7f030044, 0x7f030045, 0x7f030062, 0x7f030069, 0x7f03006a, 0x7f03006b, 0x7f03006c, 0x7f03006d, 0x7f030081, 0x7f030084, 0x7f030085, 0x7f0300c9, 0x7f0300ca, 0x7f0300cb, 0x7f0300cc, 0x7f0300cd, 0x7f0300ce, 0x7f0300cf, 0x7f0300d0, 0x7f0300d1, 0x7f0300d2, 0x7f0300d3, 0x7f0300d4, 0x7f0300d5, 0x7f0300d7, 0x7f0300d8, 0x7f0300d9, 0x7f0300da, 0x7f0300db, 0x7f0300eb, 0x7f030103, 0x7f030106, 0x7f030107, 0x7f030108, 0x7f030109, 0x7f03010a, 0x7f03010b, 0x7f03010c, 0x7f03010d, 0x7f03010e, 0x7f03010f, 0x7f030110, 0x7f030111, 0x7f030112, 0x7f030113, 0x7f030114, 0x7f030115, 0x7f030116, 0x7f030117, 0x7f030118, 0x7f030119, 0x7f03011a, 0x7f03011b, 0x7f03011c, 0x7f03011d, 0x7f03011e, 0x7f03011f, 0x7f030120, 0x7f030121, 0x7f030122, 0x7f030123, 0x7f030124, 0x7f030125, 0x7f030126, 0x7f030127, 0x7f030128, 0x7f030129, 0x7f03012a, 0x7f03012b, 0x7f03012c, 0x7f03012d, 0x7f03012e, 0x7f03012f, 0x7f030130, 0x7f030131, 0x7f030132, 0x7f030133, 0x7f030134, 0x7f030135, 0x7f030136, 0x7f030137, 0x7f030138, 0x7f030139, 0x7f03013a, 0x7f03013b, 0x7f03013c, 0x7f03013d, 0x7f03013e, 0x7f03013f }
int styleable ConstraintLayout_Layout_android_orientation 0
int styleable ConstraintLayout_Layout_android_padding 1
int styleable ConstraintLayout_Layout_android_paddingLeft 2
int styleable ConstraintLayout_Layout_android_paddingTop 3
int styleable ConstraintLayout_Layout_android_paddingRight 4
int styleable ConstraintLayout_Layout_android_paddingBottom 5
int styleable ConstraintLayout_Layout_android_visibility 6
int styleable ConstraintLayout_Layout_android_layout_width 7
int styleable ConstraintLayout_Layout_android_layout_height 8
int styleable ConstraintLayout_Layout_android_layout_margin 9
int styleable ConstraintLayout_Layout_android_layout_marginLeft 10
int styleable ConstraintLayout_Layout_android_layout_marginTop 11
int styleable ConstraintLayout_Layout_android_layout_marginRight 12
int styleable ConstraintLayout_Layout_android_layout_marginBottom 13
int styleable ConstraintLayout_Layout_android_maxWidth 14
int styleable ConstraintLayout_Layout_android_maxHeight 15
int styleable ConstraintLayout_Layout_android_minWidth 16
int styleable ConstraintLayout_Layout_android_minHeight 17
int styleable ConstraintLayout_Layout_android_paddingStart 18
int styleable ConstraintLayout_Layout_android_paddingEnd 19
int styleable ConstraintLayout_Layout_android_layout_marginStart 20
int styleable ConstraintLayout_Layout_android_layout_marginEnd 21
int styleable ConstraintLayout_Layout_android_elevation 22
int styleable ConstraintLayout_Layout_android_layout_marginHorizontal 23
int styleable ConstraintLayout_Layout_android_layout_marginVertical 24
int styleable ConstraintLayout_Layout_barrierAllowsGoneWidgets 25
int styleable ConstraintLayout_Layout_barrierDirection 26
int styleable ConstraintLayout_Layout_barrierMargin 27
int styleable ConstraintLayout_Layout_chainUseRtl 28
int styleable ConstraintLayout_Layout_circularflow_angles 29
int styleable ConstraintLayout_Layout_circularflow_defaultAngle 30
int styleable ConstraintLayout_Layout_circularflow_defaultRadius 31
int styleable ConstraintLayout_Layout_circularflow_radiusInDP 32
int styleable ConstraintLayout_Layout_circularflow_viewCenter 33
int styleable ConstraintLayout_Layout_constraintSet 34
int styleable ConstraintLayout_Layout_constraint_referenced_ids 35
int styleable ConstraintLayout_Layout_constraint_referenced_tags 36
int styleable ConstraintLayout_Layout_flow_firstHorizontalBias 37
int styleable ConstraintLayout_Layout_flow_firstHorizontalStyle 38
int styleable ConstraintLayout_Layout_flow_firstVerticalBias 39
int styleable ConstraintLayout_Layout_flow_firstVerticalStyle 40
int styleable ConstraintLayout_Layout_flow_horizontalAlign 41
int styleable ConstraintLayout_Layout_flow_horizontalBias 42
int styleable ConstraintLayout_Layout_flow_horizontalGap 43
int styleable ConstraintLayout_Layout_flow_horizontalStyle 44
int styleable ConstraintLayout_Layout_flow_lastHorizontalBias 45
int styleable ConstraintLayout_Layout_flow_lastHorizontalStyle 46
int styleable ConstraintLayout_Layout_flow_lastVerticalBias 47
int styleable ConstraintLayout_Layout_flow_lastVerticalStyle 48
int styleable ConstraintLayout_Layout_flow_maxElementsWrap 49
int styleable ConstraintLayout_Layout_flow_verticalAlign 50
int styleable ConstraintLayout_Layout_flow_verticalBias 51
int styleable ConstraintLayout_Layout_flow_verticalGap 52
int styleable ConstraintLayout_Layout_flow_verticalStyle 53
int styleable ConstraintLayout_Layout_flow_wrapMode 54
int styleable ConstraintLayout_Layout_guidelineUseRtl 55
int styleable ConstraintLayout_Layout_layoutDescription 56
int styleable ConstraintLayout_Layout_layout_constrainedHeight 57
int styleable ConstraintLayout_Layout_layout_constrainedWidth 58
int styleable ConstraintLayout_Layout_layout_constraintBaseline_creator 59
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBaselineOf 60
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toBottomOf 61
int styleable ConstraintLayout_Layout_layout_constraintBaseline_toTopOf 62
int styleable ConstraintLayout_Layout_layout_constraintBottom_creator 63
int styleable ConstraintLayout_Layout_layout_constraintBottom_toBottomOf 64
int styleable ConstraintLayout_Layout_layout_constraintBottom_toTopOf 65
int styleable ConstraintLayout_Layout_layout_constraintCircle 66
int styleable ConstraintLayout_Layout_layout_constraintCircleAngle 67
int styleable ConstraintLayout_Layout_layout_constraintCircleRadius 68
int styleable ConstraintLayout_Layout_layout_constraintDimensionRatio 69
int styleable ConstraintLayout_Layout_layout_constraintEnd_toEndOf 70
int styleable ConstraintLayout_Layout_layout_constraintEnd_toStartOf 71
int styleable ConstraintLayout_Layout_layout_constraintGuide_begin 72
int styleable ConstraintLayout_Layout_layout_constraintGuide_end 73
int styleable ConstraintLayout_Layout_layout_constraintGuide_percent 74
int styleable ConstraintLayout_Layout_layout_constraintHeight 75
int styleable ConstraintLayout_Layout_layout_constraintHeight_default 76
int styleable ConstraintLayout_Layout_layout_constraintHeight_max 77
int styleable ConstraintLayout_Layout_layout_constraintHeight_min 78
int styleable ConstraintLayout_Layout_layout_constraintHeight_percent 79
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_bias 80
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_chainStyle 81
int styleable ConstraintLayout_Layout_layout_constraintHorizontal_weight 82
int styleable ConstraintLayout_Layout_layout_constraintLeft_creator 83
int styleable ConstraintLayout_Layout_layout_constraintLeft_toLeftOf 84
int styleable ConstraintLayout_Layout_layout_constraintLeft_toRightOf 85
int styleable ConstraintLayout_Layout_layout_constraintRight_creator 86
int styleable ConstraintLayout_Layout_layout_constraintRight_toLeftOf 87
int styleable ConstraintLayout_Layout_layout_constraintRight_toRightOf 88
int styleable ConstraintLayout_Layout_layout_constraintStart_toEndOf 89
int styleable ConstraintLayout_Layout_layout_constraintStart_toStartOf 90
int styleable ConstraintLayout_Layout_layout_constraintTag 91
int styleable ConstraintLayout_Layout_layout_constraintTop_creator 92
int styleable ConstraintLayout_Layout_layout_constraintTop_toBottomOf 93
int styleable ConstraintLayout_Layout_layout_constraintTop_toTopOf 94
int styleable ConstraintLayout_Layout_layout_constraintVertical_bias 95
int styleable ConstraintLayout_Layout_layout_constraintVertical_chainStyle 96
int styleable ConstraintLayout_Layout_layout_constraintVertical_weight 97
int styleable ConstraintLayout_Layout_layout_constraintWidth 98
int styleable ConstraintLayout_Layout_layout_constraintWidth_default 99
int styleable ConstraintLayout_Layout_layout_constraintWidth_max 100
int styleable ConstraintLayout_Layout_layout_constraintWidth_min 101
int styleable ConstraintLayout_Layout_layout_constraintWidth_percent 102
int styleable ConstraintLayout_Layout_layout_editor_absoluteX 103
int styleable ConstraintLayout_Layout_layout_editor_absoluteY 104
int styleable ConstraintLayout_Layout_layout_goneMarginBaseline 105
int styleable ConstraintLayout_Layout_layout_goneMarginBottom 106
int styleable ConstraintLayout_Layout_layout_goneMarginEnd 107
int styleable ConstraintLayout_Layout_layout_goneMarginLeft 108
int styleable ConstraintLayout_Layout_layout_goneMarginRight 109
int styleable ConstraintLayout_Layout_layout_goneMarginStart 110
int styleable ConstraintLayout_Layout_layout_goneMarginTop 111
int styleable ConstraintLayout_Layout_layout_marginBaseline 112
int styleable ConstraintLayout_Layout_layout_optimizationLevel 113
int styleable ConstraintLayout_Layout_layout_wrapBehaviorInParent 114
int[] styleable ConstraintLayout_ReactiveGuide { 0x7f0301a5, 0x7f0301a6, 0x7f0301a7, 0x7f0301a8 }
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_animateChange 0
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_applyToAllConstraintSets 1
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_applyToConstraintSet 2
int styleable ConstraintLayout_ReactiveGuide_reactiveGuide_valueId 3
int[] styleable ConstraintLayout_placeholder { 0x7f030087, 0x7f030193 }
int styleable ConstraintLayout_placeholder_content 0
int styleable ConstraintLayout_placeholder_placeholder_emptyVisibility 1
int[] styleable ConstraintOverride { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f03002e, 0x7f03002f, 0x7f030043, 0x7f030044, 0x7f030045, 0x7f030062, 0x7f030084, 0x7f0300af, 0x7f0300c9, 0x7f0300ca, 0x7f0300cb, 0x7f0300cc, 0x7f0300cd, 0x7f0300ce, 0x7f0300cf, 0x7f0300d0, 0x7f0300d1, 0x7f0300d2, 0x7f0300d3, 0x7f0300d4, 0x7f0300d5, 0x7f0300d7, 0x7f0300d8, 0x7f0300d9, 0x7f0300da, 0x7f0300db, 0x7f0300eb, 0x7f030106, 0x7f030107, 0x7f030108, 0x7f03010c, 0x7f030110, 0x7f030111, 0x7f030112, 0x7f030115, 0x7f030116, 0x7f030117, 0x7f030118, 0x7f030119, 0x7f03011a, 0x7f03011b, 0x7f03011c, 0x7f03011d, 0x7f03011e, 0x7f03011f, 0x7f030120, 0x7f030123, 0x7f030128, 0x7f030129, 0x7f03012c, 0x7f03012d, 0x7f03012e, 0x7f03012f, 0x7f030130, 0x7f030131, 0x7f030132, 0x7f030133, 0x7f030134, 0x7f030135, 0x7f030136, 0x7f030137, 0x7f030138, 0x7f030139, 0x7f03013a, 0x7f03013b, 0x7f03013c, 0x7f03013d, 0x7f03013f, 0x7f03016e, 0x7f03016f, 0x7f030170, 0x7f03018b, 0x7f030192, 0x7f030194, 0x7f03019b, 0x7f03019c, 0x7f03019d, 0x7f030219, 0x7f03021b, 0x7f03021d, 0x7f030228 }
int styleable ConstraintOverride_android_orientation 0
int styleable ConstraintOverride_android_id 1
int styleable ConstraintOverride_android_visibility 2
int styleable ConstraintOverride_android_layout_width 3
int styleable ConstraintOverride_android_layout_height 4
int styleable ConstraintOverride_android_layout_marginLeft 5
int styleable ConstraintOverride_android_layout_marginTop 6
int styleable ConstraintOverride_android_layout_marginRight 7
int styleable ConstraintOverride_android_layout_marginBottom 8
int styleable ConstraintOverride_android_maxWidth 9
int styleable ConstraintOverride_android_maxHeight 10
int styleable ConstraintOverride_android_minWidth 11
int styleable ConstraintOverride_android_minHeight 12
int styleable ConstraintOverride_android_alpha 13
int styleable ConstraintOverride_android_transformPivotX 14
int styleable ConstraintOverride_android_transformPivotY 15
int styleable ConstraintOverride_android_translationX 16
int styleable ConstraintOverride_android_translationY 17
int styleable ConstraintOverride_android_scaleX 18
int styleable ConstraintOverride_android_scaleY 19
int styleable ConstraintOverride_android_rotation 20
int styleable ConstraintOverride_android_rotationX 21
int styleable ConstraintOverride_android_rotationY 22
int styleable ConstraintOverride_android_layout_marginStart 23
int styleable ConstraintOverride_android_layout_marginEnd 24
int styleable ConstraintOverride_android_translationZ 25
int styleable ConstraintOverride_android_elevation 26
int styleable ConstraintOverride_animateCircleAngleTo 27
int styleable ConstraintOverride_animateRelativeTo 28
int styleable ConstraintOverride_barrierAllowsGoneWidgets 29
int styleable ConstraintOverride_barrierDirection 30
int styleable ConstraintOverride_barrierMargin 31
int styleable ConstraintOverride_chainUseRtl 32
int styleable ConstraintOverride_constraint_referenced_ids 33
int styleable ConstraintOverride_drawPath 34
int styleable ConstraintOverride_flow_firstHorizontalBias 35
int styleable ConstraintOverride_flow_firstHorizontalStyle 36
int styleable ConstraintOverride_flow_firstVerticalBias 37
int styleable ConstraintOverride_flow_firstVerticalStyle 38
int styleable ConstraintOverride_flow_horizontalAlign 39
int styleable ConstraintOverride_flow_horizontalBias 40
int styleable ConstraintOverride_flow_horizontalGap 41
int styleable ConstraintOverride_flow_horizontalStyle 42
int styleable ConstraintOverride_flow_lastHorizontalBias 43
int styleable ConstraintOverride_flow_lastHorizontalStyle 44
int styleable ConstraintOverride_flow_lastVerticalBias 45
int styleable ConstraintOverride_flow_lastVerticalStyle 46
int styleable ConstraintOverride_flow_maxElementsWrap 47
int styleable ConstraintOverride_flow_verticalAlign 48
int styleable ConstraintOverride_flow_verticalBias 49
int styleable ConstraintOverride_flow_verticalGap 50
int styleable ConstraintOverride_flow_verticalStyle 51
int styleable ConstraintOverride_flow_wrapMode 52
int styleable ConstraintOverride_guidelineUseRtl 53
int styleable ConstraintOverride_layout_constrainedHeight 54
int styleable ConstraintOverride_layout_constrainedWidth 55
int styleable ConstraintOverride_layout_constraintBaseline_creator 56
int styleable ConstraintOverride_layout_constraintBottom_creator 57
int styleable ConstraintOverride_layout_constraintCircleAngle 58
int styleable ConstraintOverride_layout_constraintCircleRadius 59
int styleable ConstraintOverride_layout_constraintDimensionRatio 60
int styleable ConstraintOverride_layout_constraintGuide_begin 61
int styleable ConstraintOverride_layout_constraintGuide_end 62
int styleable ConstraintOverride_layout_constraintGuide_percent 63
int styleable ConstraintOverride_layout_constraintHeight 64
int styleable ConstraintOverride_layout_constraintHeight_default 65
int styleable ConstraintOverride_layout_constraintHeight_max 66
int styleable ConstraintOverride_layout_constraintHeight_min 67
int styleable ConstraintOverride_layout_constraintHeight_percent 68
int styleable ConstraintOverride_layout_constraintHorizontal_bias 69
int styleable ConstraintOverride_layout_constraintHorizontal_chainStyle 70
int styleable ConstraintOverride_layout_constraintHorizontal_weight 71
int styleable ConstraintOverride_layout_constraintLeft_creator 72
int styleable ConstraintOverride_layout_constraintRight_creator 73
int styleable ConstraintOverride_layout_constraintTag 74
int styleable ConstraintOverride_layout_constraintTop_creator 75
int styleable ConstraintOverride_layout_constraintVertical_bias 76
int styleable ConstraintOverride_layout_constraintVertical_chainStyle 77
int styleable ConstraintOverride_layout_constraintVertical_weight 78
int styleable ConstraintOverride_layout_constraintWidth 79
int styleable ConstraintOverride_layout_constraintWidth_default 80
int styleable ConstraintOverride_layout_constraintWidth_max 81
int styleable ConstraintOverride_layout_constraintWidth_min 82
int styleable ConstraintOverride_layout_constraintWidth_percent 83
int styleable ConstraintOverride_layout_editor_absoluteX 84
int styleable ConstraintOverride_layout_editor_absoluteY 85
int styleable ConstraintOverride_layout_goneMarginBaseline 86
int styleable ConstraintOverride_layout_goneMarginBottom 87
int styleable ConstraintOverride_layout_goneMarginEnd 88
int styleable ConstraintOverride_layout_goneMarginLeft 89
int styleable ConstraintOverride_layout_goneMarginRight 90
int styleable ConstraintOverride_layout_goneMarginStart 91
int styleable ConstraintOverride_layout_goneMarginTop 92
int styleable ConstraintOverride_layout_marginBaseline 93
int styleable ConstraintOverride_layout_wrapBehaviorInParent 94
int styleable ConstraintOverride_motionProgress 95
int styleable ConstraintOverride_motionStagger 96
int styleable ConstraintOverride_motionTarget 97
int styleable ConstraintOverride_pathMotionArc 98
int styleable ConstraintOverride_pivotAnchor 99
int styleable ConstraintOverride_polarRelativeTo 100
int styleable ConstraintOverride_quantizeMotionInterpolator 101
int styleable ConstraintOverride_quantizeMotionPhase 102
int styleable ConstraintOverride_quantizeMotionSteps 103
int styleable ConstraintOverride_transformPivotTarget 104
int styleable ConstraintOverride_transitionEasing 105
int styleable ConstraintOverride_transitionPathRotate 106
int styleable ConstraintOverride_visibilityMode 107
int[] styleable ConstraintSet { 0x010100c4, 0x010100d0, 0x010100dc, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x0101011f, 0x01010120, 0x0101013f, 0x01010140, 0x010101b5, 0x010101b6, 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103b5, 0x010103b6, 0x010103fa, 0x01010440, 0x7f03002e, 0x7f03002f, 0x7f030043, 0x7f030044, 0x7f030045, 0x7f030062, 0x7f030080, 0x7f030084, 0x7f030085, 0x7f0300a3, 0x7f0300af, 0x7f0300c9, 0x7f0300ca, 0x7f0300cb, 0x7f0300cc, 0x7f0300cd, 0x7f0300ce, 0x7f0300cf, 0x7f0300d0, 0x7f0300d1, 0x7f0300d2, 0x7f0300d3, 0x7f0300d4, 0x7f0300d5, 0x7f0300d7, 0x7f0300d8, 0x7f0300d9, 0x7f0300da, 0x7f0300db, 0x7f0300eb, 0x7f030106, 0x7f030107, 0x7f030108, 0x7f030109, 0x7f03010a, 0x7f03010b, 0x7f03010c, 0x7f03010d, 0x7f03010e, 0x7f03010f, 0x7f030110, 0x7f030111, 0x7f030112, 0x7f030113, 0x7f030114, 0x7f030115, 0x7f030116, 0x7f030117, 0x7f030119, 0x7f03011a, 0x7f03011b, 0x7f03011c, 0x7f03011d, 0x7f03011e, 0x7f03011f, 0x7f030120, 0x7f030121, 0x7f030122, 0x7f030123, 0x7f030124, 0x7f030125, 0x7f030126, 0x7f030127, 0x7f030128, 0x7f030129, 0x7f03012a, 0x7f03012b, 0x7f03012c, 0x7f03012d, 0x7f03012e, 0x7f030130, 0x7f030131, 0x7f030132, 0x7f030133, 0x7f030134, 0x7f030135, 0x7f030136, 0x7f030137, 0x7f030138, 0x7f030139, 0x7f03013a, 0x7f03013b, 0x7f03013c, 0x7f03013d, 0x7f03013f, 0x7f03016e, 0x7f03016f, 0x7f03018b, 0x7f030192, 0x7f030194, 0x7f03019d, 0x7f03021b, 0x7f03021d }
int styleable ConstraintSet_android_orientation 0
int styleable ConstraintSet_android_id 1
int styleable ConstraintSet_android_visibility 2
int styleable ConstraintSet_android_layout_width 3
int styleable ConstraintSet_android_layout_height 4
int styleable ConstraintSet_android_layout_marginLeft 5
int styleable ConstraintSet_android_layout_marginTop 6
int styleable ConstraintSet_android_layout_marginRight 7
int styleable ConstraintSet_android_layout_marginBottom 8
int styleable ConstraintSet_android_maxWidth 9
int styleable ConstraintSet_android_maxHeight 10
int styleable ConstraintSet_android_minWidth 11
int styleable ConstraintSet_android_minHeight 12
int styleable ConstraintSet_android_pivotX 13
int styleable ConstraintSet_android_pivotY 14
int styleable ConstraintSet_android_alpha 15
int styleable ConstraintSet_android_transformPivotX 16
int styleable ConstraintSet_android_transformPivotY 17
int styleable ConstraintSet_android_translationX 18
int styleable ConstraintSet_android_translationY 19
int styleable ConstraintSet_android_scaleX 20
int styleable ConstraintSet_android_scaleY 21
int styleable ConstraintSet_android_rotation 22
int styleable ConstraintSet_android_rotationX 23
int styleable ConstraintSet_android_rotationY 24
int styleable ConstraintSet_android_layout_marginStart 25
int styleable ConstraintSet_android_layout_marginEnd 26
int styleable ConstraintSet_android_translationZ 27
int styleable ConstraintSet_android_elevation 28
int styleable ConstraintSet_animateCircleAngleTo 29
int styleable ConstraintSet_animateRelativeTo 30
int styleable ConstraintSet_barrierAllowsGoneWidgets 31
int styleable ConstraintSet_barrierDirection 32
int styleable ConstraintSet_barrierMargin 33
int styleable ConstraintSet_chainUseRtl 34
int styleable ConstraintSet_constraintRotate 35
int styleable ConstraintSet_constraint_referenced_ids 36
int styleable ConstraintSet_constraint_referenced_tags 37
int styleable ConstraintSet_deriveConstraintsFrom 38
int styleable ConstraintSet_drawPath 39
int styleable ConstraintSet_flow_firstHorizontalBias 40
int styleable ConstraintSet_flow_firstHorizontalStyle 41
int styleable ConstraintSet_flow_firstVerticalBias 42
int styleable ConstraintSet_flow_firstVerticalStyle 43
int styleable ConstraintSet_flow_horizontalAlign 44
int styleable ConstraintSet_flow_horizontalBias 45
int styleable ConstraintSet_flow_horizontalGap 46
int styleable ConstraintSet_flow_horizontalStyle 47
int styleable ConstraintSet_flow_lastHorizontalBias 48
int styleable ConstraintSet_flow_lastHorizontalStyle 49
int styleable ConstraintSet_flow_lastVerticalBias 50
int styleable ConstraintSet_flow_lastVerticalStyle 51
int styleable ConstraintSet_flow_maxElementsWrap 52
int styleable ConstraintSet_flow_verticalAlign 53
int styleable ConstraintSet_flow_verticalBias 54
int styleable ConstraintSet_flow_verticalGap 55
int styleable ConstraintSet_flow_verticalStyle 56
int styleable ConstraintSet_flow_wrapMode 57
int styleable ConstraintSet_guidelineUseRtl 58
int styleable ConstraintSet_layout_constrainedHeight 59
int styleable ConstraintSet_layout_constrainedWidth 60
int styleable ConstraintSet_layout_constraintBaseline_creator 61
int styleable ConstraintSet_layout_constraintBaseline_toBaselineOf 62
int styleable ConstraintSet_layout_constraintBaseline_toBottomOf 63
int styleable ConstraintSet_layout_constraintBaseline_toTopOf 64
int styleable ConstraintSet_layout_constraintBottom_creator 65
int styleable ConstraintSet_layout_constraintBottom_toBottomOf 66
int styleable ConstraintSet_layout_constraintBottom_toTopOf 67
int styleable ConstraintSet_layout_constraintCircle 68
int styleable ConstraintSet_layout_constraintCircleAngle 69
int styleable ConstraintSet_layout_constraintCircleRadius 70
int styleable ConstraintSet_layout_constraintDimensionRatio 71
int styleable ConstraintSet_layout_constraintEnd_toEndOf 72
int styleable ConstraintSet_layout_constraintEnd_toStartOf 73
int styleable ConstraintSet_layout_constraintGuide_begin 74
int styleable ConstraintSet_layout_constraintGuide_end 75
int styleable ConstraintSet_layout_constraintGuide_percent 76
int styleable ConstraintSet_layout_constraintHeight_default 77
int styleable ConstraintSet_layout_constraintHeight_max 78
int styleable ConstraintSet_layout_constraintHeight_min 79
int styleable ConstraintSet_layout_constraintHeight_percent 80
int styleable ConstraintSet_layout_constraintHorizontal_bias 81
int styleable ConstraintSet_layout_constraintHorizontal_chainStyle 82
int styleable ConstraintSet_layout_constraintHorizontal_weight 83
int styleable ConstraintSet_layout_constraintLeft_creator 84
int styleable ConstraintSet_layout_constraintLeft_toLeftOf 85
int styleable ConstraintSet_layout_constraintLeft_toRightOf 86
int styleable ConstraintSet_layout_constraintRight_creator 87
int styleable ConstraintSet_layout_constraintRight_toLeftOf 88
int styleable ConstraintSet_layout_constraintRight_toRightOf 89
int styleable ConstraintSet_layout_constraintStart_toEndOf 90
int styleable ConstraintSet_layout_constraintStart_toStartOf 91
int styleable ConstraintSet_layout_constraintTag 92
int styleable ConstraintSet_layout_constraintTop_creator 93
int styleable ConstraintSet_layout_constraintTop_toBottomOf 94
int styleable ConstraintSet_layout_constraintTop_toTopOf 95
int styleable ConstraintSet_layout_constraintVertical_bias 96
int styleable ConstraintSet_layout_constraintVertical_chainStyle 97
int styleable ConstraintSet_layout_constraintVertical_weight 98
int styleable ConstraintSet_layout_constraintWidth_default 99
int styleable ConstraintSet_layout_constraintWidth_max 100
int styleable ConstraintSet_layout_constraintWidth_min 101
int styleable ConstraintSet_layout_constraintWidth_percent 102
int styleable ConstraintSet_layout_editor_absoluteX 103
int styleable ConstraintSet_layout_editor_absoluteY 104
int styleable ConstraintSet_layout_goneMarginBaseline 105
int styleable ConstraintSet_layout_goneMarginBottom 106
int styleable ConstraintSet_layout_goneMarginEnd 107
int styleable ConstraintSet_layout_goneMarginLeft 108
int styleable ConstraintSet_layout_goneMarginRight 109
int styleable ConstraintSet_layout_goneMarginStart 110
int styleable ConstraintSet_layout_goneMarginTop 111
int styleable ConstraintSet_layout_marginBaseline 112
int styleable ConstraintSet_layout_wrapBehaviorInParent 113
int styleable ConstraintSet_motionProgress 114
int styleable ConstraintSet_motionStagger 115
int styleable ConstraintSet_pathMotionArc 116
int styleable ConstraintSet_pivotAnchor 117
int styleable ConstraintSet_polarRelativeTo 118
int styleable ConstraintSet_quantizeMotionSteps 119
int styleable ConstraintSet_transitionEasing 120
int styleable ConstraintSet_transitionPathRotate 121
int[] styleable CustomAttribute { 0x7f030034, 0x7f030094, 0x7f030095, 0x7f030096, 0x7f030097, 0x7f030098, 0x7f030099, 0x7f03009b, 0x7f03009c, 0x7f03009d, 0x7f03015a }
int styleable CustomAttribute_attributeName 0
int styleable CustomAttribute_customBoolean 1
int styleable CustomAttribute_customColorDrawableValue 2
int styleable CustomAttribute_customColorValue 3
int styleable CustomAttribute_customDimension 4
int styleable CustomAttribute_customFloatValue 5
int styleable CustomAttribute_customIntegerValue 6
int styleable CustomAttribute_customPixelDimension 7
int styleable CustomAttribute_customReference 8
int styleable CustomAttribute_customStringValue 9
int styleable CustomAttribute_methodName 10
int[] styleable DrawerArrowToggle { 0x7f030032, 0x7f030033, 0x7f030042, 0x7f030074, 0x7f0300b4, 0x7f0300e9, 0x7f0301c4, 0x7f0301fb }
int styleable DrawerArrowToggle_arrowHeadLength 0
int styleable DrawerArrowToggle_arrowShaftLength 1
int styleable DrawerArrowToggle_barLength 2
int styleable DrawerArrowToggle_color 3
int styleable DrawerArrowToggle_drawableSize 4
int styleable DrawerArrowToggle_gapBetweenBars 5
int styleable DrawerArrowToggle_spinBars 6
int styleable DrawerArrowToggle_thickness 7
int[] styleable FontFamily { 0x7f0300de, 0x7f0300df, 0x7f0300e0, 0x7f0300e1, 0x7f0300e2, 0x7f0300e3, 0x7f0300e4 }
int styleable FontFamily_fontProviderAuthority 0
int styleable FontFamily_fontProviderCerts 1
int styleable FontFamily_fontProviderFetchStrategy 2
int styleable FontFamily_fontProviderFetchTimeout 3
int styleable FontFamily_fontProviderPackage 4
int styleable FontFamily_fontProviderQuery 5
int styleable FontFamily_fontProviderSystemFontFamily 6
int[] styleable FontFamilyFont { 0x01010532, 0x01010533, 0x0101053f, 0x0101056f, 0x01010570, 0x7f0300dc, 0x7f0300e5, 0x7f0300e6, 0x7f0300e7, 0x7f030221 }
int styleable FontFamilyFont_android_font 0
int styleable FontFamilyFont_android_fontWeight 1
int styleable FontFamilyFont_android_fontStyle 2
int styleable FontFamilyFont_android_ttcIndex 3
int styleable FontFamilyFont_android_fontVariationSettings 4
int styleable FontFamilyFont_font 5
int styleable FontFamilyFont_fontStyle 6
int styleable FontFamilyFont_fontVariationSettings 7
int styleable FontFamilyFont_fontWeight 8
int styleable FontFamilyFont_ttcIndex 9
int[] styleable Fragment { 0x01010003, 0x010100d0, 0x010100d1 }
int styleable Fragment_android_name 0
int styleable Fragment_android_id 1
int styleable Fragment_android_tag 2
int[] styleable FragmentContainerView { 0x01010003, 0x010100d1 }
int styleable FragmentContainerView_android_name 0
int styleable FragmentContainerView_android_tag 1
int[] styleable GradientColor { 0x0101019d, 0x0101019e, 0x010101a1, 0x010101a2, 0x010101a3, 0x010101a4, 0x01010201, 0x0101020b, 0x01010510, 0x01010511, 0x01010512, 0x01010513 }
int styleable GradientColor_android_startColor 0
int styleable GradientColor_android_endColor 1
int styleable GradientColor_android_type 2
int styleable GradientColor_android_centerX 3
int styleable GradientColor_android_centerY 4
int styleable GradientColor_android_gradientRadius 5
int styleable GradientColor_android_tileMode 6
int styleable GradientColor_android_centerColor 7
int styleable GradientColor_android_startX 8
int styleable GradientColor_android_startY 9
int styleable GradientColor_android_endX 10
int styleable GradientColor_android_endY 11
int[] styleable GradientColorItem { 0x010101a5, 0x01010514 }
int styleable GradientColorItem_android_color 0
int styleable GradientColorItem_android_offset 1
int[] styleable ImageFilterView { 0x7f03002d, 0x7f030046, 0x7f03004a, 0x7f03008f, 0x7f030091, 0x7f0300f7, 0x7f0300f8, 0x7f0300f9, 0x7f0300fa, 0x7f030183, 0x7f0301b0, 0x7f0301b1, 0x7f0301b2, 0x7f03022a }
int styleable ImageFilterView_altSrc 0
int styleable ImageFilterView_blendSrc 1
int styleable ImageFilterView_brightness 2
int styleable ImageFilterView_contrast 3
int styleable ImageFilterView_crossfade 4
int styleable ImageFilterView_imagePanX 5
int styleable ImageFilterView_imagePanY 6
int styleable ImageFilterView_imageRotate 7
int styleable ImageFilterView_imageZoom 8
int styleable ImageFilterView_overlay 9
int styleable ImageFilterView_round 10
int styleable ImageFilterView_roundPercent 11
int styleable ImageFilterView_saturation 12
int styleable ImageFilterView_warmth 13
int[] styleable KeyAttribute { 0x0101031f, 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f030093, 0x7f0300e8, 0x7f03016e, 0x7f030170, 0x7f030219, 0x7f03021b, 0x7f03021d }
int styleable KeyAttribute_android_alpha 0
int styleable KeyAttribute_android_transformPivotX 1
int styleable KeyAttribute_android_transformPivotY 2
int styleable KeyAttribute_android_translationX 3
int styleable KeyAttribute_android_translationY 4
int styleable KeyAttribute_android_scaleX 5
int styleable KeyAttribute_android_scaleY 6
int styleable KeyAttribute_android_rotation 7
int styleable KeyAttribute_android_rotationX 8
int styleable KeyAttribute_android_rotationY 9
int styleable KeyAttribute_android_translationZ 10
int styleable KeyAttribute_android_elevation 11
int styleable KeyAttribute_curveFit 12
int styleable KeyAttribute_framePosition 13
int styleable KeyAttribute_motionProgress 14
int styleable KeyAttribute_motionTarget 15
int styleable KeyAttribute_transformPivotTarget 16
int styleable KeyAttribute_transitionEasing 17
int styleable KeyAttribute_transitionPathRotate 18
int[] styleable KeyCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f030093, 0x7f0300e8, 0x7f03016e, 0x7f030170, 0x7f03021b, 0x7f03021d, 0x7f03022c, 0x7f03022d, 0x7f03022e, 0x7f03022f, 0x7f030230 }
int styleable KeyCycle_android_alpha 0
int styleable KeyCycle_android_translationX 1
int styleable KeyCycle_android_translationY 2
int styleable KeyCycle_android_scaleX 3
int styleable KeyCycle_android_scaleY 4
int styleable KeyCycle_android_rotation 5
int styleable KeyCycle_android_rotationX 6
int styleable KeyCycle_android_rotationY 7
int styleable KeyCycle_android_translationZ 8
int styleable KeyCycle_android_elevation 9
int styleable KeyCycle_curveFit 10
int styleable KeyCycle_framePosition 11
int styleable KeyCycle_motionProgress 12
int styleable KeyCycle_motionTarget 13
int styleable KeyCycle_transitionEasing 14
int styleable KeyCycle_transitionPathRotate 15
int styleable KeyCycle_waveOffset 16
int styleable KeyCycle_wavePeriod 17
int styleable KeyCycle_wavePhase 18
int styleable KeyCycle_waveShape 19
int styleable KeyCycle_waveVariesBy 20
int[] styleable KeyFrame { }
int[] styleable KeyFramesAcceleration { }
int[] styleable KeyFramesVelocity { }
int[] styleable KeyPosition { 0x7f030093, 0x7f0300af, 0x7f0300e8, 0x7f0300ff, 0x7f030170, 0x7f03018b, 0x7f03018d, 0x7f03018e, 0x7f03018f, 0x7f030190, 0x7f0301c2, 0x7f03021b }
int styleable KeyPosition_curveFit 0
int styleable KeyPosition_drawPath 1
int styleable KeyPosition_framePosition 2
int styleable KeyPosition_keyPositionType 3
int styleable KeyPosition_motionTarget 4
int styleable KeyPosition_pathMotionArc 5
int styleable KeyPosition_percentHeight 6
int styleable KeyPosition_percentWidth 7
int styleable KeyPosition_percentX 8
int styleable KeyPosition_percentY 9
int styleable KeyPosition_sizePercent 10
int styleable KeyPosition_transitionEasing 11
int[] styleable KeyTimeCycle { 0x0101031f, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f030093, 0x7f0300e8, 0x7f03016e, 0x7f030170, 0x7f03021b, 0x7f03021d, 0x7f03022b, 0x7f03022c, 0x7f03022d, 0x7f03022e, 0x7f03022f }
int styleable KeyTimeCycle_android_alpha 0
int styleable KeyTimeCycle_android_translationX 1
int styleable KeyTimeCycle_android_translationY 2
int styleable KeyTimeCycle_android_scaleX 3
int styleable KeyTimeCycle_android_scaleY 4
int styleable KeyTimeCycle_android_rotation 5
int styleable KeyTimeCycle_android_rotationX 6
int styleable KeyTimeCycle_android_rotationY 7
int styleable KeyTimeCycle_android_translationZ 8
int styleable KeyTimeCycle_android_elevation 9
int styleable KeyTimeCycle_curveFit 10
int styleable KeyTimeCycle_framePosition 11
int styleable KeyTimeCycle_motionProgress 12
int styleable KeyTimeCycle_motionTarget 13
int styleable KeyTimeCycle_transitionEasing 14
int styleable KeyTimeCycle_transitionPathRotate 15
int styleable KeyTimeCycle_waveDecay 16
int styleable KeyTimeCycle_waveOffset 17
int styleable KeyTimeCycle_wavePeriod 18
int styleable KeyTimeCycle_wavePhase 19
int styleable KeyTimeCycle_waveShape 20
int[] styleable KeyTrigger { 0x7f0300e8, 0x7f030170, 0x7f030171, 0x7f030172, 0x7f03017b, 0x7f03017d, 0x7f03017e, 0x7f03021e, 0x7f03021f, 0x7f030220, 0x7f030225, 0x7f030226, 0x7f030227 }
int styleable KeyTrigger_framePosition 0
int styleable KeyTrigger_motionTarget 1
int styleable KeyTrigger_motion_postLayoutCollision 2
int styleable KeyTrigger_motion_triggerOnCollision 3
int styleable KeyTrigger_onCross 4
int styleable KeyTrigger_onNegativeCross 5
int styleable KeyTrigger_onPositiveCross 6
int styleable KeyTrigger_triggerId 7
int styleable KeyTrigger_triggerReceiver 8
int styleable KeyTrigger_triggerSlack 9
int styleable KeyTrigger_viewTransitionOnCross 10
int styleable KeyTrigger_viewTransitionOnNegativeCross 11
int styleable KeyTrigger_viewTransitionOnPositiveCross 12
int[] styleable Layout { 0x010100c4, 0x010100f4, 0x010100f5, 0x010100f7, 0x010100f8, 0x010100f9, 0x010100fa, 0x010103b5, 0x010103b6, 0x7f030043, 0x7f030044, 0x7f030045, 0x7f030062, 0x7f030084, 0x7f030085, 0x7f0300eb, 0x7f030106, 0x7f030107, 0x7f030108, 0x7f030109, 0x7f03010a, 0x7f03010b, 0x7f03010c, 0x7f03010d, 0x7f03010e, 0x7f03010f, 0x7f030110, 0x7f030111, 0x7f030112, 0x7f030113, 0x7f030114, 0x7f030115, 0x7f030116, 0x7f030117, 0x7f030118, 0x7f030119, 0x7f03011a, 0x7f03011b, 0x7f03011c, 0x7f03011d, 0x7f03011e, 0x7f03011f, 0x7f030120, 0x7f030121, 0x7f030122, 0x7f030123, 0x7f030124, 0x7f030125, 0x7f030126, 0x7f030127, 0x7f030129, 0x7f03012a, 0x7f03012b, 0x7f03012c, 0x7f03012d, 0x7f03012e, 0x7f03012f, 0x7f030130, 0x7f030131, 0x7f030132, 0x7f030133, 0x7f030134, 0x7f030135, 0x7f030136, 0x7f030137, 0x7f030138, 0x7f030139, 0x7f03013a, 0x7f03013b, 0x7f03013c, 0x7f03013d, 0x7f03013f, 0x7f030155, 0x7f030157, 0x7f03015b, 0x7f03015c }
int styleable Layout_android_orientation 0
int styleable Layout_android_layout_width 1
int styleable Layout_android_layout_height 2
int styleable Layout_android_layout_marginLeft 3
int styleable Layout_android_layout_marginTop 4
int styleable Layout_android_layout_marginRight 5
int styleable Layout_android_layout_marginBottom 6
int styleable Layout_android_layout_marginStart 7
int styleable Layout_android_layout_marginEnd 8
int styleable Layout_barrierAllowsGoneWidgets 9
int styleable Layout_barrierDirection 10
int styleable Layout_barrierMargin 11
int styleable Layout_chainUseRtl 12
int styleable Layout_constraint_referenced_ids 13
int styleable Layout_constraint_referenced_tags 14
int styleable Layout_guidelineUseRtl 15
int styleable Layout_layout_constrainedHeight 16
int styleable Layout_layout_constrainedWidth 17
int styleable Layout_layout_constraintBaseline_creator 18
int styleable Layout_layout_constraintBaseline_toBaselineOf 19
int styleable Layout_layout_constraintBaseline_toBottomOf 20
int styleable Layout_layout_constraintBaseline_toTopOf 21
int styleable Layout_layout_constraintBottom_creator 22
int styleable Layout_layout_constraintBottom_toBottomOf 23
int styleable Layout_layout_constraintBottom_toTopOf 24
int styleable Layout_layout_constraintCircle 25
int styleable Layout_layout_constraintCircleAngle 26
int styleable Layout_layout_constraintCircleRadius 27
int styleable Layout_layout_constraintDimensionRatio 28
int styleable Layout_layout_constraintEnd_toEndOf 29
int styleable Layout_layout_constraintEnd_toStartOf 30
int styleable Layout_layout_constraintGuide_begin 31
int styleable Layout_layout_constraintGuide_end 32
int styleable Layout_layout_constraintGuide_percent 33
int styleable Layout_layout_constraintHeight 34
int styleable Layout_layout_constraintHeight_default 35
int styleable Layout_layout_constraintHeight_max 36
int styleable Layout_layout_constraintHeight_min 37
int styleable Layout_layout_constraintHeight_percent 38
int styleable Layout_layout_constraintHorizontal_bias 39
int styleable Layout_layout_constraintHorizontal_chainStyle 40
int styleable Layout_layout_constraintHorizontal_weight 41
int styleable Layout_layout_constraintLeft_creator 42
int styleable Layout_layout_constraintLeft_toLeftOf 43
int styleable Layout_layout_constraintLeft_toRightOf 44
int styleable Layout_layout_constraintRight_creator 45
int styleable Layout_layout_constraintRight_toLeftOf 46
int styleable Layout_layout_constraintRight_toRightOf 47
int styleable Layout_layout_constraintStart_toEndOf 48
int styleable Layout_layout_constraintStart_toStartOf 49
int styleable Layout_layout_constraintTop_creator 50
int styleable Layout_layout_constraintTop_toBottomOf 51
int styleable Layout_layout_constraintTop_toTopOf 52
int styleable Layout_layout_constraintVertical_bias 53
int styleable Layout_layout_constraintVertical_chainStyle 54
int styleable Layout_layout_constraintVertical_weight 55
int styleable Layout_layout_constraintWidth 56
int styleable Layout_layout_constraintWidth_default 57
int styleable Layout_layout_constraintWidth_max 58
int styleable Layout_layout_constraintWidth_min 59
int styleable Layout_layout_constraintWidth_percent 60
int styleable Layout_layout_editor_absoluteX 61
int styleable Layout_layout_editor_absoluteY 62
int styleable Layout_layout_goneMarginBaseline 63
int styleable Layout_layout_goneMarginBottom 64
int styleable Layout_layout_goneMarginEnd 65
int styleable Layout_layout_goneMarginLeft 66
int styleable Layout_layout_goneMarginRight 67
int styleable Layout_layout_goneMarginStart 68
int styleable Layout_layout_goneMarginTop 69
int styleable Layout_layout_marginBaseline 70
int styleable Layout_layout_wrapBehaviorInParent 71
int styleable Layout_maxHeight 72
int styleable Layout_maxWidth 73
int styleable Layout_minHeight 74
int styleable Layout_minWidth 75
int[] styleable LinearLayoutCompat { 0x010100af, 0x010100c4, 0x01010126, 0x01010127, 0x01010128, 0x7f0300a8, 0x7f0300aa, 0x7f030158, 0x7f0301bd }
int styleable LinearLayoutCompat_android_gravity 0
int styleable LinearLayoutCompat_android_orientation 1
int styleable LinearLayoutCompat_android_baselineAligned 2
int styleable LinearLayoutCompat_android_baselineAlignedChildIndex 3
int styleable LinearLayoutCompat_android_weightSum 4
int styleable LinearLayoutCompat_divider 5
int styleable LinearLayoutCompat_dividerPadding 6
int styleable LinearLayoutCompat_measureWithLargestChild 7
int styleable LinearLayoutCompat_showDividers 8
int[] styleable LinearLayoutCompat_Layout { 0x010100b3, 0x010100f4, 0x010100f5, 0x01010181 }
int styleable LinearLayoutCompat_Layout_android_layout_gravity 0
int styleable LinearLayoutCompat_Layout_android_layout_width 1
int styleable LinearLayoutCompat_Layout_android_layout_height 2
int styleable LinearLayoutCompat_Layout_android_layout_weight 3
int[] styleable ListPopupWindow { 0x010102ac, 0x010102ad }
int styleable ListPopupWindow_android_dropDownHorizontalOffset 0
int styleable ListPopupWindow_android_dropDownVerticalOffset 1
int[] styleable MenuGroup { 0x0101000e, 0x010100d0, 0x01010194, 0x010101de, 0x010101df, 0x010101e0 }
int styleable MenuGroup_android_enabled 0
int styleable MenuGroup_android_id 1
int styleable MenuGroup_android_visible 2
int styleable MenuGroup_android_menuCategory 3
int styleable MenuGroup_android_orderInCategory 4
int styleable MenuGroup_android_checkableBehavior 5
int[] styleable MenuItem { 0x01010002, 0x0101000e, 0x010100d0, 0x01010106, 0x01010194, 0x010101de, 0x010101df, 0x010101e1, 0x010101e2, 0x010101e3, 0x010101e4, 0x010101e5, 0x0101026f, 0x7f03000f, 0x7f030023, 0x7f030024, 0x7f03002c, 0x7f030088, 0x7f0300f1, 0x7f0300f2, 0x7f03017a, 0x7f0301bc, 0x7f030212 }
int styleable MenuItem_android_icon 0
int styleable MenuItem_android_enabled 1
int styleable MenuItem_android_id 2
int styleable MenuItem_android_checked 3
int styleable MenuItem_android_visible 4
int styleable MenuItem_android_menuCategory 5
int styleable MenuItem_android_orderInCategory 6
int styleable MenuItem_android_title 7
int styleable MenuItem_android_titleCondensed 8
int styleable MenuItem_android_alphabeticShortcut 9
int styleable MenuItem_android_numericShortcut 10
int styleable MenuItem_android_checkable 11
int styleable MenuItem_android_onClick 12
int styleable MenuItem_actionLayout 13
int styleable MenuItem_actionProviderClass 14
int styleable MenuItem_actionViewClass 15
int styleable MenuItem_alphabeticModifiers 16
int styleable MenuItem_contentDescription 17
int styleable MenuItem_iconTint 18
int styleable MenuItem_iconTintMode 19
int styleable MenuItem_numericModifiers 20
int styleable MenuItem_showAsAction 21
int styleable MenuItem_tooltipText 22
int[] styleable MenuView { 0x010100ae, 0x0101012c, 0x0101012d, 0x0101012e, 0x0101012f, 0x01010130, 0x01010131, 0x7f030198, 0x7f0301d1 }
int styleable MenuView_android_windowAnimationStyle 0
int styleable MenuView_android_itemTextAppearance 1
int styleable MenuView_android_horizontalDivider 2
int styleable MenuView_android_verticalDivider 3
int styleable MenuView_android_headerBackground 4
int styleable MenuView_android_itemBackground 5
int styleable MenuView_android_itemIconDisabledAlpha 6
int styleable MenuView_preserveIconSpacing 7
int styleable MenuView_subMenuArrow 8
int[] styleable MockView { 0x7f03015d, 0x7f03015e, 0x7f03015f, 0x7f030160, 0x7f030161, 0x7f030162 }
int styleable MockView_mock_diagonalsColor 0
int styleable MockView_mock_label 1
int styleable MockView_mock_labelBackgroundColor 2
int styleable MockView_mock_labelColor 3
int styleable MockView_mock_showDiagonals 4
int styleable MockView_mock_showLabel 5
int[] styleable Motion { 0x7f03002e, 0x7f03002f, 0x7f0300af, 0x7f03016d, 0x7f03016f, 0x7f03018b, 0x7f03019b, 0x7f03019c, 0x7f03019d, 0x7f03021b }
int styleable Motion_animateCircleAngleTo 0
int styleable Motion_animateRelativeTo 1
int styleable Motion_drawPath 2
int styleable Motion_motionPathRotate 3
int styleable Motion_motionStagger 4
int styleable Motion_pathMotionArc 5
int styleable Motion_quantizeMotionInterpolator 6
int styleable Motion_quantizeMotionPhase 7
int styleable Motion_quantizeMotionSteps 8
int styleable Motion_transitionEasing 9
int[] styleable MotionEffect { 0x7f030164, 0x7f030165, 0x7f030166, 0x7f030167, 0x7f030168, 0x7f030169, 0x7f03016a, 0x7f03016b }
int styleable MotionEffect_motionEffect_alpha 0
int styleable MotionEffect_motionEffect_end 1
int styleable MotionEffect_motionEffect_move 2
int styleable MotionEffect_motionEffect_start 3
int styleable MotionEffect_motionEffect_strict 4
int styleable MotionEffect_motionEffect_translationX 5
int styleable MotionEffect_motionEffect_translationY 6
int styleable MotionEffect_motionEffect_viewTransition 7
int[] styleable MotionHelper { 0x7f03017c, 0x7f03017f }
int styleable MotionHelper_onHide 0
int styleable MotionHelper_onShow 1
int[] styleable MotionLabel { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x010100af, 0x0101014f, 0x01010164, 0x010103ac, 0x01010535, 0x7f030047, 0x7f030048, 0x7f0301b3, 0x7f0301e9, 0x7f0301ea, 0x7f0301eb, 0x7f0301ec, 0x7f0301ed, 0x7f0301f2, 0x7f0301f3, 0x7f0301f4, 0x7f0301f5, 0x7f0301f6, 0x7f0301f7, 0x7f0301f8, 0x7f0301f9 }
int styleable MotionLabel_android_textSize 0
int styleable MotionLabel_android_typeface 1
int styleable MotionLabel_android_textStyle 2
int styleable MotionLabel_android_textColor 3
int styleable MotionLabel_android_gravity 4
int styleable MotionLabel_android_text 5
int styleable MotionLabel_android_shadowRadius 6
int styleable MotionLabel_android_fontFamily 7
int styleable MotionLabel_android_autoSizeTextType 8
int styleable MotionLabel_borderRound 9
int styleable MotionLabel_borderRoundPercent 10
int styleable MotionLabel_scaleFromTextSize 11
int styleable MotionLabel_textBackground 12
int styleable MotionLabel_textBackgroundPanX 13
int styleable MotionLabel_textBackgroundPanY 14
int styleable MotionLabel_textBackgroundRotate 15
int styleable MotionLabel_textBackgroundZoom 16
int styleable MotionLabel_textOutlineColor 17
int styleable MotionLabel_textOutlineThickness 18
int styleable MotionLabel_textPanX 19
int styleable MotionLabel_textPanY 20
int styleable MotionLabel_textureBlurFactor 21
int styleable MotionLabel_textureEffect 22
int styleable MotionLabel_textureHeight 23
int styleable MotionLabel_textureWidth 24
int[] styleable MotionLayout { 0x7f030030, 0x7f030092, 0x7f030103, 0x7f030163, 0x7f03016e, 0x7f0301be }
int styleable MotionLayout_applyMotionScene 0
int styleable MotionLayout_currentState 1
int styleable MotionLayout_layoutDescription 2
int styleable MotionLayout_motionDebug 3
int styleable MotionLayout_motionProgress 4
int styleable MotionLayout_showPaths 5
int[] styleable MotionScene { 0x7f03009e, 0x7f030104 }
int styleable MotionScene_defaultDuration 0
int styleable MotionScene_layoutDuringTransition 1
int[] styleable MotionTelltales { 0x7f0301dd, 0x7f0301de, 0x7f0301df }
int styleable MotionTelltales_telltales_tailColor 0
int styleable MotionTelltales_telltales_tailScale 1
int styleable MotionTelltales_telltales_velocityMode 2
int[] styleable OnClick { 0x7f03006f, 0x7f0301dc }
int styleable OnClick_clickAction 0
int styleable OnClick_targetId 1
int[] styleable OnSwipe { 0x7f030035, 0x7f0300ac, 0x7f0300ad, 0x7f0300ae, 0x7f030140, 0x7f030153, 0x7f030156, 0x7f030173, 0x7f030178, 0x7f030181, 0x7f0301af, 0x7f0301c8, 0x7f0301c9, 0x7f0301ca, 0x7f0301cb, 0x7f0301cc, 0x7f030213, 0x7f030214, 0x7f030215 }
int styleable OnSwipe_autoCompleteMode 0
int styleable OnSwipe_dragDirection 1
int styleable OnSwipe_dragScale 2
int styleable OnSwipe_dragThreshold 3
int styleable OnSwipe_limitBoundsTo 4
int styleable OnSwipe_maxAcceleration 5
int styleable OnSwipe_maxVelocity 6
int styleable OnSwipe_moveWhenScrollAtTop 7
int styleable OnSwipe_nestedScrollFlags 8
int styleable OnSwipe_onTouchUp 9
int styleable OnSwipe_rotationCenterId 10
int styleable OnSwipe_springBoundary 11
int styleable OnSwipe_springDamping 12
int styleable OnSwipe_springMass 13
int styleable OnSwipe_springStiffness 14
int styleable OnSwipe_springStopThreshold 15
int styleable OnSwipe_touchAnchorId 16
int styleable OnSwipe_touchAnchorSide 17
int styleable OnSwipe_touchRegionId 18
int[] styleable PopupWindow { 0x01010176, 0x010102c9, 0x7f030182 }
int styleable PopupWindow_android_popupBackground 0
int styleable PopupWindow_android_popupAnimationStyle 1
int styleable PopupWindow_overlapAnchor 2
int[] styleable PopupWindowBackgroundState { 0x7f0301d0 }
int styleable PopupWindowBackgroundState_state_above_anchor 0
int[] styleable PropertySet { 0x010100dc, 0x0101031f, 0x7f030128, 0x7f03016e, 0x7f030228 }
int styleable PropertySet_android_visibility 0
int styleable PropertySet_android_alpha 1
int styleable PropertySet_layout_constraintTag 2
int styleable PropertySet_motionProgress 3
int styleable PropertySet_visibilityMode 4
int[] styleable RecycleListView { 0x7f030184, 0x7f030187 }
int styleable RecycleListView_paddingBottomNoButtons 0
int styleable RecycleListView_paddingTopNoTitle 1
int[] styleable RecyclerView { 0x010100c4, 0x010100eb, 0x010100f1, 0x7f0300c3, 0x7f0300c4, 0x7f0300c5, 0x7f0300c6, 0x7f0300c7, 0x7f030105, 0x7f0301ae, 0x7f0301c3, 0x7f0301ce }
int styleable RecyclerView_android_orientation 0
int styleable RecyclerView_android_clipToPadding 1
int styleable RecyclerView_android_descendantFocusability 2
int styleable RecyclerView_fastScrollEnabled 3
int styleable RecyclerView_fastScrollHorizontalThumbDrawable 4
int styleable RecyclerView_fastScrollHorizontalTrackDrawable 5
int styleable RecyclerView_fastScrollVerticalThumbDrawable 6
int styleable RecyclerView_fastScrollVerticalTrackDrawable 7
int styleable RecyclerView_layoutManager 8
int styleable RecyclerView_reverseLayout 9
int styleable RecyclerView_spanCount 10
int styleable RecyclerView_stackFromEnd 11
int[] styleable SearchView { 0x010100da, 0x0101011f, 0x01010220, 0x01010264, 0x7f030070, 0x7f03007f, 0x7f03009f, 0x7f0300ea, 0x7f0300f3, 0x7f030102, 0x7f03019e, 0x7f03019f, 0x7f0301b4, 0x7f0301b5, 0x7f0301d2, 0x7f0301d7, 0x7f030229 }
int styleable SearchView_android_focusable 0
int styleable SearchView_android_maxWidth 1
int styleable SearchView_android_inputType 2
int styleable SearchView_android_imeOptions 3
int styleable SearchView_closeIcon 4
int styleable SearchView_commitIcon 5
int styleable SearchView_defaultQueryHint 6
int styleable SearchView_goIcon 7
int styleable SearchView_iconifiedByDefault 8
int styleable SearchView_layout 9
int styleable SearchView_queryBackground 10
int styleable SearchView_queryHint 11
int styleable SearchView_searchHintIcon 12
int styleable SearchView_searchIcon 13
int styleable SearchView_submitBackground 14
int styleable SearchView_suggestionRowLayout 15
int styleable SearchView_voiceIcon 16
int[] styleable Spinner { 0x010100b2, 0x01010176, 0x0101017b, 0x01010262, 0x7f030196 }
int styleable Spinner_android_entries 0
int styleable Spinner_android_popupBackground 1
int styleable Spinner_android_prompt 2
int styleable Spinner_android_dropDownWidth 3
int styleable Spinner_popupTheme 4
int[] styleable State { 0x010100d0, 0x7f030086 }
int styleable State_android_id 0
int styleable State_constraints 1
int[] styleable StateListDrawable { 0x0101011c, 0x01010194, 0x01010195, 0x01010196, 0x0101030c, 0x0101030d }
int styleable StateListDrawable_android_dither 0
int styleable StateListDrawable_android_visible 1
int styleable StateListDrawable_android_variablePadding 2
int styleable StateListDrawable_android_constantSize 3
int styleable StateListDrawable_android_enterFadeDuration 4
int styleable StateListDrawable_android_exitFadeDuration 5
int[] styleable StateListDrawableItem { 0x01010199 }
int styleable StateListDrawableItem_android_drawable 0
int[] styleable StateSet { 0x7f0300a0 }
int styleable StateSet_defaultState 0
int[] styleable SwitchCompat { 0x01010124, 0x01010125, 0x01010142, 0x7f0301bf, 0x7f0301c7, 0x7f0301d8, 0x7f0301d9, 0x7f0301db, 0x7f0301fc, 0x7f0301fd, 0x7f0301fe, 0x7f030216, 0x7f030217, 0x7f030218 }
int styleable SwitchCompat_android_textOn 0
int styleable SwitchCompat_android_textOff 1
int styleable SwitchCompat_android_thumb 2
int styleable SwitchCompat_showText 3
int styleable SwitchCompat_splitTrack 4
int styleable SwitchCompat_switchMinWidth 5
int styleable SwitchCompat_switchPadding 6
int styleable SwitchCompat_switchTextAppearance 7
int styleable SwitchCompat_thumbTextPadding 8
int styleable SwitchCompat_thumbTint 9
int styleable SwitchCompat_thumbTintMode 10
int styleable SwitchCompat_track 11
int styleable SwitchCompat_trackTint 12
int styleable SwitchCompat_trackTintMode 13
int[] styleable TextAppearance { 0x01010095, 0x01010096, 0x01010097, 0x01010098, 0x0101009a, 0x0101009b, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x01010585, 0x7f0300dd, 0x7f0300e6, 0x7f0301e0, 0x7f0301f1 }
int styleable TextAppearance_android_textSize 0
int styleable TextAppearance_android_typeface 1
int styleable TextAppearance_android_textStyle 2
int styleable TextAppearance_android_textColor 3
int styleable TextAppearance_android_textColorHint 4
int styleable TextAppearance_android_textColorLink 5
int styleable TextAppearance_android_shadowColor 6
int styleable TextAppearance_android_shadowDx 7
int styleable TextAppearance_android_shadowDy 8
int styleable TextAppearance_android_shadowRadius 9
int styleable TextAppearance_android_fontFamily 10
int styleable TextAppearance_android_textFontWeight 11
int styleable TextAppearance_fontFamily 12
int styleable TextAppearance_fontVariationSettings 13
int styleable TextAppearance_textAllCaps 14
int styleable TextAppearance_textLocale 15
int[] styleable TextEffects { 0x01010095, 0x01010096, 0x01010097, 0x0101014f, 0x01010161, 0x01010162, 0x01010163, 0x01010164, 0x010103ac, 0x7f030047, 0x7f030048, 0x7f0301f0, 0x7f0301f2, 0x7f0301f3 }
int styleable TextEffects_android_textSize 0
int styleable TextEffects_android_typeface 1
int styleable TextEffects_android_textStyle 2
int styleable TextEffects_android_text 3
int styleable TextEffects_android_shadowColor 4
int styleable TextEffects_android_shadowDx 5
int styleable TextEffects_android_shadowDy 6
int styleable TextEffects_android_shadowRadius 7
int styleable TextEffects_android_fontFamily 8
int styleable TextEffects_borderRound 9
int styleable TextEffects_borderRoundPercent 10
int styleable TextEffects_textFillColor 11
int styleable TextEffects_textOutlineColor 12
int styleable TextEffects_textOutlineThickness 13
int[] styleable Toolbar { 0x010100af, 0x01010140, 0x7f030051, 0x7f030072, 0x7f030073, 0x7f030089, 0x7f03008a, 0x7f03008b, 0x7f03008c, 0x7f03008d, 0x7f03008e, 0x7f030151, 0x7f030152, 0x7f030154, 0x7f030159, 0x7f030175, 0x7f030176, 0x7f030196, 0x7f0301d3, 0x7f0301d4, 0x7f0301d5, 0x7f030204, 0x7f030205, 0x7f030206, 0x7f030207, 0x7f030208, 0x7f030209, 0x7f03020a, 0x7f03020b, 0x7f03020c }
int styleable Toolbar_android_gravity 0
int styleable Toolbar_android_minHeight 1
int styleable Toolbar_buttonGravity 2
int styleable Toolbar_collapseContentDescription 3
int styleable Toolbar_collapseIcon 4
int styleable Toolbar_contentInsetEnd 5
int styleable Toolbar_contentInsetEndWithActions 6
int styleable Toolbar_contentInsetLeft 7
int styleable Toolbar_contentInsetRight 8
int styleable Toolbar_contentInsetStart 9
int styleable Toolbar_contentInsetStartWithNavigation 10
int styleable Toolbar_logo 11
int styleable Toolbar_logoDescription 12
int styleable Toolbar_maxButtonHeight 13
int styleable Toolbar_menu 14
int styleable Toolbar_navigationContentDescription 15
int styleable Toolbar_navigationIcon 16
int styleable Toolbar_popupTheme 17
int styleable Toolbar_subtitle 18
int styleable Toolbar_subtitleTextAppearance 19
int styleable Toolbar_subtitleTextColor 20
int styleable Toolbar_title 21
int styleable Toolbar_titleMargin 22
int styleable Toolbar_titleMarginBottom 23
int styleable Toolbar_titleMarginEnd 24
int styleable Toolbar_titleMarginStart 25
int styleable Toolbar_titleMarginTop 26
int styleable Toolbar_titleMargins 27
int styleable Toolbar_titleTextAppearance 28
int styleable Toolbar_titleTextColor 29
int[] styleable Transform { 0x01010320, 0x01010321, 0x01010322, 0x01010323, 0x01010324, 0x01010325, 0x01010326, 0x01010327, 0x01010328, 0x010103fa, 0x01010440, 0x7f030219 }
int styleable Transform_android_transformPivotX 0
int styleable Transform_android_transformPivotY 1
int styleable Transform_android_translationX 2
int styleable Transform_android_translationY 3
int styleable Transform_android_scaleX 4
int styleable Transform_android_scaleY 5
int styleable Transform_android_rotation 6
int styleable Transform_android_rotationX 7
int styleable Transform_android_rotationY 8
int styleable Transform_android_translationZ 9
int styleable Transform_android_elevation 10
int styleable Transform_transformPivotTarget 11
int[] styleable Transition { 0x010100d0, 0x7f03003c, 0x7f030082, 0x7f030083, 0x7f0300bc, 0x7f030104, 0x7f03016c, 0x7f03018b, 0x7f0301cf, 0x7f03021a, 0x7f03021c }
int styleable Transition_android_id 0
int styleable Transition_autoTransition 1
int styleable Transition_constraintSetEnd 2
int styleable Transition_constraintSetStart 3
int styleable Transition_duration 4
int styleable Transition_layoutDuringTransition 5
int styleable Transition_motionInterpolator 6
int styleable Transition_pathMotionArc 7
int styleable Transition_staggered 8
int styleable Transition_transitionDisable 9
int styleable Transition_transitionFlags 10
int[] styleable Variant { 0x7f030086, 0x7f0301aa, 0x7f0301ab, 0x7f0301ac, 0x7f0301ad }
int styleable Variant_constraints 0
int styleable Variant_region_heightLessThan 1
int styleable Variant_region_heightMoreThan 2
int styleable Variant_region_widthLessThan 3
int styleable Variant_region_widthMoreThan 4
int[] styleable View { 0x01010000, 0x010100da, 0x7f030185, 0x7f030186, 0x7f0301fa }
int styleable View_android_theme 0
int styleable View_android_focusable 1
int styleable View_paddingEnd 2
int styleable View_paddingStart 3
int styleable View_theme 4
int[] styleable ViewBackgroundHelper { 0x010100d4, 0x7f030040, 0x7f030041 }
int styleable ViewBackgroundHelper_android_background 0
int styleable ViewBackgroundHelper_backgroundTint 1
int styleable ViewBackgroundHelper_backgroundTintMode 2
int[] styleable ViewStubCompat { 0x010100d0, 0x010100f2, 0x010100f3 }
int styleable ViewStubCompat_android_id 0
int styleable ViewStubCompat_android_layout 1
int styleable ViewStubCompat_android_inflatedId 2
int[] styleable ViewTransition { 0x010100d0, 0x7f030000, 0x7f030001, 0x7f03006e, 0x7f0300bc, 0x7f0300f4, 0x7f0300f5, 0x7f03016c, 0x7f030170, 0x7f030180, 0x7f03018b, 0x7f0301ba, 0x7f03021a, 0x7f030222, 0x7f030224 }
int styleable ViewTransition_android_id 0
int styleable ViewTransition_SharedValue 1
int styleable ViewTransition_SharedValueId 2
int styleable ViewTransition_clearsTag 3
int styleable ViewTransition_duration 4
int styleable ViewTransition_ifTagNotSet 5
int styleable ViewTransition_ifTagSet 6
int styleable ViewTransition_motionInterpolator 7
int styleable ViewTransition_motionTarget 8
int styleable ViewTransition_onStateTransition 9
int styleable ViewTransition_pathMotionArc 10
int styleable ViewTransition_setsTag 11
int styleable ViewTransition_transitionDisable 12
int styleable ViewTransition_upDuration 13
int styleable ViewTransition_viewTransitionMode 14
int[] styleable include { 0x7f030081 }
int styleable include_constraintSet 0
int xml backup_rules 0x7f110000
int xml data_extraction_rules 0x7f110001
int xml file_paths 0x7f110002
