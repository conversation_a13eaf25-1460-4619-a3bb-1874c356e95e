[{"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-mergeDebugResources-33:/layout/item_file.xml", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/item_file.xml"}, {"merged": "com.ccppdfreader.app-mergeDebugResources-33:/layout/activity_pdf_reader.xml", "source": "com.ccppdfreader.app-main-36:/layout/activity_pdf_reader.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-mergeDebugResources-33:/layout/view_pdf.xml", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/view_pdf.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-mergeDebugResources-33:/layout/layout_loading.xml", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/layout_loading.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-mergeDebugResources-33:/layout/activity_main.xml", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/activity_main.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-mergeDebugResources-33:/layout/activity_pdf_reader.xml", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/activity_pdf_reader.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-mergeDebugResources-33:/layout/item_pdf_page.xml", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/item_pdf_page.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-mergeDebugResources-33:/layout/activity_bookmarks.xml", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/activity_bookmarks.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-mergeDebugResources-33:/layout/item_pdf_document.xml", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/item_pdf_document.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-mergeDebugResources-33:/layout/activity_file_explorer.xml", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/activity_file_explorer.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-mergeDebugResources-33:/layout/item_bookmark.xml", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/item_bookmark.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-mergeDebugResources-33:/layout/activity_splash.xml", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/activity_splash.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-mergeDebugResources-33:/layout/activity_settings.xml", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/activity_settings.xml"}]