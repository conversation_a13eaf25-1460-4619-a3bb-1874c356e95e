[{"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/layout_view_pdf.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/view_pdf.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/anim_slide_in_right.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/anim/slide_in_right.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/layout_activity_bookmarks.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/activity_bookmarks.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/layout_layout_loading.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/layout_loading.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/menu_bottom_navigation_menu.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/menu/bottom_navigation_menu.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_history.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_history.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/anim_fade_out.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/anim/fade_out.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/mipmap-hdpi_ic_launcher.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/mipmap-hdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/layout_activity_settings.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/activity_settings.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/anim_fade_in.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/anim/fade_in.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_folder_open.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_folder_open.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_document_empty.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_document_empty.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_night_mode.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_night_mode.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_zoom_out.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_zoom_out.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_file.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_file.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/layout_item_pdf_page.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/item_pdf_page.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_launcher_foreground.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_launcher_foreground.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_share.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_share.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_bookmark_add.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_bookmark_add.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/mipmap-xhdpi_ic_launcher.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_sort.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_sort.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/xml_backup_rules.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/xml/backup_rules.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_delete.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_delete.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/mipmap-mdpi_ic_launcher.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/mipmap-mdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_pdf_document.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_pdf_document.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/mipmap-xxhdpi_ic_launcher_round.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/mipmap-xxhdpi/ic_launcher_round.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/menu_bookmarks_menu.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/menu/bookmarks_menu.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/layout_item_pdf_document.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/item_pdf_document.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/layout_activity_main.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/activity_main.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_home.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_home.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/menu_file_explorer_menu.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/menu/file_explorer_menu.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_chevron_up.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_chevron_up.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_info.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_info.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/layout_item_file.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/item_file.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_security.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_security.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_wifi_off.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_wifi_off.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_pdf.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_pdf.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/mipmap-hdpi_ic_launcher_round.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/mipmap-hdpi/ic_launcher_round.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/mipmap-xxxhdpi_ic_launcher_round.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/mipmap-xxxhdpi/ic_launcher_round.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/menu_main_menu.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/menu/main_menu.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_search.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_search.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_folder.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_folder.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_chevron_right.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_chevron_right.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/menu_pdf_reader_menu.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/menu/pdf_reader_menu.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/layout_activity_splash.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/activity_splash.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_settings.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_settings.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_bookmark.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_bookmark.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_add.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_add.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/mipmap-mdpi_ic_launcher_round.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/mipmap-mdpi/ic_launcher_round.png"}, {"merged": "com.ccppdfreader.app-merged_res-34:/layout_item_pdf_page.xml.flat", "source": "com.ccppdfreader.app-main-36:/layout/item_pdf_page.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/layout_activity_pdf_reader.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/activity_pdf_reader.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/layout_item_bookmark.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/item_bookmark.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/xml_file_paths.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/xml/file_paths.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_zoom_in.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_zoom_in.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_close.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_close.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/anim_scale_in.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/anim/scale_in.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_rotate.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_rotate.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/anim_slide_out_left.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/anim/slide_out_left.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_refresh.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_refresh.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_favorite.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_favorite.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/mipmap-xhdpi_ic_launcher_round.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/mipmap-xhdpi/ic_launcher_round.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_chevron_down.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_chevron_down.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_arrow_forward.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_arrow_forward.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_storage.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_storage.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_error.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_error.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_arrow_back.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_arrow_back.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_more_vert.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_more_vert.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_folder_empty.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_folder_empty.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_menu.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_menu.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/drawable_ic_chevron_left.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/drawable/ic_chevron_left.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/xml_data_extraction_rules.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/xml/data_extraction_rules.xml"}, {"merged": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-merged_res-34:/layout_activity_file_explorer.xml.flat", "source": "/Users/<USER>/.gradle/daemon/8.12/com.ccppdfreader.app-main-36:/layout/activity_file_explorer.xml"}, {"merged": "com.ccppdfreader.app-merged_res-34:/layout_activity_pdf_reader.xml.flat", "source": "com.ccppdfreader.app-main-36:/layout/activity_pdf_reader.xml"}, {"merged": "com.ccppdfreader.app-merged_res-34:/layout_view_pdf.xml.flat", "source": "com.ccppdfreader.app-main-36:/layout/view_pdf.xml"}]