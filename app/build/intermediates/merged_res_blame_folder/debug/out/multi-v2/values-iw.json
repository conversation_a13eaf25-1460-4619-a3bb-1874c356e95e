{"logs": [{"outputFile": "com.ccppdfreader.app-mergeDebugResources-32:/values-iw/values-iw.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e38e0b0290a761211bd8aa2e2c31f15b/transformed/appcompat-1.6.1/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "361,466,566,674,758,860,976,1055,1133,1224,1318,1412,1506,1606,1699,1794,1887,1978,2070,2151,2256,2359,2457,2562,2664,2766,2920,8979", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "461,561,669,753,855,971,1050,1128,1219,1313,1407,1501,1601,1694,1789,1882,1973,2065,2146,2251,2354,2452,2557,2659,2761,2915,3012,9056"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/691c3c025c1f73e07ed75c4c26cdc6a0/transformed/material-1.11.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,311,388,463,540,640,731,824,937,1017,1082,1170,1240,1303,1395,1458,1518,1577,1640,1701,1755,1857,1914,1973,2027,2095,2206,2287,2369,2501,2572,2645,2769,2857,2933,2986,3040,3106,3179,3255,3341,3419,3489,3564,3646,3714,3815,3900,3970,4060,4151,4225,4298,4387,4438,4519,4586,4668,4753,4815,4879,4942,5010,5104,5199,5289,5386,5443,5501", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,76,74,76,99,90,92,112,79,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,123,87,75,52,53,65,72,75,85,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74", "endOffsets": "306,383,458,535,635,726,819,932,1012,1077,1165,1235,1298,1390,1453,1513,1572,1635,1696,1750,1852,1909,1968,2022,2090,2201,2282,2364,2496,2567,2640,2764,2852,2928,2981,3035,3101,3174,3250,3336,3414,3484,3559,3641,3709,3810,3895,3965,4055,4146,4220,4293,4382,4433,4514,4581,4663,4748,4810,4874,4937,5005,5099,5194,5284,5381,5438,5496,5571"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3017,3094,3169,3246,3346,4134,4227,4340,4420,4485,4573,4643,4706,4798,4861,4921,4980,5043,5104,5158,5260,5317,5376,5430,5498,5609,5690,5772,5904,5975,6048,6172,6260,6336,6389,6443,6509,6582,6658,6744,6822,6892,6967,7049,7117,7218,7303,7373,7463,7554,7628,7701,7790,7841,7922,7989,8071,8156,8218,8282,8345,8413,8507,8602,8692,8789,8846,8904", "endLines": "6,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "endColumns": "12,76,74,76,99,90,92,112,79,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,123,87,75,52,53,65,72,75,85,77,69,74,81,67,100,84,69,89,90,73,72,88,50,80,66,81,84,61,63,62,67,93,94,89,96,56,57,74", "endOffsets": "356,3089,3164,3241,3341,3432,4222,4335,4415,4480,4568,4638,4701,4793,4856,4916,4975,5038,5099,5153,5255,5312,5371,5425,5493,5604,5685,5767,5899,5970,6043,6167,6255,6331,6384,6438,6504,6577,6653,6739,6817,6887,6962,7044,7112,7213,7298,7368,7458,7549,7623,7696,7785,7836,7917,7984,8066,8151,8213,8277,8340,8408,8502,8597,8687,8784,8841,8899,8974"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "39,40,41,42,43,44,45,110", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3437,3531,3633,3730,3827,3928,4028,9061", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3526,3628,3725,3822,3923,4023,4129,9157"}}]}]}