{"logs": [{"outputFile": "com.ccppdfreader.app-mergeDebugResources-34:/values-pl/values-pl.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/3c02378f60fa35bab5544f4e5368d727/transformed/material-1.9.0/res/values-pl/values-pl.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,386,461,536,615,719,814,899,1016,1098,1162,1243,1307,1368,1479,1543,1611,1665,1734,1796,1850,1961,2022,2084,2138,2210,2339,2428,2510,2659,2741,2824,2911,2965,3016,3082,3153,3229,3318,3395,3473,3551,3627,3717,3790,3885,3982,4054,4128,4228,4280,4346,4434,4524,4586,4650,4713,4820,4909,5008,5096,5154,5209", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,74,74,78,103,94,84,116,81,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,81,148,81,82,86,53,50,65,70,75,88,76,77,77,75,89,72,94,96,71,73,99,51,65,87,89,61,63,62,106,88,98,87,57,54,75", "endOffsets": "381,456,531,610,714,809,894,1011,1093,1157,1238,1302,1363,1474,1538,1606,1660,1729,1791,1845,1956,2017,2079,2133,2205,2334,2423,2505,2654,2736,2819,2906,2960,3011,3077,3148,3224,3313,3390,3468,3546,3622,3712,3785,3880,3977,4049,4123,4223,4275,4341,4429,4519,4581,4645,4708,4815,4904,5003,5091,5149,5204,5280"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3197,3272,3347,3426,3530,4362,4447,4564,5148,5279,5360,5424,5485,5596,5660,5728,5782,5851,5913,5967,6078,6139,6201,6255,6327,6456,6545,6627,6776,6858,6941,7028,7082,7133,7199,7270,7346,7435,7512,7590,7668,7744,7834,7907,8002,8099,8171,8245,8345,8397,8463,8551,8641,8703,8767,8830,8937,9026,9125,9213,9271,9326", "endLines": "7,36,37,38,39,40,48,49,50,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "endColumns": "12,74,74,78,103,94,84,116,81,63,80,63,60,110,63,67,53,68,61,53,110,60,61,53,71,128,88,81,148,81,82,86,53,50,65,70,75,88,76,77,77,75,89,72,94,96,71,73,99,51,65,87,89,61,63,62,106,88,98,87,57,54,75", "endOffsets": "431,3267,3342,3421,3525,3620,4442,4559,4641,5207,5355,5419,5480,5591,5655,5723,5777,5846,5908,5962,6073,6134,6196,6250,6322,6451,6540,6622,6771,6853,6936,7023,7077,7128,7194,7265,7341,7430,7507,7585,7663,7739,7829,7902,7997,8094,8166,8240,8340,8392,8458,8546,8636,8698,8762,8825,8932,9021,9120,9208,9266,9321,9397"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e38e0b0290a761211bd8aa2e2c31f15b/transformed/appcompat-1.6.1/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,430,516,623,742,821,897,988,1081,1176,1270,1371,1464,1559,1654,1745,1836,1918,2027,2127,2226,2335,2447,2558,2721,2817", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "215,317,425,511,618,737,816,892,983,1076,1171,1265,1366,1459,1554,1649,1740,1831,1913,2022,2122,2221,2330,2442,2553,2716,2812,2895"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "436,551,653,761,847,954,1073,1152,1228,1319,1412,1507,1601,1702,1795,1890,1985,2076,2167,2249,2358,2458,2557,2666,2778,2889,3052,9595", "endColumns": "114,101,107,85,106,118,78,75,90,92,94,93,100,92,94,94,90,90,81,108,99,98,108,111,110,162,95,82", "endOffsets": "546,648,756,842,949,1068,1147,1223,1314,1407,1502,1596,1697,1790,1885,1980,2071,2162,2244,2353,2453,2552,2661,2773,2884,3047,3143,9673"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/29b741c7658a3f1a25fa9a544d30ed40/transformed/imagepicker-2.1/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,104,192,303,401,512,606,673,866,914,978,1028,1076,1142,1200,1253,1300", "endLines": "2,3,4,5,6,7,8,10,11,12,13,14,15,16,17,18,19", "endColumns": "48,87,110,97,110,93,66,147,47,63,49,47,65,57,52,46,46", "endOffsets": "99,187,298,396,507,601,668,861,909,973,1023,1071,1137,1195,1248,1295,1342"}, "to": {"startLines": "35,51,52,53,54,55,57,111,115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3148,4646,4734,4845,4943,5054,5212,9402,9779,9827,9891,9941,9989,10055,10113,10166,10213", "endLines": "35,51,52,53,54,55,57,112,115,116,117,118,119,120,121,122,123", "endColumns": "48,87,110,97,110,93,66,147,47,63,49,47,65,57,52,46,46", "endOffsets": "3192,4729,4840,4938,5049,5143,5274,9590,9822,9886,9936,9984,10050,10108,10161,10208,10255"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/res/values-pl/values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "41,42,43,44,45,46,47,114", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3625,3722,3824,3922,4021,4135,4240,9678", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "3717,3819,3917,4016,4130,4235,4357,9774"}}]}]}