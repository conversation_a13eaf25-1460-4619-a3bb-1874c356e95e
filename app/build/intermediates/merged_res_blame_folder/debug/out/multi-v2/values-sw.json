{"logs": [{"outputFile": "com.ccppdfreader.app-mergeDebugResources-32:/values-sw/values-sw.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/691c3c025c1f73e07ed75c4c26cdc6a0/transformed/material-1.11.0/res/values-sw/values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,335,409,482,579,668,767,896,979,1047,1139,1212,1275,1361,1423,1486,1551,1619,1682,1736,1868,1925,1987,2041,2115,2253,2334,2414,2546,2631,2718,2859,2947,3026,3080,3133,3199,3271,3353,3443,3528,3600,3675,3746,3819,3925,4022,4096,4191,4288,4362,4447,4547,4600,4685,4753,4841,4931,4993,5057,5120,5187,5304,5416,5527,5638,5696,5753", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,73,72,96,88,98,128,82,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,140,87,78,53,52,65,71,81,89,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80", "endOffsets": "254,330,404,477,574,663,762,891,974,1042,1134,1207,1270,1356,1418,1481,1546,1614,1677,1731,1863,1920,1982,2036,2110,2248,2329,2409,2541,2626,2713,2854,2942,3021,3075,3128,3194,3266,3348,3438,3523,3595,3670,3741,3814,3920,4017,4091,4186,4283,4357,4442,4542,4595,4680,4748,4836,4926,4988,5052,5115,5182,5299,5411,5522,5633,5691,5748,5829"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3005,3081,3155,3228,3325,4137,4236,4365,4448,4516,4608,4681,4744,4830,4892,4955,5020,5088,5151,5205,5337,5394,5456,5510,5584,5722,5803,5883,6015,6100,6187,6328,6416,6495,6549,6602,6668,6740,6822,6912,6997,7069,7144,7215,7288,7394,7491,7565,7660,7757,7831,7916,8016,8069,8154,8222,8310,8400,8462,8526,8589,8656,8773,8885,8996,9107,9165,9222", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,75,73,72,96,88,98,128,82,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,131,84,86,140,87,78,53,52,65,71,81,89,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80", "endOffsets": "304,3076,3150,3223,3320,3409,4231,4360,4443,4511,4603,4676,4739,4825,4887,4950,5015,5083,5146,5200,5332,5389,5451,5505,5579,5717,5798,5878,6010,6095,6182,6323,6411,6490,6544,6597,6663,6735,6817,6907,6992,7064,7139,7210,7283,7389,7486,7560,7655,7752,7826,7911,8011,8064,8149,8217,8305,8395,8457,8521,8584,8651,8768,8880,8991,9102,9160,9217,9298"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3414,3508,3610,3707,3808,3915,4022,9386", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "3503,3605,3702,3803,3910,4017,4132,9482"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e38e0b0290a761211bd8aa2e2c31f15b/transformed/appcompat-1.6.1/res/values-sw/values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,412,511,619,709,814,931,1014,1096,1187,1280,1375,1469,1569,1662,1757,1851,1942,2033,2115,2216,2324,2423,2530,2642,2746,2908,9303", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "407,506,614,704,809,926,1009,1091,1182,1275,1370,1464,1564,1657,1752,1846,1937,2028,2110,2211,2319,2418,2525,2637,2741,2903,3000,9381"}}]}]}