{"logs": [{"outputFile": "com.ccppdfreader.app-mergeDebugResources-34:/values-mn/values-mn.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "38,39,40,41,42,43,44,103", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3414,3512,3614,3715,3813,3918,4030,8740", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "3507,3609,3710,3808,3913,4025,4144,8836"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e38e0b0290a761211bd8aa2e2c31f15b/transformed/appcompat-1.6.1/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,529,638,724,830,944,1027,1108,1199,1292,1387,1483,1580,1673,1767,1859,1950,2040,2120,2227,2330,2427,2534,2636,2749,2908,8659", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "424,524,633,719,825,939,1022,1103,1194,1287,1382,1478,1575,1668,1762,1854,1945,2035,2115,2222,2325,2422,2529,2631,2744,2903,3002,8735"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/3c02378f60fa35bab5544f4e5368d727/transformed/material-1.9.0/res/values-mn/values-mn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,500,587,672,770,889,974,1039,1137,1218,1277,1370,1432,1495,1553,1624,1686,1740,1861,1918,1979,2033,2104,2237,2321,2404,2537,2619,2697,2787,2841,2892,2958,3029,3107,3193,3268,3346,3426,3509,3597,3676,3766,3859,3933,4003,4094,4148,4215,4299,4384,4446,4510,4573,4677,4783,4880,4985,5043,5098", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,78,76,78,86,84,97,118,84,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,82,132,81,77,89,53,50,65,70,77,85,74,77,79,82,87,78,89,92,73,69,90,53,66,83,84,61,63,62,103,105,96,104,57,54,83", "endOffsets": "260,339,416,495,582,667,765,884,969,1034,1132,1213,1272,1365,1427,1490,1548,1619,1681,1735,1856,1913,1974,2028,2099,2232,2316,2399,2532,2614,2692,2782,2836,2887,2953,3024,3102,3188,3263,3341,3421,3504,3592,3671,3761,3854,3928,3998,4089,4143,4210,4294,4379,4441,4505,4568,4672,4778,4875,4980,5038,5093,5177"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3086,3163,3242,3329,4149,4247,4366,4451,4516,4614,4695,4754,4847,4909,4972,5030,5101,5163,5217,5338,5395,5456,5510,5581,5714,5798,5881,6014,6096,6174,6264,6318,6369,6435,6506,6584,6670,6745,6823,6903,6986,7074,7153,7243,7336,7410,7480,7571,7625,7692,7776,7861,7923,7987,8050,8154,8260,8357,8462,8520,8575", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101", "endColumns": "12,78,76,78,86,84,97,118,84,64,97,80,58,92,61,62,57,70,61,53,120,56,60,53,70,132,83,82,132,81,77,89,53,50,65,70,77,85,74,77,79,82,87,78,89,92,73,69,90,53,66,83,84,61,63,62,103,105,96,104,57,54,83", "endOffsets": "310,3081,3158,3237,3324,3409,4242,4361,4446,4511,4609,4690,4749,4842,4904,4967,5025,5096,5158,5212,5333,5390,5451,5505,5576,5709,5793,5876,6009,6091,6169,6259,6313,6364,6430,6501,6579,6665,6740,6818,6898,6981,7069,7148,7238,7331,7405,7475,7566,7620,7687,7771,7856,7918,7982,8045,8149,8255,8352,8457,8515,8570,8654"}}]}]}