<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:background="?android:attr/selectableItemBackground">

    <!-- File Icon -->
    <ImageView
        android:id="@+id/imageViewFileIcon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/ic_file"
        android:layout_marginEnd="16dp"
        android:layout_gravity="center_vertical" />

    <!-- File Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textViewFileName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="File Name"
            android:textSize="16sp"
            android:textColor="?android:attr/textColorPrimary"
            android:maxLines="1"
            android:ellipsize="end" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="4dp">

            <TextView
                android:id="@+id/textViewFileSize"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="1.2 MB"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary" />

            <TextView
                android:id="@+id/textViewFileDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="• Today"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary"
                android:layout_marginStart="8dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
