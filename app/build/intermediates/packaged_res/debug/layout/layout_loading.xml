<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="@dimen/padding_extra_large">

    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/progress_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminate="true"
        app:indicatorColor="?attr/colorPrimary"
        app:trackColor="?attr/colorSurfaceVariant"
        app:indicatorSize="48dp"
        app:trackThickness="4dp" />

    <TextView
        android:id="@+id/tv_loading_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_medium"
        android:text="Loading..."
        android:textAppearance="@style/TextAppearance.CCPPDFReader.Body1"
        android:textColor="?attr/colorOnSurfaceVariant" />

</LinearLayout>
