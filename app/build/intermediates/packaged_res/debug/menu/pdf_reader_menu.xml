<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <item
        android:id="@+id/action_search"
        android:title="@string/action_search"
        android:icon="@drawable/ic_search"
        app:iconTint="@android:color/white"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_bookmark"
        android:title="@string/action_bookmark"
        android:icon="@drawable/ic_bookmark"
        app:iconTint="@android:color/white"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_share"
        android:title="@string/action_share"
        android:icon="@drawable/ic_share"
        app:iconTint="@android:color/white"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_night_mode"
        android:title="Night Mode"
        android:icon="@drawable/ic_night_mode"
        app:iconTint="@android:color/white"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_rotate"
        android:title="Rotate"
        android:icon="@drawable/ic_rotate"
        app:iconTint="@android:color/white"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_fit_width"
        android:title="Fit Width"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_fit_height"
        android:title="Fit Height"
        app:showAsAction="never" />

</menu>
