<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_pdf_reader" modulePackage="com.ccppdfreader.app" filePath="app/src/main/res/layout/activity_pdf_reader.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_pdf_reader_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="212" endOffset="53"/></Target><Target id="@+id/pdf_view" view="com.github.barteksc.pdfviewer.PDFView"><Expressions/><location startLine="11" startOffset="4" endLine="15" endOffset="49"/></Target><Target id="@+id/app_bar_layout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="18" startOffset="4" endLine="35" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="26" startOffset="8" endLine="33" endOffset="47"/></Target><Target id="@+id/bottom_controls" view="LinearLayout"><Expressions/><location startLine="38" startOffset="4" endLine="141" endOffset="18"/></Target><Target id="@+id/btn_previous_page" view="ImageButton"><Expressions/><location startLine="57" startOffset="12" endLine="64" endOffset="49"/></Target><Target id="@+id/tv_page_indicator" view="TextView"><Expressions/><location startLine="66" startOffset="12" endLine="74" endOffset="43"/></Target><Target id="@+id/btn_next_page" view="ImageButton"><Expressions/><location startLine="76" startOffset="12" endLine="83" endOffset="49"/></Target><Target id="@+id/slider_page" view="com.google.android.material.slider.Slider"><Expressions/><location startLine="88" startOffset="8" endLine="98" endOffset="30"/></Target><Target id="@+id/btn_zoom_out" view="ImageButton"><Expressions/><location startLine="108" startOffset="12" endLine="116" endOffset="49"/></Target><Target id="@+id/tv_zoom_level" view="TextView"><Expressions/><location startLine="118" startOffset="12" endLine="127" endOffset="35"/></Target><Target id="@+id/btn_zoom_in" view="ImageButton"><Expressions/><location startLine="129" startOffset="12" endLine="137" endOffset="49"/></Target><Target id="@+id/search_container" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="144" startOffset="4" endLine="199" endOffset="55"/></Target><Target id="@+id/et_search" view="EditText"><Expressions/><location startLine="161" startOffset="12" endLine="171" endOffset="42"/></Target><Target id="@+id/btn_search_previous" view="ImageButton"><Expressions/><location startLine="173" startOffset="12" endLine="179" endOffset="62"/></Target><Target id="@+id/btn_search_next" view="ImageButton"><Expressions/><location startLine="181" startOffset="12" endLine="187" endOffset="58"/></Target><Target id="@+id/btn_close_search" view="ImageButton"><Expressions/><location startLine="189" startOffset="12" endLine="195" endOffset="59"/></Target><Target id="@+id/progress_loading" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="202" startOffset="4" endLine="210" endOffset="36"/></Target></Targets></Layout>