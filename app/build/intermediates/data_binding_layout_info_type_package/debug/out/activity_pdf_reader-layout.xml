<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_pdf_reader" modulePackage="com.ccppdfreader.app" filePath="app/src/main/res/layout/activity_pdf_reader.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_pdf_reader_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="72" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="9" startOffset="4" endLine="15" endOffset="47"/></Target><Target id="@+id/layoutPageInfo" view="LinearLayout"><Expressions/><location startLine="18" startOffset="4" endLine="45" endOffset="18"/></Target><Target id="@+id/textViewPageInfo" view="TextView"><Expressions/><location startLine="27" startOffset="8" endLine="34" endOffset="64"/></Target><Target id="@+id/textViewProgress" view="TextView"><Expressions/><location startLine="36" startOffset="8" endLine="43" endOffset="46"/></Target><Target id="@+id/pdfView" view="com.ccppdfreader.app.views.PDFView"><Expressions/><location startLine="55" startOffset="8" endLine="58" endOffset="50"/></Target><Target id="@+id/textViewPdfPlaceholder" view="TextView"><Expressions/><location startLine="61" startOffset="8" endLine="68" endOffset="39"/></Target></Targets></Layout>