<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_pdf_document" modulePackage="com.ccppdfreader.app" filePath="app/src/main/res/layout/item_pdf_document.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_pdf_document_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="121" endOffset="51"/></Target><Target id="@+id/iv_pdf_icon" view="ImageView"><Expressions/><location startLine="22" startOffset="8" endLine="30" endOffset="55"/></Target><Target id="@+id/iv_favorite" view="ImageView"><Expressions/><location startLine="33" startOffset="8" endLine="43" endOffset="40"/></Target><Target id="@+id/tv_document_title" view="TextView"><Expressions/><location startLine="46" startOffset="8" endLine="59" endOffset="55"/></Target><Target id="@+id/tv_document_info" view="TextView"><Expressions/><location startLine="62" startOffset="8" endLine="75" endOffset="59"/></Target><Target id="@+id/progress_reading" view="com.google.android.material.progressindicator.LinearProgressIndicator"><Expressions/><location startLine="78" startOffset="8" endLine="91" endOffset="33"/></Target><Target id="@+id/tv_progress" view="TextView"><Expressions/><location startLine="94" startOffset="8" endLine="105" endOffset="46"/></Target><Target id="@+id/btn_more_options" view="ImageButton"><Expressions/><location startLine="108" startOffset="8" endLine="117" endOffset="61"/></Target></Targets></Layout>