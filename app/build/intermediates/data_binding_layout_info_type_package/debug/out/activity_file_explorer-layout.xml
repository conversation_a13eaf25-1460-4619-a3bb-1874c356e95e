<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_file_explorer" modulePackage="com.ccppdfreader.app" filePath="app/src/main/res/layout/activity_file_explorer.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_file_explorer_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="102" endOffset="53"/></Target><Target id="@+id/app_bar_layout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="23" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="15" startOffset="8" endLine="21" endOffset="49"/></Target><Target id="@+id/recycler_files" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="32" startOffset="8" endLine="43" endOffset="55"/></Target><Target id="@+id/progress_loading" view="com.google.android.material.progressindicator.CircularProgressIndicator"><Expressions/><location startLine="46" startOffset="8" endLine="57" endOffset="40"/></Target><Target id="@+id/layout_empty" view="LinearLayout"><Expressions/><location startLine="60" startOffset="8" endLine="98" endOffset="22"/></Target></Targets></Layout>