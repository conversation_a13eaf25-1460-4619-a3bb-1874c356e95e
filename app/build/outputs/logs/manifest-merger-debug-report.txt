-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:94:9-102:20
	android:grantUriPermissions
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:98:13-47
	android:authorities
		INJECTED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:96:13-64
	android:exported
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:97:13-37
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:95:13-62
manifest
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:2:1-106:12
INJECTED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:2:1-106:12
INJECTED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:2:1-106:12
INJECTED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:2:1-106:12
MERGED from [androidx.databinding:viewbinding:8.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/92b84eeb7f4ef823fd29e58cd2516480/transformed/viewbinding-8.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.12/transforms/af02ef9184c45cd0d39c8e65c096a281/transformed/constraintlayout-2.1.4/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/6d9b7768b057881b7c0db5280eefa4e8/transformed/appcompat-resources-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/e38e0b0290a761211bd8aa2e2c31f15b/transformed/appcompat-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] /Users/<USER>/.gradle/caches/8.12/transforms/c23182c48dbd6d129f9e370b42c4d631/transformed/recyclerview-1.3.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/8.12/transforms/d04d960ffca4237790ec24afe67f3104/transformed/fragment-1.3.6/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] /Users/<USER>/.gradle/caches/8.12/transforms/5298fdc2ddb151a131bc0cdd5208c101/transformed/activity-1.6.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d54b46b106b5d0d37b32569e3cda350f/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/a1ff2a6ef8fe3c41de8c51f8ad2d5633/transformed/emoji2-views-helper-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/bb600d56bb230801259e3b367fb50a1e/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:17:1-23:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d9ae424a55d59efca75f0554e8151343/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/ceea67894f795cbd94e776a6dd1777f2/transformed/lifecycle-viewmodel-2.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/4b96bc7caf6633d3bfcdc57e6a1acc20/transformed/lifecycle-viewmodel-savedstate-2.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/3f449aca249bd7ddde6d273a8b927487/transformed/core-ktx-1.12.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a5df99489af3bb248968b705c354f1a7/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1bc69160b0c78dc31472c4f4b1c23809/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/214f6d7828291f92d2f1d169bccc5cb3/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/3c1175300d8f4168e5061bfaa34de220/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8fdb30fdece0771424cc113d8ede5467/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4dbadb3e41cbeee8168608c03081643/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/ff3ea0068e9ca4ef9b85a45ae310854a/transformed/lifecycle-runtime-2.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/897a23407a641fd2c256fdc2efc74082/transformed/savedstate-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/cd9e43af57ee1d6daaa9371e4735e85b/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/842e02f5d0c9c46d6c640d9d5fdd5a0c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/0132733321e61c61de6fa2fd54eccf74/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a2f7cfd00a55b4e5d19529717e6876fa/transformed/tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3a6825d74634d52b771dbb7588a6e8a2/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/f01dc7926de8b47a05dd9ae110def208/transformed/lifecycle-livedata-core-2.5.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/4dcabb2506fa3abd32fdcdb53bfa9c61/transformed/core-runtime-2.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/e81c50d5bf5fddfbbdd6d2c43c6361f0/transformed/annotation-experimental-1.3.0/AndroidManifest.xml:17:1-22:12
	package
		INJECTED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:6:5-7:38
	android:maxSdkVersion
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:7:9-35
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:6:22-77
uses-permission#android.permission.READ_MEDIA_DOCUMENTS
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:8:5-79
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:8:22-76
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:9:5-11:40
	android:maxSdkVersion
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:10:9-35
	tools:ignore
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:11:9-37
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:9:22-78
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:14:5-67
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:14:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:15:5-79
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:15:22-76
application
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:17:5-104:19
INJECTED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:17:5-104:19
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.12/transforms/af02ef9184c45cd0d39c8e65c096a281/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.12/transforms/af02ef9184c45cd0d39c8e65c096a281/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4dbadb3e41cbeee8168608c03081643/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4dbadb3e41cbeee8168608c03081643/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:25:5-35:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/842e02f5d0c9c46d6c640d9d5fdd5a0c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/842e02f5d0c9c46d6c640d9d5fdd5a0c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/0132733321e61c61de6fa2fd54eccf74/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/0132733321e61c61de6fa2fd54eccf74/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:26:9-52
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
	android:label
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:23:9-41
	android:fullBackupContent
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:21:9-54
	android:roundIcon
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:24:9-54
	tools:targetApi
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:27:9-29
	android:icon
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:22:9-43
	android:allowBackup
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:19:9-35
	android:theme
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:25:9-57
	android:dataExtractionRules
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:20:9-65
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:18:9-48
activity#com.ccppdfreader.app.activities.SplashActivity
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:30:9-38:20
	android:exported
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:32:13-36
	android:theme
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:33:13-54
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:31:13-54
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:34:13-37:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:35:17-69
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:35:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:36:17-77
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:36:27-74
activity#com.ccppdfreader.app.activities.MainActivity
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:41:9-46:58
	android:windowSoftInputMode
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:46:13-55
	android:exported
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:43:13-37
	android:configChanges
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:45:13-74
	android:theme
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:44:13-54
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:42:13-52
activity#com.ccppdfreader.app.activities.PDFReaderActivity
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:49:9-70:20
	android:windowSoftInputMode
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:54:13-55
	android:exported
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:51:13-36
	android:configChanges
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:53:13-74
	android:theme
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:52:13-65
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:50:13-57
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:mimeType:application/pdf
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:57:13-62:29
action#android.intent.action.VIEW
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:58:17-69
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:58:25-66
category#android.intent.category.DEFAULT
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:59:17-76
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:59:27-73
category#android.intent.category.BROWSABLE
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:60:17-78
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:60:27-75
data
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:61:17-60
	android:scheme
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:67:23-44
	android:pathPattern
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:68:23-53
	android:mimeType
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:61:23-57
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.DEFAULT+data:pathPattern:.*\\.pdf+data:scheme:file
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:64:13-69:29
activity#com.ccppdfreader.app.activities.SettingsActivity
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:73:9-77:69
	android:parentActivityName
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:77:13-66
	android:exported
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:75:13-37
	android:theme
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:76:13-54
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:74:13-56
activity#com.ccppdfreader.app.activities.FileExplorerActivity
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:80:9-84:69
	android:parentActivityName
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:84:13-66
	android:exported
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:82:13-37
	android:theme
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:83:13-54
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:81:13-60
activity#com.ccppdfreader.app.activities.BookmarksActivity
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:87:9-91:69
	android:parentActivityName
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:91:13-66
	android:exported
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:89:13-37
	android:theme
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:90:13-54
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:88:13-57
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:99:13-101:54
	android:resource
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:101:17-51
	android:name
		ADDED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml:100:17-67
uses-sdk
INJECTED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/92b84eeb7f4ef823fd29e58cd2516480/transformed/viewbinding-8.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/92b84eeb7f4ef823fd29e58cd2516480/transformed/viewbinding-8.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.12/transforms/af02ef9184c45cd0d39c8e65c096a281/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.12/transforms/af02ef9184c45cd0d39c8e65c096a281/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/6d9b7768b057881b7c0db5280eefa4e8/transformed/appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/6d9b7768b057881b7c0db5280eefa4e8/transformed/appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/e38e0b0290a761211bd8aa2e2c31f15b/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/e38e0b0290a761211bd8aa2e2c31f15b/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] /Users/<USER>/.gradle/caches/8.12/transforms/c23182c48dbd6d129f9e370b42c4d631/transformed/recyclerview-1.3.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] /Users/<USER>/.gradle/caches/8.12/transforms/c23182c48dbd6d129f9e370b42c4d631/transformed/recyclerview-1.3.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/8.12/transforms/d04d960ffca4237790ec24afe67f3104/transformed/fragment-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/8.12/transforms/d04d960ffca4237790ec24afe67f3104/transformed/fragment-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] /Users/<USER>/.gradle/caches/8.12/transforms/5298fdc2ddb151a131bc0cdd5208c101/transformed/activity-1.6.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] /Users/<USER>/.gradle/caches/8.12/transforms/5298fdc2ddb151a131bc0cdd5208c101/transformed/activity-1.6.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d54b46b106b5d0d37b32569e3cda350f/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d54b46b106b5d0d37b32569e3cda350f/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/a1ff2a6ef8fe3c41de8c51f8ad2d5633/transformed/emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/a1ff2a6ef8fe3c41de8c51f8ad2d5633/transformed/emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/bb600d56bb230801259e3b367fb50a1e/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/bb600d56bb230801259e3b367fb50a1e/transformed/customview-poolingcontainer-1.0.0/AndroidManifest.xml:20:5-21:38
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d9ae424a55d59efca75f0554e8151343/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d9ae424a55d59efca75f0554e8151343/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/ceea67894f795cbd94e776a6dd1777f2/transformed/lifecycle-viewmodel-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/ceea67894f795cbd94e776a6dd1777f2/transformed/lifecycle-viewmodel-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/4b96bc7caf6633d3bfcdc57e6a1acc20/transformed/lifecycle-viewmodel-savedstate-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/4b96bc7caf6633d3bfcdc57e6a1acc20/transformed/lifecycle-viewmodel-savedstate-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/3f449aca249bd7ddde6d273a8b927487/transformed/core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/3f449aca249bd7ddde6d273a8b927487/transformed/core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a5df99489af3bb248968b705c354f1a7/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a5df99489af3bb248968b705c354f1a7/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1bc69160b0c78dc31472c4f4b1c23809/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/1bc69160b0c78dc31472c4f4b1c23809/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/214f6d7828291f92d2f1d169bccc5cb3/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/214f6d7828291f92d2f1d169bccc5cb3/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/3c1175300d8f4168e5061bfaa34de220/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/3c1175300d8f4168e5061bfaa34de220/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8fdb30fdece0771424cc113d8ede5467/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/8fdb30fdece0771424cc113d8ede5467/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4dbadb3e41cbeee8168608c03081643/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4dbadb3e41cbeee8168608c03081643/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/ff3ea0068e9ca4ef9b85a45ae310854a/transformed/lifecycle-runtime-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/ff3ea0068e9ca4ef9b85a45ae310854a/transformed/lifecycle-runtime-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/897a23407a641fd2c256fdc2efc74082/transformed/savedstate-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/897a23407a641fd2c256fdc2efc74082/transformed/savedstate-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/cd9e43af57ee1d6daaa9371e4735e85b/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/cd9e43af57ee1d6daaa9371e4735e85b/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/842e02f5d0c9c46d6c640d9d5fdd5a0c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/842e02f5d0c9c46d6c640d9d5fdd5a0c/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/0132733321e61c61de6fa2fd54eccf74/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/0132733321e61c61de6fa2fd54eccf74/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a2f7cfd00a55b4e5d19529717e6876fa/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a2f7cfd00a55b4e5d19529717e6876fa/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3a6825d74634d52b771dbb7588a6e8a2/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/3a6825d74634d52b771dbb7588a6e8a2/transformed/lifecycle-livedata-2.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/f01dc7926de8b47a05dd9ae110def208/transformed/lifecycle-livedata-core-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] /Users/<USER>/.gradle/caches/8.12/transforms/f01dc7926de8b47a05dd9ae110def208/transformed/lifecycle-livedata-core-2.5.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/4dcabb2506fa3abd32fdcdb53bfa9c61/transformed/core-runtime-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/4dcabb2506fa3abd32fdcdb53bfa9c61/transformed/core-runtime-2.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/e81c50d5bf5fddfbbdd6d2c43c6361f0/transformed/annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/e81c50d5bf5fddfbbdd6d2c43c6361f0/transformed/annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Downloads/CCPPDFReader/app/src/main/AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4dbadb3e41cbeee8168608c03081643/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4dbadb3e41cbeee8168608c03081643/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/0132733321e61c61de6fa2fd54eccf74/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/0132733321e61c61de6fa2fd54eccf74/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1091e587cec38684002d949c8908d1cf/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
permission#com.ccppdfreader.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
uses-permission#com.ccppdfreader.app.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.12/transforms/5c902d40ba3bd21d28a00824bcaff24a/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4dbadb3e41cbeee8168608c03081643/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4dbadb3e41cbeee8168608c03081643/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4dbadb3e41cbeee8168608c03081643/transformed/lifecycle-process-2.4.1/AndroidManifest.xml:32:17-78
