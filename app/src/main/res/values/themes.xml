<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    
    <!-- Base Light Theme -->
    <style name="Base.Theme.CCPPDFReader" parent="Theme.Material3.DayNight">
        <!-- Primary colors -->
        <item name="colorPrimary">@color/md_theme_light_primary</item>
        <item name="colorOnPrimary">@color/md_theme_light_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_light_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_light_onPrimaryContainer</item>
        
        <!-- Secondary colors -->
        <item name="colorSecondary">@color/md_theme_light_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_light_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_light_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_light_onSecondaryContainer</item>
        
        <!-- Tertiary colors -->
        <item name="colorTertiary">@color/md_theme_light_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_light_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_light_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_light_onTertiaryContainer</item>
        
        <!-- Error colors -->
        <item name="colorError">@color/md_theme_light_error</item>
        <item name="colorOnError">@color/md_theme_light_onError</item>
        <item name="colorErrorContainer">@color/md_theme_light_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_light_onErrorContainer</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/md_theme_light_background</item>
        <item name="colorOnBackground">@color/md_theme_light_onBackground</item>
        <item name="colorSurface">@color/md_theme_light_surface</item>
        <item name="colorOnSurface">@color/md_theme_light_onSurface</item>
        
        <!-- Surface variants -->
        <item name="colorSurfaceVariant">@color/md_theme_light_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_light_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_light_outline</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@color/status_bar_light</item>
        <item name="android:windowLightStatusBar">true</item>
        
        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/md_theme_light_surface</item>
        <item name="android:windowLightNavigationBar">true</item>
        
        <!-- Typography -->
        <item name="textAppearanceHeadline1">@style/TextAppearance.CCPPDFReader.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.CCPPDFReader.Headline2</item>
        <item name="textAppearanceBody1">@style/TextAppearance.CCPPDFReader.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.CCPPDFReader.Body2</item>
        
        <!-- Shapes -->
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.CCPPDFReader.SmallComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.CCPPDFReader.MediumComponent</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.CCPPDFReader.LargeComponent</item>
    </style>

    <!-- Main App Theme -->
    <style name="Theme.CCPPDFReader" parent="Base.Theme.CCPPDFReader">
        <!-- Window flags -->
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
    </style>

    <!-- Splash Screen Theme -->
    <style name="Theme.CCPPDFReader.Splash" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/splash_background</item>
        <item name="windowSplashScreenAnimatedIcon">@drawable/ic_launcher_foreground</item>
        <item name="windowSplashScreenIconBackgroundColor">@color/splash_background</item>
        <item name="postSplashScreenTheme">@style/Theme.CCPPDFReader</item>
    </style>

    <!-- Full Screen Theme for PDF Reader -->
    <style name="Theme.CCPPDFReader.FullScreen" parent="Theme.CCPPDFReader">
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowLayoutInDisplayCutoutMode" tools:targetApi="p">shortEdges</item>
    </style>

    <!-- Typography Styles -->
    <style name="TextAppearance.CCPPDFReader.Headline1" parent="TextAppearance.Material3.HeadlineLarge">
        <item name="fontFamily">@font/roboto_bold</item>
        <item name="android:textSize">32sp</item>
        <item name="android:letterSpacing">0</item>
    </style>

    <style name="TextAppearance.CCPPDFReader.Headline2" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textSize">24sp</item>
        <item name="android:letterSpacing">0</item>
    </style>

    <style name="TextAppearance.CCPPDFReader.Body1" parent="TextAppearance.Material3.BodyLarge">
        <item name="fontFamily">@font/roboto_regular</item>
        <item name="android:textSize">16sp</item>
        <item name="android:letterSpacing">0.5sp</item>
    </style>

    <style name="TextAppearance.CCPPDFReader.Body2" parent="TextAppearance.Material3.BodyMedium">
        <item name="fontFamily">@font/roboto_regular</item>
        <item name="android:textSize">14sp</item>
        <item name="android:letterSpacing">0.25sp</item>
    </style>

    <!-- Shape Styles -->
    <style name="ShapeAppearance.CCPPDFReader.SmallComponent" parent="ShapeAppearance.Material3.Corner.Small">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>

    <style name="ShapeAppearance.CCPPDFReader.MediumComponent" parent="ShapeAppearance.Material3.Corner.Medium">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12dp</item>
    </style>

    <style name="ShapeAppearance.CCPPDFReader.LargeComponent" parent="ShapeAppearance.Material3.Corner.Large">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style>

    <!-- Button Styles -->
    <style name="Widget.CCPPDFReader.Button" parent="Widget.Material3.Button">
        <item name="shapeAppearance">@style/ShapeAppearance.CCPPDFReader.MediumComponent</item>
        <item name="android:textAppearance">@style/TextAppearance.CCPPDFReader.Body1</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <style name="Widget.CCPPDFReader.Button.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="shapeAppearance">@style/ShapeAppearance.CCPPDFReader.MediumComponent</item>
        <item name="android:textAppearance">@style/TextAppearance.CCPPDFReader.Body1</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <!-- Card Styles -->
    <style name="Widget.CCPPDFReader.CardView" parent="Widget.Material3.CardView.Elevated">
        <item name="shapeAppearance">@style/ShapeAppearance.CCPPDFReader.MediumComponent</item>
        <item name="cardElevation">4dp</item>
        <item name="cardCornerRadius">12dp</item>
    </style>

</resources>
