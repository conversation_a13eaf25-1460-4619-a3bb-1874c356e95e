<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/margin_small"
    android:layout_marginVertical="@dimen/margin_small"
    android:clickable="true"
    android:focusable="true"
    app:cardElevation="2dp"
    app:cardCornerRadius="@dimen/card_corner_radius"
    app:rippleColor="?attr/colorPrimary"
    style="@style/Widget.CCPPDFReader.CardView">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/padding_medium">

        <!-- PDF Icon -->
        <ImageView
            android:id="@+id/iv_pdf_icon"
            android:layout_width="@dimen/icon_size_large"
            android:layout_height="@dimen/icon_size_large"
            android:src="@drawable/ic_pdf_document"
            android:contentDescription="PDF Document"
            app:tint="?attr/colorPrimary"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Favorite Icon -->
        <ImageView
            android:id="@+id/iv_favorite"
            android:layout_width="@dimen/icon_size_medium"
            android:layout_height="@dimen/icon_size_medium"
            android:src="@drawable/ic_favorite"
            android:contentDescription="Favorite"
            android:visibility="gone"
            app:tint="?attr/colorError"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <!-- Document Title -->
        <TextView
            android:id="@+id/tv_document_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginEnd="@dimen/margin_small"
            android:textAppearance="@style/TextAppearance.CCPPDFReader.Body1"
            android:textColor="?attr/colorOnSurface"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/iv_pdf_icon"
            app:layout_constraintEnd_toStartOf="@id/iv_favorite"
            app:layout_constraintTop_toTopOf="@id/iv_pdf_icon"
            tools:text="Sample PDF Document Name.pdf" />

        <!-- Document Info -->
        <TextView
            android:id="@+id/tv_document_info"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginTop="@dimen/margin_small"
            android:textAppearance="@style/TextAppearance.CCPPDFReader.Body2"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintStart_toEndOf="@id/iv_pdf_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_document_title"
            tools:text="2.5 MB • 24 pages • Mar 15, 2024" />

        <!-- Progress Bar -->
        <com.google.android.material.progressindicator.LinearProgressIndicator
            android:id="@+id/progress_reading"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginTop="@dimen/margin_small"
            android:layout_marginEnd="@dimen/margin_medium"
            app:indicatorColor="?attr/colorPrimary"
            app:trackColor="?attr/colorSurfaceVariant"
            app:trackThickness="4dp"
            app:layout_constraintStart_toEndOf="@id/iv_pdf_icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_document_info"
            tools:progress="45" />

        <!-- Progress Text -->
        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_medium"
            android:layout_marginTop="@dimen/margin_xs"
            android:textAppearance="@style/TextAppearance.CCPPDFReader.Body2"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:textSize="10sp"
            app:layout_constraintStart_toEndOf="@id/iv_pdf_icon"
            app:layout_constraintTop_toBottomOf="@id/progress_reading"
            tools:text="Page 11 of 24 (45%)" />

        <!-- More Options Button -->
        <ImageButton
            android:id="@+id/btn_more_options"
            android:layout_width="@dimen/icon_size_medium"
            android:layout_height="@dimen/icon_size_medium"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_more_vert"
            android:contentDescription="More options"
            app:tint="?attr/colorOnSurfaceVariant"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
