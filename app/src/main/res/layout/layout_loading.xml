<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="32dp">

    <!-- Loading Indicator -->
    <ProgressBar
        android:id="@+id/progress_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminate="true" />

    <!-- Loading Text -->
    <TextView
        android:id="@+id/tv_loading_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="Loading..."
        android:textSize="16sp" />

</LinearLayout>
