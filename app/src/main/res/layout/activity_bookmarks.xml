<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".activities.BookmarksActivity">

    <!-- Simple Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:title="@string/bookmarks"
        android:titleTextColor="@color/white" />

    <!-- Main Content -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- RecyclerView for bookmarks -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerViewBookmarks"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="8dp" />

        <!-- Empty State -->
        <LinearLayout
            android:id="@+id/emptyStateLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="32dp"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/no_bookmarks"
                android:textSize="16sp"
                android:gravity="center" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
