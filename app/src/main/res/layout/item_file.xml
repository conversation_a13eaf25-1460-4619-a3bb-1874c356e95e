<?xml version="1.0" encoding="utf-8"?>
<LinearLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="@dimen/padding_medium"
    android:background="?attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:gravity="center_vertical">

    <!-- File/Folder Icon -->
    <ImageView
        android:id="@+id/iv_file_icon"
        android:layout_width="@dimen/icon_size_large"
        android:layout_height="@dimen/icon_size_large"
        android:src="@drawable/ic_pdf_document"
        android:contentDescription="File icon"
        app:tint="?attr/colorPrimary"
        tools:src="@drawable/ic_folder" />

    <!-- File Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginStart="@dimen/margin_medium"
        android:orientation="vertical">

        <!-- File Name -->
        <TextView
            android:id="@+id/tv_file_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="@style/TextAppearance.CCPPDFReader.Body1"
            android:textColor="?attr/colorOnSurface"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="Sample Document.pdf" />

        <!-- File Details -->
        <TextView
            android:id="@+id/tv_file_details"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_xs"
            android:textAppearance="@style/TextAppearance.CCPPDFReader.Body2"
            android:textColor="?attr/colorOnSurfaceVariant"
            android:maxLines="1"
            android:ellipsize="end"
            tools:text="2.5 MB • Mar 15, 2024" />

    </LinearLayout>

    <!-- More Options Button -->
    <ImageButton
        android:id="@+id/btn_more_options"
        android:layout_width="@dimen/icon_size_medium"
        android:layout_height="@dimen/icon_size_medium"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:src="@drawable/ic_more_vert"
        android:contentDescription="More options"
        app:tint="?attr/colorOnSurfaceVariant" />

</LinearLayout>
