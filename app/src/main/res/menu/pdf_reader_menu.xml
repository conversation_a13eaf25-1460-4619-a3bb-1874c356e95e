<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <item
        android:id="@+id/action_previous_page"
        android:title="Previous Page"
        android:icon="@drawable/ic_arrow_back"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_next_page"
        android:title="Next Page"
        android:icon="@drawable/ic_arrow_forward"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_bookmark"
        android:title="Add Bookmark"
        android:icon="@drawable/ic_bookmark_add"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_favorite"
        android:title="Toggle Favorite"
        android:icon="@drawable/ic_favorite"
        app:showAsAction="ifRoom" />

    <item
        android:id="@+id/action_bookmarks_list"
        android:title="View Bookmarks"
        android:icon="@drawable/ic_bookmark"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_night_mode"
        android:title="Night Mode"
        android:icon="@drawable/ic_night_mode"
        app:iconTint="@android:color/white"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_rotate"
        android:title="Rotate"
        android:icon="@drawable/ic_rotate"
        app:iconTint="@android:color/white"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_fit_width"
        android:title="Fit Width"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_fit_height"
        android:title="Fit Height"
        app:showAsAction="never" />

</menu>
