package com.ccppdfreader.app.activities;

import android.os.Bundle;
import android.view.View;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.ccppdfreader.app.R;

public class BookmarksActivity extends AppCompatActivity {

    private RecyclerView recyclerViewBookmarks;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_bookmarks);

        initViews();
        setupRecyclerView();
    }

    private void initViews() {
        recyclerViewBookmarks = findViewById(R.id.recyclerViewBookmarks);
        
        // Show empty state initially
        findViewById(R.id.emptyStateLayout).setVisibility(View.VISIBLE);
    }

    private void setupRecyclerView() {
        recyclerViewBookmarks.setLayoutManager(new LinearLayoutManager(this));
        // No adapter for now - just show empty state
    }
}
