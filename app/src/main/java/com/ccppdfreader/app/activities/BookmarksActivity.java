package com.ccppdfreader.app.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.ccppdfreader.app.R;
import com.ccppdfreader.app.adapters.BookmarkAdapter;
import com.ccppdfreader.app.databinding.ActivityBookmarksBinding;
import com.ccppdfreader.app.managers.BookmarkManager;
import com.ccppdfreader.app.models.Bookmark;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import java.util.List;

/**
 * Activity to display and manage bookmarks
 */
public class BookmarksActivity extends AppCompatActivity {
    
    private ActivityBookmarksBinding binding;
    private BookmarkAdapter adapter;
    private BookmarkManager bookmarkManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityBookmarksBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        
        setupToolbar();
        setupRecyclerView();
        
        bookmarkManager = BookmarkManager.getInstance();
        loadBookmarks();
    }
    
    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Bookmarks");
        }
        
        binding.toolbar.setNavigationOnClickListener(v -> finish());
    }
    
    private void setupRecyclerView() {
        adapter = new BookmarkAdapter(this::onBookmarkClick, this::onBookmarkLongClick);
        binding.recyclerBookmarks.setLayoutManager(new LinearLayoutManager(this));
        binding.recyclerBookmarks.setAdapter(adapter);
    }
    
    private void loadBookmarks() {
        List<Bookmark> bookmarks = bookmarkManager.getAllBookmarks();
        
        if (bookmarks.isEmpty()) {
            binding.recyclerBookmarks.setVisibility(View.GONE);
            binding.layoutEmpty.setVisibility(View.VISIBLE);
        } else {
            binding.recyclerBookmarks.setVisibility(View.VISIBLE);
            binding.layoutEmpty.setVisibility(View.GONE);
            adapter.updateBookmarks(bookmarks);
        }
    }
    
    private void onBookmarkClick(Bookmark bookmark) {
        // Open PDF at bookmarked page
        Intent intent = new Intent(this, PDFReaderActivity.class);
        // TODO: Pass bookmark information to PDF reader
        startActivity(intent);
    }
    
    private void onBookmarkLongClick(Bookmark bookmark) {
        showBookmarkContextMenu(bookmark);
    }
    
    private void showBookmarkContextMenu(Bookmark bookmark) {
        String[] options = {"Edit", "Delete"};
        
        new MaterialAlertDialogBuilder(this)
            .setTitle(bookmark.getDisplayTitle())
            .setItems(options, (dialog, which) -> {
                switch (which) {
                    case 0: // Edit
                        editBookmark(bookmark);
                        break;
                    case 1: // Delete
                        deleteBookmark(bookmark);
                        break;
                }
            })
            .show();
    }
    
    private void editBookmark(Bookmark bookmark) {
        // TODO: Implement bookmark editing
    }
    
    private void deleteBookmark(Bookmark bookmark) {
        new MaterialAlertDialogBuilder(this)
            .setTitle("Delete Bookmark")
            .setMessage("Are you sure you want to delete this bookmark?")
            .setPositiveButton("Delete", (dialog, which) -> {
                bookmarkManager.removeBookmark(bookmark.getFilePath(), bookmark.getPageNumber());
                loadBookmarks(); // Refresh list
            })
            .setNegativeButton("Cancel", null)
            .show();
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.bookmarks_menu, menu);
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();
        if (itemId == R.id.action_clear_all) {
            clearAllBookmarks();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    private void clearAllBookmarks() {
        new MaterialAlertDialogBuilder(this)
            .setTitle("Clear All Bookmarks")
            .setMessage("Are you sure you want to delete all bookmarks?")
            .setPositiveButton("Clear All", (dialog, which) -> {
                bookmarkManager.clearAllBookmarks();
                loadBookmarks(); // Refresh list
            })
            .setNegativeButton("Cancel", null)
            .show();
    }
}
