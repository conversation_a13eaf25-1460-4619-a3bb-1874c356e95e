package com.ccppdfreader.app.activities;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Environment;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.ccppdfreader.app.R;
import com.ccppdfreader.app.adapters.FileExplorerAdapter;
import com.ccppdfreader.app.databinding.ActivityFileExplorerBinding;
import com.ccppdfreader.app.interfaces.FileExplorerContract;
import com.ccppdfreader.app.models.FileItem;
import com.ccppdfreader.app.presenters.FileExplorerPresenter;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.karumi.dexter.Dexter;
import com.karumi.dexter.PermissionToken;
import com.karumi.dexter.listener.PermissionDeniedResponse;
import com.karumi.dexter.listener.PermissionGrantedResponse;
import com.karumi.dexter.listener.PermissionRequest;
import com.karumi.dexter.listener.single.PermissionListener;

import java.io.File;
import java.util.List;

/**
 * File Explorer Activity
 * Browse and select PDF files from device storage
 */
public class FileExplorerActivity extends AppCompatActivity implements FileExplorerContract.View {
    
    private ActivityFileExplorerBinding binding;
    private FileExplorerPresenter presenter;
    private FileExplorerAdapter adapter;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityFileExplorerBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        
        setupToolbar();
        setupRecyclerView();
        
        // Initialize presenter
        presenter = new FileExplorerPresenter();
        presenter.attachView(this);
        
        // Check permissions and load files
        checkStoragePermissionAndLoad();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (presenter != null) {
            presenter.detachView();
        }
    }
    
    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Browse Files");
        }
        
        binding.toolbar.setNavigationOnClickListener(v -> finish());
    }
    
    private void setupRecyclerView() {
        adapter = new FileExplorerAdapter(this::onFileClick, this::onFolderClick);
        binding.recyclerFiles.setLayoutManager(new LinearLayoutManager(this));
        binding.recyclerFiles.setAdapter(adapter);
    }
    
    private void onFileClick(FileItem fileItem) {
        if (fileItem.isPDF()) {
            presenter.openPDFFile(fileItem);
        } else {
            showMessage("Only PDF files can be opened");
        }
    }
    
    private void onFolderClick(FileItem fileItem) {
        presenter.navigateToFolder(fileItem.getPath());
    }
    
    private void checkStoragePermissionAndLoad() {
        String permission = android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU
            ? Manifest.permission.READ_MEDIA_DOCUMENTS
            : Manifest.permission.READ_EXTERNAL_STORAGE;
            
        if (ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED) {
            presenter.loadInitialDirectory();
        } else {
            requestStoragePermission();
        }
    }
    
    private void requestStoragePermission() {
        String permission = android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU
            ? Manifest.permission.READ_MEDIA_DOCUMENTS
            : Manifest.permission.READ_EXTERNAL_STORAGE;
            
        Dexter.withContext(this)
            .withPermission(permission)
            .withListener(new PermissionListener() {
                @Override
                public void onPermissionGranted(PermissionGrantedResponse response) {
                    presenter.loadInitialDirectory();
                }
                
                @Override
                public void onPermissionDenied(PermissionDeniedResponse response) {
                    showPermissionDeniedDialog();
                }
                
                @Override
                public void onPermissionRationaleShouldBeShown(PermissionRequest permission, PermissionToken token) {
                    showPermissionRationaleDialog(token);
                }
            })
            .check();
    }
    
    private void showPermissionRationaleDialog(PermissionToken token) {
        new MaterialAlertDialogBuilder(this)
            .setTitle(R.string.permission_storage_title)
            .setMessage(R.string.permission_storage_message)
            .setPositiveButton(R.string.permission_grant, (dialog, which) -> token.continuePermissionRequest())
            .setNegativeButton(R.string.permission_deny, (dialog, which) -> token.cancelPermissionRequest())
            .show();
    }
    
    private void showPermissionDeniedDialog() {
        new MaterialAlertDialogBuilder(this)
            .setTitle(R.string.permission_storage_title)
            .setMessage("Storage permission is required to browse files. Please grant permission in app settings.")
            .setPositiveButton(R.string.ok, (dialog, which) -> finish())
            .show();
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.file_explorer_menu, menu);
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();
        if (itemId == R.id.action_home) {
            presenter.navigateToHome();
            return true;
        } else if (itemId == R.id.action_refresh) {
            presenter.refreshCurrentDirectory();
            return true;
        } else if (itemId == R.id.action_sort) {
            showSortDialog();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    private void showSortDialog() {
        String[] sortOptions = {"Name", "Date Modified", "Size", "Type"};
        new MaterialAlertDialogBuilder(this)
            .setTitle("Sort by")
            .setItems(sortOptions, (dialog, which) -> {
                switch (which) {
                    case 0: presenter.sortByName(); break;
                    case 1: presenter.sortByDate(); break;
                    case 2: presenter.sortBySize(); break;
                    case 3: presenter.sortByType(); break;
                }
            })
            .show();
    }
    
    @Override
    public void onBackPressed() {
        if (!presenter.navigateBack()) {
            super.onBackPressed();
        }
    }
    
    // FileExplorerContract.View implementation
    @Override
    public void showLoading() {
        binding.progressLoading.setVisibility(View.VISIBLE);
        binding.recyclerFiles.setVisibility(View.GONE);
        binding.layoutEmpty.setVisibility(View.GONE);
    }
    
    @Override
    public void hideLoading() {
        binding.progressLoading.setVisibility(View.GONE);
    }
    
    @Override
    public void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }
    
    @Override
    public void showSuccess(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public void showMessage(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public boolean isActive() {
        return !isFinishing() && !isDestroyed();
    }
    
    @Override
    public void showFiles(List<FileItem> files) {
        hideLoading();
        if (files.isEmpty()) {
            binding.recyclerFiles.setVisibility(View.GONE);
            binding.layoutEmpty.setVisibility(View.VISIBLE);
        } else {
            binding.recyclerFiles.setVisibility(View.VISIBLE);
            binding.layoutEmpty.setVisibility(View.GONE);
            adapter.updateFiles(files);
        }
    }
    
    @Override
    public void updateCurrentPath(String path) {
        if (getSupportActionBar() != null) {
            File file = new File(path);
            getSupportActionBar().setSubtitle(file.getName());
        }
    }
    
    @Override
    public void openPDFReader(FileItem fileItem) {
        Intent intent = new Intent(this, PDFReaderActivity.class);
        intent.setData(android.net.Uri.fromFile(new File(fileItem.getPath())));
        startActivity(intent);
    }
}
