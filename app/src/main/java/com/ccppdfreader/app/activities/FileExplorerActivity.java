package com.ccppdfreader.app.activities;

import android.content.Intent;
import android.os.Bundle;
import android.os.Environment;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.ccppdfreader.app.R;
import com.ccppdfreader.app.adapters.FileAdapter;
import com.ccppdfreader.app.managers.PDFManager;
import com.ccppdfreader.app.models.FileItem;
import com.ccppdfreader.app.models.PDFDocument;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class FileExplorerActivity extends AppCompatActivity implements FileAdapter.OnFileClickListener {

    private RecyclerView recyclerViewFiles;
    private TextView textViewCurrentPath;
    private FileAdapter adapter;
    private PDFManager pdfManager;
    private View emptyStateLayout;
    private File currentDirectory;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_file_explorer);

        initViews();
        setupToolbar();
        setupRecyclerView();
        navigateToDirectory(getDefaultDirectory());
    }

    private void initViews() {
        recyclerViewFiles = findViewById(R.id.recyclerViewFiles);
        textViewCurrentPath = findViewById(R.id.textViewCurrentPath);
        emptyStateLayout = findViewById(R.id.emptyStateLayout);
        pdfManager = new PDFManager(this);
    }

    private void setupToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("File Explorer");
        }
    }

    private void setupRecyclerView() {
        adapter = new FileAdapter(this);
        adapter.setOnFileClickListener(this);
        recyclerViewFiles.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewFiles.setAdapter(adapter);
    }

    private File getDefaultDirectory() {
        // Try to get the Downloads directory first
        File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
        if (downloadsDir.exists() && downloadsDir.canRead()) {
            return downloadsDir;
        }

        // Fallback to external storage root
        File externalStorage = Environment.getExternalStorageDirectory();
        if (externalStorage.exists() && externalStorage.canRead()) {
            return externalStorage;
        }

        // Last resort - app's external files directory
        return getExternalFilesDir(null);
    }

    private void navigateToDirectory(File directory) {
        if (directory == null || !directory.exists() || !directory.canRead()) {
            Toast.makeText(this, "Cannot access directory", Toast.LENGTH_SHORT).show();
            return;
        }

        currentDirectory = directory;
        textViewCurrentPath.setText(directory.getAbsolutePath());
        loadDirectoryContents();
    }

    private void loadDirectoryContents() {
        try {
            File[] files = currentDirectory.listFiles();
            List<FileItem> fileItems = new ArrayList<>();

            if (files != null) {
                for (File file : files) {
                    // Skip hidden files unless specifically showing them
                    if (file.isHidden()) continue;

                    FileItem item = new FileItem(file);

                    // Only show directories and PDF files
                    if (item.isDirectory() || item.isPDF()) {
                        fileItems.add(item);
                    }
                }
            }

            // Sort: directories first, then files, both alphabetically
            Collections.sort(fileItems, new Comparator<FileItem>() {
                @Override
                public int compare(FileItem o1, FileItem o2) {
                    if (o1.isDirectory() && !o2.isDirectory()) return -1;
                    if (!o1.isDirectory() && o2.isDirectory()) return 1;
                    return o1.getName().compareToIgnoreCase(o2.getName());
                }
            });

            adapter.setFiles(fileItems);
            updateEmptyState();

        } catch (SecurityException e) {
            Toast.makeText(this, "Permission denied to access directory", Toast.LENGTH_SHORT).show();
            updateEmptyState();
        }
    }

    private void updateEmptyState() {
        if (adapter.isEmpty()) {
            emptyStateLayout.setVisibility(View.VISIBLE);
            recyclerViewFiles.setVisibility(View.GONE);
        } else {
            emptyStateLayout.setVisibility(View.GONE);
            recyclerViewFiles.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onFileClick(FileItem fileItem) {
        if (fileItem.isDirectory()) {
            navigateToDirectory(fileItem.getFile());
        } else if (fileItem.isPDF()) {
            openPDFFile(fileItem);
        }
    }

    @Override
    public void onFileLongClick(FileItem fileItem) {
        // Show file options or details
        Toast.makeText(this, "File: " + fileItem.getName() + "\nSize: " + fileItem.getFormattedSize(),
                Toast.LENGTH_LONG).show();
    }

    private void openPDFFile(FileItem fileItem) {
        // Create PDF document and add to recent files
        PDFDocument document = new PDFDocument(fileItem.getPath(), fileItem.getName());
        document.setFileSize(fileItem.getSize());

        pdfManager.addRecentDocument(document);

        // Open PDF reader
        Intent intent = new Intent(this, PDFReaderActivity.class);
        intent.putExtra("pdf_document", document);
        startActivity(intent);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        if (currentDirectory != null && currentDirectory.getParent() != null) {
            File parentDir = currentDirectory.getParentFile();
            if (parentDir != null && parentDir.canRead()) {
                navigateToDirectory(parentDir);
                return;
            }
        }
        super.onBackPressed();
    }
}
