package com.ccppdfreader.app.activities;

import android.os.Bundle;
import android.view.View;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.ccppdfreader.app.R;

public class FileExplorerActivity extends AppCompatActivity {

    private RecyclerView recyclerViewFiles;
    private TextView textViewCurrentPath;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_file_explorer);

        initViews();
        setupRecyclerView();
    }

    private void initViews() {
        recyclerViewFiles = findViewById(R.id.recyclerViewFiles);
        textViewCurrentPath = findViewById(R.id.textViewCurrentPath);
        
        textViewCurrentPath.setText("/storage/emulated/0/");
        
        // Show empty state initially
        findViewById(R.id.emptyStateLayout).setVisibility(View.VISIBLE);
    }

    private void setupRecyclerView() {
        recyclerViewFiles.setLayoutManager(new LinearLayoutManager(this));
        // No adapter for now - just show empty state
    }
}
