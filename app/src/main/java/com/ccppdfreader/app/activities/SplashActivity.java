package com.ccppdfreader.app.activities;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.splashscreen.SplashScreen;
import com.ccppdfreader.app.CCPPDFReaderApplication;
import com.ccppdfreader.app.R;

/**
 * Splash screen activity with modern Android 12+ splash screen API
 */
public class SplashActivity extends AppCompatActivity {
    
    private static final int SPLASH_DELAY = 2000; // 2 seconds
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // Handle the splash screen transition
        SplashScreen splashScreen = SplashScreen.installSplashScreen(this);
        
        super.onCreate(savedInstanceState);
        
        // Keep the splash screen on-screen for longer period
        splashScreen.setKeepOnScreenCondition(() -> true);
        
        // Initialize app and navigate to main activity
        initializeApp();
    }
    
    private void initializeApp() {
        new Handler(Looper.getMainLooper()).postDelayed(() -> {
            // Navigate to main activity
            Intent intent = new Intent(SplashActivity.this, MainActivity.class);
            startActivity(intent);
            finish();
            
            // Add smooth transition
            overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);
        }, SPLASH_DELAY);
    }
}
