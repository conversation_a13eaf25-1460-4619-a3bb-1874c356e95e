package com.ccppdfreader.app.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.ccppdfreader.app.R;

public class MainActivity extends AppCompatActivity {

    private RecyclerView recyclerViewRecentFiles;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initViews();
        setupRecyclerView();
    }

    private void initViews() {
        recyclerViewRecentFiles = findViewById(R.id.recyclerViewRecentFiles);
        
        // Show empty state initially
        findViewById(R.id.emptyStateLayout).setVisibility(View.VISIBLE);
    }

    private void setupRecyclerView() {
        recyclerViewRecentFiles.setLayoutManager(new LinearLayoutManager(this));
        // No adapter for now - just show empty state
    }

    public void openFileExplorer(View view) {
        Intent intent = new Intent(this, FileExplorerActivity.class);
        startActivity(intent);
    }

    public void openBookmarks(View view) {
        Intent intent = new Intent(this, BookmarksActivity.class);
        startActivity(intent);
    }

    public void openSettings(View view) {
        Intent intent = new Intent(this, SettingsActivity.class);
        startActivity(intent);
    }
}
