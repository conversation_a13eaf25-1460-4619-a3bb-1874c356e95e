package com.ccppdfreader.app.activities;

import android.Manifest;
import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.ccppdfreader.app.R;
import com.ccppdfreader.app.adapters.PDFDocumentAdapter;
import com.ccppdfreader.app.managers.PDFManager;
import com.ccppdfreader.app.models.PDFDocument;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.content.pm.PackageManager;
import java.util.List;

public class MainActivity extends AppCompatActivity implements PDFDocumentAdapter.OnDocumentClickListener {

    private RecyclerView recyclerViewRecentFiles;
    private PDFDocumentAdapter adapter;
    private PDFManager pdfManager;
    private View emptyStateLayout;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initViews();
        setupToolbar();
        setupRecyclerView();
        requestPermissions();
        loadRecentDocuments();
    }

    private void initViews() {
        recyclerViewRecentFiles = findViewById(R.id.recyclerViewRecentFiles);
        emptyStateLayout = findViewById(R.id.emptyStateLayout);
        pdfManager = new PDFManager(this);
    }

    private void setupToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle(getString(R.string.app_name));
        }
    }

    private void setupRecyclerView() {
        adapter = new PDFDocumentAdapter(this);
        adapter.setOnDocumentClickListener(this);
        recyclerViewRecentFiles.setLayoutManager(new LinearLayoutManager(this));
        recyclerViewRecentFiles.setAdapter(adapter);
    }

    private void requestPermissions() {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE)
                != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.READ_EXTERNAL_STORAGE},
                100);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == 100) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // Permission granted
                loadRecentDocuments();
            } else {
                Toast.makeText(this, "Storage permission is required to access PDF files",
                    Toast.LENGTH_LONG).show();
            }
        }
    }

    private void loadRecentDocuments() {
        List<PDFDocument> recentDocs = pdfManager.getRecentDocuments(10);
        adapter.setDocuments(recentDocs);
        updateEmptyState();
    }

    private void updateEmptyState() {
        if (adapter.isEmpty()) {
            emptyStateLayout.setVisibility(View.VISIBLE);
            recyclerViewRecentFiles.setVisibility(View.GONE);
        } else {
            emptyStateLayout.setVisibility(View.GONE);
            recyclerViewRecentFiles.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();

        if (id == R.id.action_file_explorer) {
            openFileExplorer();
            return true;
        } else if (id == R.id.action_bookmarks) {
            openBookmarks();
            return true;
        } else if (id == R.id.action_settings) {
            openSettings();
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    public void openFileExplorer() {
        Intent intent = new Intent(this, FileExplorerActivity.class);
        startActivity(intent);
    }

    public void openBookmarks() {
        Intent intent = new Intent(this, BookmarksActivity.class);
        startActivity(intent);
    }

    public void openSettings() {
        Intent intent = new Intent(this, SettingsActivity.class);
        startActivity(intent);
    }

    @Override
    public void onDocumentClick(PDFDocument document) {
        Intent intent = new Intent(this, PDFReaderActivity.class);
        intent.putExtra("pdf_document", document);
        startActivity(intent);
    }

    @Override
    public void onDocumentLongClick(PDFDocument document) {
        // Show context menu or options
        Toast.makeText(this, "Long clicked: " + document.getTitle(), Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onFavoriteClick(PDFDocument document) {
        if (document.isFavorite()) {
            pdfManager.removeFavoriteDocument(document.getId());
            document.setFavorite(false);
        } else {
            pdfManager.addFavoriteDocument(document);
            document.setFavorite(true);
        }
        adapter.updateDocument(document);
        Toast.makeText(this, document.isFavorite() ? "Added to favorites" : "Removed from favorites",
                Toast.LENGTH_SHORT).show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadRecentDocuments();
    }
}
