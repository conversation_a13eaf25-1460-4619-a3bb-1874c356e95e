package com.ccppdfreader.app.activities;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.ccppdfreader.app.R;
import com.ccppdfreader.app.adapters.PDFDocumentAdapter;
import com.ccppdfreader.app.databinding.ActivityMainBinding;
import com.ccppdfreader.app.interfaces.MainContract;
import com.ccppdfreader.app.models.PDFDocument;
import com.ccppdfreader.app.presenters.MainPresenter;
import com.ccppdfreader.app.utils.FileUtils;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.karumi.dexter.Dexter;
import com.karumi.dexter.PermissionToken;
import com.karumi.dexter.listener.PermissionDeniedResponse;
import com.karumi.dexter.listener.PermissionGrantedResponse;
import com.karumi.dexter.listener.PermissionRequest;
import com.karumi.dexter.listener.single.PermissionListener;

import java.util.List;

/**
 * Main activity of the application
 * Displays recent files and provides navigation to other features
 */
public class MainActivity extends AppCompatActivity implements MainContract.View {
    
    private ActivityMainBinding binding;
    private MainPresenter presenter;
    private PDFDocumentAdapter adapter;
    
    // Activity result launchers
    private final ActivityResultLauncher<String> filePickerLauncher = 
        registerForActivityResult(new ActivityResultContracts.GetContent(), this::handleSelectedFile);
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        
        setupToolbar();
        setupRecyclerView();
        setupClickListeners();
        setupBottomNavigation();
        
        // Initialize presenter
        presenter = new MainPresenter();
        presenter.attachView(this);
        presenter.start();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        if (presenter != null) {
            presenter.loadRecentDocuments();
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (presenter != null) {
            presenter.detachView();
        }
    }
    
    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayShowTitleEnabled(true);
        }
    }
    
    private void setupRecyclerView() {
        adapter = new PDFDocumentAdapter(this::onDocumentClick, this::onDocumentLongClick);
        binding.recyclerRecentFiles.setLayoutManager(new LinearLayoutManager(this));
        binding.recyclerRecentFiles.setAdapter(adapter);
    }
    
    private void setupClickListeners() {
        binding.btnOpenPdf.setOnClickListener(v -> openFilePicker());
        binding.btnBrowseFiles.setOnClickListener(v -> openFileBrowser());
        binding.fabOpenPdf.setOnClickListener(v -> openFilePicker());
    }
    
    private void setupBottomNavigation() {
        binding.bottomNavigation.setOnItemSelectedListener(item -> {
            int itemId = item.getItemId();
            if (itemId == R.id.nav_home) {
                // Already on home
                return true;
            } else if (itemId == R.id.nav_recent) {
                // Show recent files (already showing)
                return true;
            } else if (itemId == R.id.nav_favorites) {
                presenter.loadFavoriteDocuments();
                return true;
            } else if (itemId == R.id.nav_settings) {
                openSettings();
                return true;
            }
            return false;
        });
        
        // Set home as selected
        binding.bottomNavigation.setSelectedItemId(R.id.nav_home);
    }
    
    private void openFilePicker() {
        checkStoragePermissionAndExecute(() -> filePickerLauncher.launch("application/pdf"));
    }
    
    private void openFileBrowser() {
        // TODO: Implement file browser activity
        Toast.makeText(this, "File browser coming soon!", Toast.LENGTH_SHORT).show();
    }
    
    private void openSettings() {
        Intent intent = new Intent(this, SettingsActivity.class);
        startActivity(intent);
    }
    
    private void handleSelectedFile(Uri uri) {
        if (uri != null) {
            presenter.openDocument(uri);
        }
    }
    
    private void onDocumentClick(PDFDocument document) {
        presenter.openDocument(document);
    }
    
    private void onDocumentLongClick(PDFDocument document) {
        // Show context menu
        showDocumentContextMenu(document);
    }
    
    private void showDocumentContextMenu(PDFDocument document) {
        String[] options = {
            document.isFavorite() ? "Remove from Favorites" : "Add to Favorites",
            "Share",
            "Delete from Recent"
        };
        
        new MaterialAlertDialogBuilder(this)
            .setTitle(document.getDisplayName())
            .setItems(options, (dialog, which) -> {
                switch (which) {
                    case 0: // Toggle favorite
                        presenter.toggleFavorite(document);
                        break;
                    case 1: // Share
                        presenter.shareDocument(document);
                        break;
                    case 2: // Remove from recent
                        presenter.removeFromRecent(document);
                        break;
                }
            })
            .show();
    }
    
    private void checkStoragePermissionAndExecute(Runnable action) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ uses different permissions
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_DOCUMENTS) 
                == PackageManager.PERMISSION_GRANTED) {
                action.run();
            } else {
                requestStoragePermission(action);
            }
        } else {
            // Android 12 and below
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) 
                == PackageManager.PERMISSION_GRANTED) {
                action.run();
            } else {
                requestStoragePermission(action);
            }
        }
    }
    
    private void requestStoragePermission(Runnable onGranted) {
        String permission = android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU
            ? Manifest.permission.READ_MEDIA_DOCUMENTS
            : Manifest.permission.READ_EXTERNAL_STORAGE;
            
        Dexter.withContext(this)
            .withPermission(permission)
            .withListener(new PermissionListener() {
                @Override
                public void onPermissionGranted(PermissionGrantedResponse response) {
                    onGranted.run();
                }
                
                @Override
                public void onPermissionDenied(PermissionDeniedResponse response) {
                    showPermissionDeniedDialog();
                }
                
                @Override
                public void onPermissionRationaleShouldBeShown(PermissionRequest permission, PermissionToken token) {
                    showPermissionRationaleDialog(token);
                }
            })
            .check();
    }
    
    private void showPermissionRationaleDialog(PermissionToken token) {
        new MaterialAlertDialogBuilder(this)
            .setTitle(R.string.permission_storage_title)
            .setMessage(R.string.permission_storage_message)
            .setPositiveButton(R.string.permission_grant, (dialog, which) -> token.continuePermissionRequest())
            .setNegativeButton(R.string.permission_deny, (dialog, which) -> token.cancelPermissionRequest())
            .show();
    }
    
    private void showPermissionDeniedDialog() {
        new MaterialAlertDialogBuilder(this)
            .setTitle(R.string.permission_storage_title)
            .setMessage(R.string.permission_storage_message)
            .setPositiveButton(R.string.ok, null)
            .show();
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main_menu, menu);
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();
        if (itemId == R.id.action_settings) {
            openSettings();
            return true;
        } else if (itemId == R.id.action_about) {
            // TODO: Show about dialog
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    // MainContract.View implementation
    @Override
    public void showLoading() {
        // TODO: Show loading indicator
    }
    
    @Override
    public void hideLoading() {
        // TODO: Hide loading indicator
    }
    
    @Override
    public void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }
    
    @Override
    public void showSuccess(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public void showMessage(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public boolean isActive() {
        return !isFinishing() && !isDestroyed();
    }
    
    @Override
    public void showRecentDocuments(List<PDFDocument> documents) {
        if (documents.isEmpty()) {
            binding.recyclerRecentFiles.setVisibility(View.GONE);
            binding.layoutEmptyRecent.setVisibility(View.VISIBLE);
        } else {
            binding.recyclerRecentFiles.setVisibility(View.VISIBLE);
            binding.layoutEmptyRecent.setVisibility(View.GONE);
            adapter.updateDocuments(documents);
        }
    }
    
    @Override
    public void openPDFReader(PDFDocument document) {
        Intent intent = new Intent(this, PDFReaderActivity.class);
        intent.putExtra(PDFReaderActivity.EXTRA_PDF_DOCUMENT, document);
        startActivity(intent);
    }
    
    @Override
    public void openPDFReader(Uri uri) {
        Intent intent = new Intent(this, PDFReaderActivity.class);
        intent.setData(uri);
        startActivity(intent);
    }
    
    @Override
    public void refreshDocumentList() {
        adapter.notifyDataSetChanged();
    }
}
