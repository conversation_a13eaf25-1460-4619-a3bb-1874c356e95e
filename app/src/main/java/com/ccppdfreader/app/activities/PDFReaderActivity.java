package com.ccppdfreader.app.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import com.ccppdfreader.app.R;
import com.ccppdfreader.app.managers.BookmarkManager;
import com.ccppdfreader.app.managers.PDFManager;
import com.ccppdfreader.app.models.Bookmark;
import com.ccppdfreader.app.models.PDFDocument;
import java.io.File;

public class PDFReaderActivity extends AppCompatActivity {

    private PDFDocument currentDocument;
    private PDFManager pdfManager;
    private BookmarkManager bookmarkManager;
    private TextView textViewPdfPlaceholder;
    private int currentPage = 1;
    private int totalPages = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_pdf_reader);

        initViews();
        setupToolbar();
        loadPDFDocument();
    }

    private void initViews() {
        textViewPdfPlaceholder = findViewById(R.id.textViewPdfPlaceholder);
        pdfManager = new PDFManager(this);
        bookmarkManager = new BookmarkManager(this);
    }

    private void setupToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
    }

    private void loadPDFDocument() {
        Intent intent = getIntent();
        if (intent != null && intent.hasExtra("pdf_document")) {
            currentDocument = (PDFDocument) intent.getSerializableExtra("pdf_document");

            if (currentDocument != null) {
                setupDocument();
                displayPDFInfo();
            } else {
                showError("No PDF document provided");
            }
        } else {
            showError("No PDF document provided");
        }
    }

    private void setupDocument() {
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle(currentDocument.getTitle());
        }

        // Simulate PDF loading and get page count
        File pdfFile = new File(currentDocument.getFilePath());
        if (pdfFile.exists()) {
            // In a real implementation, you would use a PDF library to get actual page count
            totalPages = 10; // Simulated page count
            currentDocument.setTotalPages(totalPages);
            currentPage = Math.max(1, currentDocument.getLastPageRead());

            // Update document in manager
            pdfManager.updateDocument(currentDocument);
        } else {
            showError("PDF file not found: " + currentDocument.getFilePath());
        }
    }

    private void displayPDFInfo() {
        StringBuilder info = new StringBuilder();
        info.append("📄 PDF Reader\n\n");
        info.append("Document: ").append(currentDocument.getTitle()).append("\n");
        info.append("File: ").append(currentDocument.getFileName()).append("\n");
        info.append("Size: ").append(currentDocument.getFormattedFileSize()).append("\n");
        info.append("Pages: ").append(totalPages).append("\n");
        info.append("Current Page: ").append(currentPage).append("\n\n");

        if (currentDocument.getReadingProgress() > 0) {
            info.append("Progress: ").append(String.format("%.1f%%", currentDocument.getReadingProgress() * 100)).append("\n\n");
        }

        info.append("📖 PDF content would be displayed here\n");
        info.append("Use the menu to add bookmarks, navigate pages, etc.\n\n");

        int bookmarkCount = bookmarkManager.getBookmarkCountForDocument(currentDocument.getId());
        if (bookmarkCount > 0) {
            info.append("📑 Bookmarks: ").append(bookmarkCount).append("\n");
        }

        textViewPdfPlaceholder.setText(info.toString());
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.pdf_reader_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();

        if (id == android.R.id.home) {
            onBackPressed();
            return true;
        } else if (id == R.id.action_bookmark) {
            addBookmark();
            return true;
        } else if (id == R.id.action_bookmarks_list) {
            showBookmarks();
            return true;
        } else if (id == R.id.action_previous_page) {
            previousPage();
            return true;
        } else if (id == R.id.action_next_page) {
            nextPage();
            return true;
        } else if (id == R.id.action_favorite) {
            toggleFavorite();
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    private void addBookmark() {
        if (currentDocument != null) {
            String title = "Page " + currentPage;
            Bookmark bookmark = new Bookmark(currentDocument.getId(), currentDocument.getTitle(), currentPage, title);
            bookmarkManager.addBookmark(bookmark);
            Toast.makeText(this, "Bookmark added for page " + currentPage, Toast.LENGTH_SHORT).show();
            displayPDFInfo(); // Refresh display
        }
    }

    private void showBookmarks() {
        Intent intent = new Intent(this, BookmarksActivity.class);
        intent.putExtra("document_id", currentDocument.getId());
        startActivity(intent);
    }

    private void previousPage() {
        if (currentPage > 1) {
            currentPage--;
            updateCurrentPage();
        } else {
            Toast.makeText(this, "Already at first page", Toast.LENGTH_SHORT).show();
        }
    }

    private void nextPage() {
        if (currentPage < totalPages) {
            currentPage++;
            updateCurrentPage();
        } else {
            Toast.makeText(this, "Already at last page", Toast.LENGTH_SHORT).show();
        }
    }

    private void updateCurrentPage() {
        currentDocument.setLastPageRead(currentPage);
        currentDocument.updateReadingProgress();
        pdfManager.updateDocument(currentDocument);
        displayPDFInfo();
    }

    private void toggleFavorite() {
        if (currentDocument.isFavorite()) {
            pdfManager.removeFavoriteDocument(currentDocument.getId());
            currentDocument.setFavorite(false);
            Toast.makeText(this, "Removed from favorites", Toast.LENGTH_SHORT).show();
        } else {
            pdfManager.addFavoriteDocument(currentDocument);
            currentDocument.setFavorite(true);
            Toast.makeText(this, "Added to favorites", Toast.LENGTH_SHORT).show();
        }
    }

    private void showError(String message) {
        textViewPdfPlaceholder.setText("❌ Error: " + message);
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (currentDocument != null) {
            // Save current reading position
            pdfManager.updateDocument(currentDocument);
        }
    }
}
