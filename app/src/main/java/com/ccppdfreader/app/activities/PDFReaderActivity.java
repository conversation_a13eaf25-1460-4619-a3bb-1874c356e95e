package com.ccppdfreader.app.activities;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;

import com.ccppdfreader.app.R;
import com.ccppdfreader.app.databinding.ActivityPdfReaderBinding;
import com.ccppdfreader.app.interfaces.PDFReaderContract;
import com.ccppdfreader.app.models.PDFDocument;
import com.ccppdfreader.app.presenters.PDFReaderPresenter;
import com.github.barteksc.pdfviewer.listener.OnLoadCompleteListener;
import com.github.barteksc.pdfviewer.listener.OnPageChangeListener;
import com.github.barteksc.pdfviewer.listener.OnPageErrorListener;
import com.github.barteksc.pdfviewer.listener.OnTapListener;
import com.github.barteksc.pdfviewer.scroll.DefaultScrollHandle;
import com.google.android.material.slider.Slider;

/**
 * PDF Reader Activity
 * Displays PDF documents with full-screen reading experience
 */
public class PDFReaderActivity extends AppCompatActivity implements PDFReaderContract.View {
    
    public static final String EXTRA_PDF_DOCUMENT = "extra_pdf_document";
    
    private ActivityPdfReaderBinding binding;
    private PDFReaderPresenter presenter;
    
    private boolean isControlsVisible = false;
    private Handler hideControlsHandler = new Handler(Looper.getMainLooper());
    private Runnable hideControlsRunnable = this::hideControls;
    
    private int currentPage = 0;
    private int totalPages = 0;
    private float currentZoom = 1.0f;
    private boolean isNightMode = false;
    private boolean isSearchVisible = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityPdfReaderBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        
        setupFullScreen();
        setupToolbar();
        setupPDFView();
        setupControls();
        
        // Initialize presenter
        presenter = new PDFReaderPresenter();
        presenter.attachView(this);
        
        // Load PDF from intent
        loadPDFFromIntent();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (presenter != null) {
            presenter.detachView();
        }
        hideControlsHandler.removeCallbacks(hideControlsRunnable);
    }
    
    private void setupFullScreen() {
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);
        WindowInsetsControllerCompat controller = WindowCompat.getInsetsController(getWindow(), getWindow().getDecorView());
        if (controller != null) {
            controller.setSystemBarsBehavior(WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE);
        }
    }
    
    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setDisplayShowHomeEnabled(true);
        }
        
        binding.toolbar.setNavigationOnClickListener(v -> finish());
    }
    
    private void setupPDFView() {
        binding.pdfView.setOnTapListener(new OnTapListener() {
            @Override
            public boolean onTap(float x, float y) {
                toggleControls();
                return true;
            }
        });
        
        binding.pdfView.setOnPageChangeListener(new OnPageChangeListener() {
            @Override
            public void onPageChanged(int page, int pageCount) {
                currentPage = page;
                totalPages = pageCount;
                updatePageIndicator();
                updatePageSlider();
                presenter.updateReadingProgress(page + 1, pageCount);
            }
        });
        
        binding.pdfView.setOnLoadCompleteListener(new OnLoadCompleteListener() {
            @Override
            public void loadComplete(int nbPages) {
                totalPages = nbPages;
                hideLoading();
                updatePageIndicator();
                setupPageSlider();
            }
        });
        
        binding.pdfView.setOnPageErrorListener(new OnPageErrorListener() {
            @Override
            public void onPageError(int page, Throwable t) {
                showError("Error loading page " + (page + 1));
            }
        });
    }
    
    private void setupControls() {
        // Page navigation
        binding.btnPreviousPage.setOnClickListener(v -> {
            if (currentPage > 0) {
                binding.pdfView.jumpTo(currentPage - 1);
            }
        });
        
        binding.btnNextPage.setOnClickListener(v -> {
            if (currentPage < totalPages - 1) {
                binding.pdfView.jumpTo(currentPage + 1);
            }
        });
        
        // Zoom controls
        binding.btnZoomIn.setOnClickListener(v -> {
            currentZoom = Math.min(currentZoom * 1.2f, 5.0f);
            binding.pdfView.zoomTo(currentZoom);
            updateZoomLevel();
        });
        
        binding.btnZoomOut.setOnClickListener(v -> {
            currentZoom = Math.max(currentZoom / 1.2f, 0.5f);
            binding.pdfView.zoomTo(currentZoom);
            updateZoomLevel();
        });
        
        // Search controls
        binding.btnCloseSearch.setOnClickListener(v -> hideSearch());
        binding.btnSearchNext.setOnClickListener(v -> searchNext());
        binding.btnSearchPrevious.setOnClickListener(v -> searchPrevious());

        // Search text listener
        binding.etSearch.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == android.view.inputmethod.EditorInfo.IME_ACTION_SEARCH) {
                performSearch(binding.etSearch.getText().toString());
                return true;
            }
            return false;
        });
        
        // Page slider
        binding.sliderPage.addOnSliderTouchListener(new Slider.OnSliderTouchListener() {
            @Override
            public void onStartTrackingTouch(@NonNull Slider slider) {
                // Stop auto-hide while user is interacting
                hideControlsHandler.removeCallbacks(hideControlsRunnable);
            }
            
            @Override
            public void onStopTrackingTouch(@NonNull Slider slider) {
                int page = (int) slider.getValue() - 1;
                binding.pdfView.jumpTo(page);
                scheduleHideControls();
            }
        });
    }
    
    private void loadPDFFromIntent() {
        Intent intent = getIntent();
        
        if (intent.hasExtra(EXTRA_PDF_DOCUMENT)) {
            PDFDocument document = intent.getParcelableExtra(EXTRA_PDF_DOCUMENT);
            if (document != null) {
                presenter.loadDocument(document);
            }
        } else if (intent.getData() != null) {
            Uri uri = intent.getData();
            presenter.loadDocument(uri);
        } else {
            showError("No PDF file specified");
            finish();
        }
    }
    
    private void toggleControls() {
        if (isControlsVisible) {
            hideControls();
        } else {
            showControls();
        }
    }
    
    private void showControls() {
        isControlsVisible = true;
        binding.appBarLayout.setVisibility(View.VISIBLE);
        binding.bottomControls.setVisibility(View.VISIBLE);
        
        // Show system bars
        WindowInsetsControllerCompat controller = WindowCompat.getInsetsController(getWindow(), getWindow().getDecorView());
        if (controller != null) {
            controller.show(WindowInsetsCompat.Type.systemBars());
        }
        
        scheduleHideControls();
    }
    
    private void hideControls() {
        isControlsVisible = false;
        binding.appBarLayout.setVisibility(View.GONE);
        binding.bottomControls.setVisibility(View.GONE);
        
        // Hide system bars
        WindowInsetsControllerCompat controller = WindowCompat.getInsetsController(getWindow(), getWindow().getDecorView());
        if (controller != null) {
            controller.hide(WindowInsetsCompat.Type.systemBars());
        }
        
        hideControlsHandler.removeCallbacks(hideControlsRunnable);
    }
    
    private void scheduleHideControls() {
        hideControlsHandler.removeCallbacks(hideControlsRunnable);
        hideControlsHandler.postDelayed(hideControlsRunnable, 3000); // Hide after 3 seconds
    }
    
    private void showSearch() {
        binding.searchContainer.setVisibility(View.VISIBLE);
        binding.etSearch.requestFocus();
    }
    
    private void hideSearch() {
        binding.searchContainer.setVisibility(View.GONE);
        isSearchVisible = false;
    }

    private void performSearch(String query) {
        if (query == null || query.trim().isEmpty()) {
            showMessage("Please enter search text");
            return;
        }

        presenter.searchText(query.trim());
    }

    private void searchNext() {
        // TODO: Implement search next functionality
        showMessage("Search next");
    }

    private void searchPrevious() {
        // TODO: Implement search previous functionality
        showMessage("Search previous");
    }

    private void toggleNightMode() {
        isNightMode = !isNightMode;

        if (isNightMode) {
            // Apply night mode styling
            binding.pdfView.setBackgroundColor(getColor(R.color.pdf_background_dark));
            showMessage("Night mode enabled");
        } else {
            // Apply day mode styling
            binding.pdfView.setBackgroundColor(getColor(R.color.pdf_background));
            showMessage("Night mode disabled");
        }

        // Update menu icon
        invalidateOptionsMenu();
    }

    private void rotatePage() {
        // TODO: Implement page rotation
        showMessage("Page rotation coming soon");
    }

    private void fitToWidth() {
        binding.pdfView.fitToWidth();
        showMessage("Fit to width");
    }

    private void fitToHeight() {
        // TODO: Implement fit to height
        showMessage("Fit to height");
    }
    
    private void updatePageIndicator() {
        String pageText = getString(R.string.page_indicator, currentPage + 1, totalPages);
        binding.tvPageIndicator.setText(pageText);
    }
    
    private void updatePageSlider() {
        if (totalPages > 0) {
            binding.sliderPage.setValueFrom(1);
            binding.sliderPage.setValueTo(totalPages);
            binding.sliderPage.setValue(currentPage + 1);
        }
    }
    
    private void setupPageSlider() {
        if (totalPages > 0) {
            binding.sliderPage.setValueFrom(1);
            binding.sliderPage.setValueTo(totalPages);
            binding.sliderPage.setValue(1);
        }
    }
    
    private void updateZoomLevel() {
        String zoomText = getString(R.string.zoom_level, Math.round(currentZoom * 100));
        binding.tvZoomLevel.setText(zoomText);
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.pdf_reader_menu, menu);
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();
        if (itemId == R.id.action_search) {
            showSearch();
            return true;
        } else if (itemId == R.id.action_bookmark) {
            presenter.toggleBookmark();
            return true;
        } else if (itemId == R.id.action_share) {
            presenter.shareDocument();
            return true;
        } else if (itemId == R.id.action_night_mode) {
            toggleNightMode();
            return true;
        } else if (itemId == R.id.action_rotate) {
            rotatePage();
            return true;
        } else if (itemId == R.id.action_fit_width) {
            fitToWidth();
            return true;
        } else if (itemId == R.id.action_fit_height) {
            fitToHeight();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    // PDFReaderContract.View implementation
    @Override
    public void showLoading() {
        binding.progressLoading.setVisibility(View.VISIBLE);
    }
    
    @Override
    public void hideLoading() {
        binding.progressLoading.setVisibility(View.GONE);
    }
    
    @Override
    public void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }
    
    @Override
    public void showSuccess(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public void showMessage(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
    
    @Override
    public boolean isActive() {
        return !isFinishing() && !isDestroyed();
    }
    
    @Override
    public void displayPDF(PDFDocument document) {
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle(document.getDisplayName());
        }
        
        showLoading();
        binding.pdfView.fromFile(new java.io.File(document.getFilePath()))
                .defaultPage(Math.max(0, document.getLastPageRead() - 1))
                .onLoad(this::hideLoading)
                .onPageChange(this::onPageChanged)
                .onError(this::onPDFError)
                .enableSwipe(true)
                .swipeHorizontal(false)
                .enableDoubletap(true)
                .scrollHandle(new DefaultScrollHandle(this))
                .spacing(10)
                .load();
    }
    
    @Override
    public void displayPDF(Uri uri) {
        showLoading();
        binding.pdfView.fromUri(uri)
                .onLoad(this::hideLoading)
                .onPageChange(this::onPageChanged)
                .onError(this::onPDFError)
                .enableSwipe(true)
                .swipeHorizontal(false)
                .enableDoubletap(true)
                .scrollHandle(new DefaultScrollHandle(this))
                .spacing(10)
                .load();
    }
    
    private void onPageChanged(int page, int pageCount) {
        currentPage = page;
        totalPages = pageCount;
        updatePageIndicator();
        updatePageSlider();
    }
    
    private void onPDFError(Throwable t) {
        hideLoading();
        showError("Error loading PDF: " + t.getMessage());
    }
}
