package com.ccppdfreader.app.activities;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import com.ccppdfreader.app.R;
import com.ccppdfreader.app.managers.BookmarkManager;
import com.ccppdfreader.app.managers.PDFManager;
import com.ccppdfreader.app.models.Bookmark;
import com.ccppdfreader.app.models.PDFDocument;
import com.ccppdfreader.app.views.PDFView;
import java.io.File;

public class PDFReaderActivity extends AppCompatActivity implements PDFView.OnPageChangeListener {

    private PDFDocument currentDocument;
    private PDFManager pdfManager;
    private BookmarkManager bookmarkManager;
    private PDFView pdfView;
    private TextView textViewPageInfo;
    private TextView textViewProgress;
    private TextView textViewPdfPlaceholder;
    private int currentPage = 1;
    private int totalPages = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_pdf_reader);

        initViews();
        setupToolbar();
        loadPDFDocument();
    }

    private void initViews() {
        pdfView = findViewById(R.id.pdfView);
        textViewPageInfo = findViewById(R.id.textViewPageInfo);
        textViewProgress = findViewById(R.id.textViewProgress);
        textViewPdfPlaceholder = findViewById(R.id.textViewPdfPlaceholder);

        pdfManager = new PDFManager(this);
        bookmarkManager = new BookmarkManager(this);

        // Set page change listener
        pdfView.setOnPageChangeListener(this);
    }

    private void setupToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
    }

    private void loadPDFDocument() {
        Intent intent = getIntent();
        if (intent != null && intent.hasExtra("pdf_document")) {
            currentDocument = (PDFDocument) intent.getSerializableExtra("pdf_document");

            if (currentDocument != null) {
                setupDocument();
            } else {
                showError("No PDF document provided");
            }
        } else {
            showError("No PDF document provided");
        }
    }

    private void setupDocument() {
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle(currentDocument.getTitle());
        }

        // Load the actual PDF file
        File pdfFile = new File(currentDocument.getFilePath());
        if (pdfFile.exists()) {
            // Load PDF using our custom PDFView
            pdfView.loadPDF(currentDocument.getFilePath());

            // Hide placeholder
            textViewPdfPlaceholder.setVisibility(View.GONE);

            // Set initial page if available
            currentPage = Math.max(1, currentDocument.getLastPageRead());

        } else {
            showError("PDF file not found: " + currentDocument.getFilePath());
        }
    }

    private void updatePageInfo() {
        if (totalPages > 0) {
            textViewPageInfo.setText("Page " + currentPage + " of " + totalPages);

            double progress = (double) currentPage / totalPages * 100;
            textViewProgress.setText(String.format("%.0f%%", progress));

            // Update document progress
            currentDocument.setLastPageRead(currentPage);
            currentDocument.updateReadingProgress();
            pdfManager.updateDocument(currentDocument);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.pdf_reader_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();

        if (id == android.R.id.home) {
            onBackPressed();
            return true;
        } else if (id == R.id.action_bookmark) {
            addBookmark();
            return true;
        } else if (id == R.id.action_bookmarks_list) {
            showBookmarks();
            return true;
        } else if (id == R.id.action_previous_page) {
            previousPage();
            return true;
        } else if (id == R.id.action_next_page) {
            nextPage();
            return true;
        } else if (id == R.id.action_favorite) {
            toggleFavorite();
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    private void addBookmark() {
        if (currentDocument != null) {
            String title = "Page " + currentPage;
            Bookmark bookmark = new Bookmark(currentDocument.getId(), currentDocument.getTitle(), currentPage, title);
            bookmarkManager.addBookmark(bookmark);
            Toast.makeText(this, "Bookmark added for page " + currentPage, Toast.LENGTH_SHORT).show();
        }
    }

    private void showBookmarks() {
        Intent intent = new Intent(this, BookmarksActivity.class);
        intent.putExtra("document_id", currentDocument.getId());
        startActivity(intent);
    }

    private void previousPage() {
        pdfView.previousPage();
    }

    private void nextPage() {
        pdfView.nextPage();
    }

    private void toggleFavorite() {
        if (currentDocument.isFavorite()) {
            pdfManager.removeFavoriteDocument(currentDocument.getId());
            currentDocument.setFavorite(false);
            Toast.makeText(this, "Removed from favorites", Toast.LENGTH_SHORT).show();
        } else {
            pdfManager.addFavoriteDocument(currentDocument);
            currentDocument.setFavorite(true);
            Toast.makeText(this, "Added to favorites", Toast.LENGTH_SHORT).show();
        }
    }

    private void showError(String message) {
        textViewPdfPlaceholder.setText("❌ Error: " + message);
        textViewPdfPlaceholder.setVisibility(View.VISIBLE);
        Toast.makeText(this, message, Toast.LENGTH_LONG).show();
    }

    // PDFView.OnPageChangeListener implementation
    @Override
    public void onPageChanged(int currentPage, int totalPages) {
        this.currentPage = currentPage;
        this.totalPages = totalPages;

        // Update document with new page count if needed
        if (currentDocument.getTotalPages() != totalPages) {
            currentDocument.setTotalPages(totalPages);
        }

        updatePageInfo();
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (currentDocument != null) {
            // Save current reading position
            pdfManager.updateDocument(currentDocument);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (pdfView != null) {
            pdfView.cleanup();
        }
    }
}
