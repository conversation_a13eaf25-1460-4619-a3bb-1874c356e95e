package com.ccppdfreader.app.views;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.pdf.PdfRenderer;
import android.os.ParcelFileDescriptor;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.ccppdfreader.app.R;
import com.ccppdfreader.app.adapters.PDFPageAdapter;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class PDFView extends LinearLayout {

    public interface OnPageChangeListener {
        void onPageChanged(int currentPage, int totalPages);
    }

    private RecyclerView recyclerViewPages;
    private ProgressBar progressBar;
    private TextView textViewError;
    private PDFPageAdapter adapter;
    private PdfRenderer pdfRenderer;
    private ParcelFileDescriptor fileDescriptor;
    private OnPageChangeListener pageChangeListener;
    private int currentPage = 0;
    private int totalPages = 0;

    public PDFView(Context context) {
        super(context);
        init();
    }

    public PDFView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public PDFView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        setOrientation(VERTICAL);
        LayoutInflater.from(getContext()).inflate(R.layout.view_pdf, this, true);
        
        recyclerViewPages = findViewById(R.id.recyclerViewPages);
        progressBar = findViewById(R.id.progressBarPdf);
        textViewError = findViewById(R.id.textViewError);
        
        setupRecyclerView();
    }

    private void setupRecyclerView() {
        adapter = new PDFPageAdapter(getContext());
        recyclerViewPages.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerViewPages.setAdapter(adapter);
        
        // Add scroll listener to track current page
        recyclerViewPages.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                updateCurrentPage();
            }
        });
    }

    public void loadPDF(String filePath) {
        showLoading();

        new Thread(() -> {
            try {
                File file = new File(filePath);
                if (!file.exists()) {
                    post(() -> showError("PDF file not found: " + filePath));
                    return;
                }

                // Check if PdfRenderer is available (API 21+)
                if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.LOLLIPOP) {
                    post(() -> showError("PDF viewing requires Android 5.0 or higher"));
                    return;
                }

                fileDescriptor = ParcelFileDescriptor.open(file, ParcelFileDescriptor.MODE_READ_ONLY);
                pdfRenderer = new PdfRenderer(fileDescriptor);
                totalPages = pdfRenderer.getPageCount();

                // Load only first few pages initially to avoid memory issues
                int pagesToLoad = Math.min(totalPages, 5);
                List<Bitmap> pages = new ArrayList<>();

                for (int i = 0; i < pagesToLoad; i++) {
                    try {
                        PdfRenderer.Page page = pdfRenderer.openPage(i);

                        // Create bitmap with appropriate size
                        int width = getContext().getResources().getDisplayMetrics().widthPixels - 64; // padding
                        int height = (int) ((float) width * page.getHeight() / page.getWidth());

                        // Limit height to avoid memory issues
                        if (height > 2000) {
                            height = 2000;
                            width = (int) ((float) height * page.getWidth() / page.getHeight());
                        }

                        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565); // Use RGB_565 to save memory
                        page.render(bitmap, null, null, PdfRenderer.Page.RENDER_MODE_FOR_DISPLAY);
                        pages.add(bitmap);
                        page.close();

                    } catch (Exception e) {
                        // Skip problematic pages
                        continue;
                    }
                }

                post(() -> {
                    if (pages.size() > 0) {
                        adapter.setPages(pages);
                        showContent();
                        if (pageChangeListener != null) {
                            pageChangeListener.onPageChanged(1, totalPages);
                        }
                    } else {
                        showError("Could not load any pages from PDF");
                    }
                });

            } catch (IOException e) {
                post(() -> showError("Error loading PDF: " + e.getMessage()));
            } catch (SecurityException e) {
                post(() -> showError("Permission denied to access PDF file"));
            } catch (Exception e) {
                post(() -> showError("Unexpected error: " + e.getMessage()));
            }
        }).start();
    }

    public void goToPage(int pageNumber) {
        if (pageNumber >= 1 && pageNumber <= totalPages) {
            recyclerViewPages.scrollToPosition(pageNumber - 1);
            currentPage = pageNumber;
            if (pageChangeListener != null) {
                pageChangeListener.onPageChanged(currentPage, totalPages);
            }
        }
    }

    public void nextPage() {
        if (currentPage < totalPages) {
            goToPage(currentPage + 1);
        }
    }

    public void previousPage() {
        if (currentPage > 1) {
            goToPage(currentPage - 1);
        }
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public void setOnPageChangeListener(OnPageChangeListener listener) {
        this.pageChangeListener = listener;
    }

    private void updateCurrentPage() {
        LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerViewPages.getLayoutManager();
        if (layoutManager != null) {
            int firstVisiblePosition = layoutManager.findFirstVisibleItemPosition();
            if (firstVisiblePosition >= 0 && firstVisiblePosition != currentPage - 1) {
                currentPage = firstVisiblePosition + 1;
                if (pageChangeListener != null) {
                    pageChangeListener.onPageChanged(currentPage, totalPages);
                }
            }
        }
    }

    private void showLoading() {
        progressBar.setVisibility(View.VISIBLE);
        recyclerViewPages.setVisibility(View.GONE);
        textViewError.setVisibility(View.GONE);
    }

    private void showContent() {
        progressBar.setVisibility(View.GONE);
        recyclerViewPages.setVisibility(View.VISIBLE);
        textViewError.setVisibility(View.GONE);
    }

    private void showError(String message) {
        progressBar.setVisibility(View.GONE);
        recyclerViewPages.setVisibility(View.GONE);
        textViewError.setVisibility(View.VISIBLE);
        textViewError.setText(message);
        Toast.makeText(getContext(), message, Toast.LENGTH_LONG).show();
    }

    public void cleanup() {
        try {
            if (pdfRenderer != null) {
                pdfRenderer.close();
            }
            if (fileDescriptor != null) {
                fileDescriptor.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        cleanup();
    }
}
