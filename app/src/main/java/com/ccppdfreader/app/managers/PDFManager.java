package com.ccppdfreader.app.managers;

import android.content.Context;
import android.content.SharedPreferences;
import com.ccppdfreader.app.models.PDFDocument;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

public class PDFManager {
    private static final String PREFS_NAME = "pdf_manager_prefs";
    private static final String KEY_RECENT_DOCUMENTS = "recent_documents";
    private static final String KEY_FAVORITE_DOCUMENTS = "favorite_documents";
    private static final int MAX_RECENT_DOCUMENTS = 20;

    private Context context;
    private SharedPreferences prefs;
    private Gson gson;
    private List<PDFDocument> recentDocuments;
    private List<PDFDocument> favoriteDocuments;

    public PDFManager(Context context) {
        this.context = context;
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.gson = new Gson();
        loadDocuments();
    }

    private void loadDocuments() {
        // Load recent documents
        String recentJson = prefs.getString(KEY_RECENT_DOCUMENTS, "[]");
        Type recentType = new TypeToken<List<PDFDocument>>(){}.getType();
        recentDocuments = gson.fromJson(recentJson, recentType);
        if (recentDocuments == null) {
            recentDocuments = new ArrayList<>();
        }

        // Load favorite documents
        String favoriteJson = prefs.getString(KEY_FAVORITE_DOCUMENTS, "[]");
        Type favoriteType = new TypeToken<List<PDFDocument>>(){}.getType();
        favoriteDocuments = gson.fromJson(favoriteJson, favoriteType);
        if (favoriteDocuments == null) {
            favoriteDocuments = new ArrayList<>();
        }
    }

    private void saveDocuments() {
        SharedPreferences.Editor editor = prefs.edit();
        
        // Save recent documents
        String recentJson = gson.toJson(recentDocuments);
        editor.putString(KEY_RECENT_DOCUMENTS, recentJson);
        
        // Save favorite documents
        String favoriteJson = gson.toJson(favoriteDocuments);
        editor.putString(KEY_FAVORITE_DOCUMENTS, favoriteJson);
        
        editor.apply();
    }

    public void addRecentDocument(PDFDocument document) {
        if (document == null) return;

        // Remove if already exists
        recentDocuments.removeIf(doc -> doc.getId().equals(document.getId()));
        
        // Add to beginning
        document.setLastOpened(new Date());
        recentDocuments.add(0, document);
        
        // Limit size
        if (recentDocuments.size() > MAX_RECENT_DOCUMENTS) {
            recentDocuments = recentDocuments.subList(0, MAX_RECENT_DOCUMENTS);
        }
        
        saveDocuments();
    }

    public void removeRecentDocument(String documentId) {
        recentDocuments.removeIf(doc -> doc.getId().equals(documentId));
        saveDocuments();
    }

    public void clearRecentDocuments() {
        recentDocuments.clear();
        saveDocuments();
    }

    public List<PDFDocument> getRecentDocuments() {
        return new ArrayList<>(recentDocuments);
    }

    public List<PDFDocument> getRecentDocuments(int limit) {
        int size = Math.min(limit, recentDocuments.size());
        return new ArrayList<>(recentDocuments.subList(0, size));
    }

    public void addFavoriteDocument(PDFDocument document) {
        if (document == null) return;

        // Remove if already exists
        favoriteDocuments.removeIf(doc -> doc.getId().equals(document.getId()));
        
        // Mark as favorite and add
        document.setFavorite(true);
        favoriteDocuments.add(document);
        
        // Sort by title
        Collections.sort(favoriteDocuments, new Comparator<PDFDocument>() {
            @Override
            public int compare(PDFDocument o1, PDFDocument o2) {
                return o1.getTitle().compareToIgnoreCase(o2.getTitle());
            }
        });
        
        saveDocuments();
    }

    public void removeFavoriteDocument(String documentId) {
        favoriteDocuments.removeIf(doc -> doc.getId().equals(documentId));
        
        // Also update in recent documents
        for (PDFDocument doc : recentDocuments) {
            if (doc.getId().equals(documentId)) {
                doc.setFavorite(false);
                break;
            }
        }
        
        saveDocuments();
    }

    public List<PDFDocument> getFavoriteDocuments() {
        return new ArrayList<>(favoriteDocuments);
    }

    public boolean isFavorite(String documentId) {
        return favoriteDocuments.stream().anyMatch(doc -> doc.getId().equals(documentId));
    }

    public void updateDocument(PDFDocument document) {
        if (document == null) return;

        // Update in recent documents
        for (int i = 0; i < recentDocuments.size(); i++) {
            if (recentDocuments.get(i).getId().equals(document.getId())) {
                recentDocuments.set(i, document);
                break;
            }
        }

        // Update in favorite documents
        for (int i = 0; i < favoriteDocuments.size(); i++) {
            if (favoriteDocuments.get(i).getId().equals(document.getId())) {
                favoriteDocuments.set(i, document);
                break;
            }
        }

        saveDocuments();
    }

    public PDFDocument findDocumentById(String documentId) {
        // Search in recent documents first
        for (PDFDocument doc : recentDocuments) {
            if (doc.getId().equals(documentId)) {
                return doc;
            }
        }
        
        // Search in favorite documents
        for (PDFDocument doc : favoriteDocuments) {
            if (doc.getId().equals(documentId)) {
                return doc;
            }
        }
        
        return null;
    }

    public List<PDFDocument> searchDocuments(String query) {
        List<PDFDocument> results = new ArrayList<>();
        String lowerQuery = query.toLowerCase();
        
        // Search in recent documents
        for (PDFDocument doc : recentDocuments) {
            if (doc.getTitle().toLowerCase().contains(lowerQuery) ||
                doc.getFileName().toLowerCase().contains(lowerQuery)) {
                results.add(doc);
            }
        }
        
        // Search in favorite documents (avoid duplicates)
        for (PDFDocument doc : favoriteDocuments) {
            if ((doc.getTitle().toLowerCase().contains(lowerQuery) ||
                 doc.getFileName().toLowerCase().contains(lowerQuery)) &&
                !results.contains(doc)) {
                results.add(doc);
            }
        }
        
        return results;
    }

    public int getTotalDocuments() {
        return recentDocuments.size();
    }

    public int getFavoriteCount() {
        return favoriteDocuments.size();
    }
}
