package com.ccppdfreader.app.managers;

import android.content.SharedPreferences;
import androidx.appcompat.app.AppCompatDelegate;
import com.ccppdfreader.app.CCPPDFReaderApplication;

/**
 * Manager class for handling app settings and preferences
 */
public class SettingsManager {
    
    private static final String TAG = "SettingsManager";
    
    // Preference keys
    public static final String PREF_READING_MODE = "reading_mode";
    public static final String PREF_AUTO_NIGHT_MODE = "auto_night_mode";
    public static final String PREF_DEFAULT_ZOOM = "default_zoom";
    public static final String PREF_PAGE_TRANSITION = "page_transition";
    public static final String PREF_KEEP_SCREEN_ON = "keep_screen_on";
    public static final String PREF_VOLUME_KEY_NAVIGATION = "volume_key_navigation";
    public static final String PREF_SHOW_PAGE_NUMBER = "show_page_number";
    public static final String PREF_FULLSCREEN_MODE = "fullscreen_mode";
    
    // Default values
    public static final String DEFAULT_READING_MODE = "day";
    public static final boolean DEFAULT_AUTO_NIGHT_MODE = true;
    public static final float DEFAULT_ZOOM = 1.0f;
    public static final String DEFAULT_PAGE_TRANSITION = "slide";
    public static final boolean DEFAULT_KEEP_SCREEN_ON = false;
    public static final boolean DEFAULT_VOLUME_KEY_NAVIGATION = false;
    public static final boolean DEFAULT_SHOW_PAGE_NUMBER = true;
    public static final boolean DEFAULT_FULLSCREEN_MODE = true;
    
    private static SettingsManager instance;
    private final SharedPreferences preferences;
    
    private SettingsManager() {
        preferences = CCPPDFReaderApplication.getInstance().getAppPreferences();
    }
    
    public static synchronized SettingsManager getInstance() {
        if (instance == null) {
            instance = new SettingsManager();
        }
        return instance;
    }
    
    // Reading Mode
    public String getReadingMode() {
        return preferences.getString(PREF_READING_MODE, DEFAULT_READING_MODE);
    }
    
    public void setReadingMode(String mode) {
        preferences.edit().putString(PREF_READING_MODE, mode).apply();
    }
    
    public boolean isNightMode() {
        return "night".equals(getReadingMode());
    }
    
    // Auto Night Mode
    public boolean isAutoNightMode() {
        return preferences.getBoolean(PREF_AUTO_NIGHT_MODE, DEFAULT_AUTO_NIGHT_MODE);
    }
    
    public void setAutoNightMode(boolean enabled) {
        preferences.edit().putBoolean(PREF_AUTO_NIGHT_MODE, enabled).apply();
    }
    
    // Default Zoom
    public float getDefaultZoom() {
        return preferences.getFloat(PREF_DEFAULT_ZOOM, DEFAULT_ZOOM);
    }
    
    public void setDefaultZoom(float zoom) {
        preferences.edit().putFloat(PREF_DEFAULT_ZOOM, zoom).apply();
    }
    
    // Page Transition
    public String getPageTransition() {
        return preferences.getString(PREF_PAGE_TRANSITION, DEFAULT_PAGE_TRANSITION);
    }
    
    public void setPageTransition(String transition) {
        preferences.edit().putString(PREF_PAGE_TRANSITION, transition).apply();
    }
    
    // Keep Screen On
    public boolean isKeepScreenOn() {
        return preferences.getBoolean(PREF_KEEP_SCREEN_ON, DEFAULT_KEEP_SCREEN_ON);
    }
    
    public void setKeepScreenOn(boolean enabled) {
        preferences.edit().putBoolean(PREF_KEEP_SCREEN_ON, enabled).apply();
    }
    
    // Volume Key Navigation
    public boolean isVolumeKeyNavigation() {
        return preferences.getBoolean(PREF_VOLUME_KEY_NAVIGATION, DEFAULT_VOLUME_KEY_NAVIGATION);
    }
    
    public void setVolumeKeyNavigation(boolean enabled) {
        preferences.edit().putBoolean(PREF_VOLUME_KEY_NAVIGATION, enabled).apply();
    }
    
    // Show Page Number
    public boolean isShowPageNumber() {
        return preferences.getBoolean(PREF_SHOW_PAGE_NUMBER, DEFAULT_SHOW_PAGE_NUMBER);
    }
    
    public void setShowPageNumber(boolean enabled) {
        preferences.edit().putBoolean(PREF_SHOW_PAGE_NUMBER, enabled).apply();
    }
    
    // Fullscreen Mode
    public boolean isFullscreenMode() {
        return preferences.getBoolean(PREF_FULLSCREEN_MODE, DEFAULT_FULLSCREEN_MODE);
    }
    
    public void setFullscreenMode(boolean enabled) {
        preferences.edit().putBoolean(PREF_FULLSCREEN_MODE, enabled).apply();
    }
    
    // Theme Management
    public void applyTheme() {
        if (isAutoNightMode()) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM);
        } else {
            if (isNightMode()) {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
            } else {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
            }
        }
    }
    
    // Reset to defaults
    public void resetToDefaults() {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(PREF_READING_MODE, DEFAULT_READING_MODE);
        editor.putBoolean(PREF_AUTO_NIGHT_MODE, DEFAULT_AUTO_NIGHT_MODE);
        editor.putFloat(PREF_DEFAULT_ZOOM, DEFAULT_ZOOM);
        editor.putString(PREF_PAGE_TRANSITION, DEFAULT_PAGE_TRANSITION);
        editor.putBoolean(PREF_KEEP_SCREEN_ON, DEFAULT_KEEP_SCREEN_ON);
        editor.putBoolean(PREF_VOLUME_KEY_NAVIGATION, DEFAULT_VOLUME_KEY_NAVIGATION);
        editor.putBoolean(PREF_SHOW_PAGE_NUMBER, DEFAULT_SHOW_PAGE_NUMBER);
        editor.putBoolean(PREF_FULLSCREEN_MODE, DEFAULT_FULLSCREEN_MODE);
        editor.apply();
        
        applyTheme();
    }
}
