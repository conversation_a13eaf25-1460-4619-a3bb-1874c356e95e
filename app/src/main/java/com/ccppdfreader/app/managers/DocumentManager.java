package com.ccppdfreader.app.managers;

import android.content.Context;
import android.content.SharedPreferences;
import com.ccppdfreader.app.CCPPDFReaderApplication;
import com.ccppdfreader.app.models.PDFDocument;
import com.ccppdfreader.app.utils.FileUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * Manager class for handling PDF documents
 * Manages recent files, favorites, and document metadata
 */
public class DocumentManager {
    
    private static final String TAG = "DocumentManager";
    private static final String PREF_RECENT_DOCUMENTS = "recent_documents";
    private static final String PREF_FAVORITE_DOCUMENTS = "favorite_documents";
    private static final int MAX_RECENT_DOCUMENTS = 50;
    
    private static DocumentManager instance;
    private final SharedPreferences preferences;
    private final Gson gson;
    
    private List<PDFDocument> recentDocuments;
    private List<PDFDocument> favoriteDocuments;
    
    private DocumentManager() {
        preferences = CCPPDFReaderApplication.getInstance().getAppPreferences();
        gson = new Gson();
        loadDocuments();
    }
    
    public static synchronized DocumentManager getInstance() {
        if (instance == null) {
            instance = new DocumentManager();
        }
        return instance;
    }
    
    /**
     * Load documents from preferences
     */
    private void loadDocuments() {
        // Load recent documents
        String recentJson = preferences.getString(PREF_RECENT_DOCUMENTS, "[]");
        Type listType = new TypeToken<List<PDFDocument>>(){}.getType();
        recentDocuments = gson.fromJson(recentJson, listType);
        if (recentDocuments == null) {
            recentDocuments = new ArrayList<>();
        }
        
        // Load favorite documents
        String favoriteJson = preferences.getString(PREF_FAVORITE_DOCUMENTS, "[]");
        favoriteDocuments = gson.fromJson(favoriteJson, listType);
        if (favoriteDocuments == null) {
            favoriteDocuments = new ArrayList<>();
        }
        
        // Clean up non-existent files
        cleanupDocuments();
    }
    
    /**
     * Save documents to preferences
     */
    private void saveDocuments() {
        String recentJson = gson.toJson(recentDocuments);
        String favoriteJson = gson.toJson(favoriteDocuments);
        
        preferences.edit()
                .putString(PREF_RECENT_DOCUMENTS, recentJson)
                .putString(PREF_FAVORITE_DOCUMENTS, favoriteJson)
                .apply();
    }
    
    /**
     * Remove non-existent files from lists
     */
    private void cleanupDocuments() {
        recentDocuments.removeIf(doc -> !FileUtils.isFileReadable(doc.getFilePath()));
        favoriteDocuments.removeIf(doc -> !FileUtils.isFileReadable(doc.getFilePath()));
    }
    
    /**
     * Add document to recent list
     */
    public void addToRecent(PDFDocument document) {
        if (document == null || document.getFilePath() == null) return;
        
        // Remove if already exists
        recentDocuments.removeIf(doc -> doc.getFilePath().equals(document.getFilePath()));
        
        // Update last opened time
        document.setLastOpened(new Date());
        
        // Add to beginning of list
        recentDocuments.add(0, document);
        
        // Limit size
        if (recentDocuments.size() > MAX_RECENT_DOCUMENTS) {
            recentDocuments = recentDocuments.subList(0, MAX_RECENT_DOCUMENTS);
        }
        
        saveDocuments();
    }
    
    /**
     * Get recent documents
     */
    public List<PDFDocument> getRecentDocuments() {
        return new ArrayList<>(recentDocuments);
    }
    
    /**
     * Add document to favorites
     */
    public void addToFavorites(PDFDocument document) {
        if (document == null || document.getFilePath() == null) return;
        
        // Check if already in favorites
        boolean exists = favoriteDocuments.stream()
                .anyMatch(doc -> doc.getFilePath().equals(document.getFilePath()));
        
        if (!exists) {
            document.setFavorite(true);
            favoriteDocuments.add(document);
            saveDocuments();
        }
    }
    
    /**
     * Remove document from favorites
     */
    public void removeFromFavorites(String filePath) {
        if (filePath == null) return;
        
        favoriteDocuments.removeIf(doc -> doc.getFilePath().equals(filePath));
        
        // Update in recent documents too
        recentDocuments.stream()
                .filter(doc -> doc.getFilePath().equals(filePath))
                .forEach(doc -> doc.setFavorite(false));
        
        saveDocuments();
    }
    
    /**
     * Get favorite documents
     */
    public List<PDFDocument> getFavoriteDocuments() {
        return new ArrayList<>(favoriteDocuments);
    }
    
    /**
     * Check if document is in favorites
     */
    public boolean isFavorite(String filePath) {
        if (filePath == null) return false;
        return favoriteDocuments.stream()
                .anyMatch(doc -> doc.getFilePath().equals(filePath));
    }
    
    /**
     * Update document reading progress
     */
    public void updateReadingProgress(String filePath, int currentPage, int totalPages) {
        if (filePath == null) return;
        
        // Update in recent documents
        recentDocuments.stream()
                .filter(doc -> doc.getFilePath().equals(filePath))
                .forEach(doc -> {
                    doc.setLastPageRead(currentPage);
                    doc.setTotalPages(totalPages);
                    doc.setLastOpened(new Date());
                });
        
        // Update in favorites
        favoriteDocuments.stream()
                .filter(doc -> doc.getFilePath().equals(filePath))
                .forEach(doc -> {
                    doc.setLastPageRead(currentPage);
                    doc.setTotalPages(totalPages);
                });
        
        saveDocuments();
    }
    
    /**
     * Get document by file path
     */
    public PDFDocument getDocument(String filePath) {
        if (filePath == null) return null;
        
        // Check recent documents first
        for (PDFDocument doc : recentDocuments) {
            if (doc.getFilePath().equals(filePath)) {
                return doc;
            }
        }
        
        // Check favorites
        for (PDFDocument doc : favoriteDocuments) {
            if (doc.getFilePath().equals(filePath)) {
                return doc;
            }
        }
        
        return null;
    }
    
    /**
     * Create PDFDocument from file path
     */
    public PDFDocument createDocumentFromPath(String filePath) {
        if (filePath == null || !FileUtils.isFileReadable(filePath)) {
            return null;
        }
        
        PDFDocument document = new PDFDocument(filePath, FileUtils.getFileName(filePath));
        document.setDisplayName(FileUtils.getFileNameWithoutExtension(filePath));
        document.setFileSize(FileUtils.getFileSize(filePath));
        document.setLastModified(FileUtils.getFileLastModified(filePath));
        document.setFavorite(isFavorite(filePath));
        
        return document;
    }
    
    /**
     * Clear all recent documents
     */
    public void clearRecentDocuments() {
        recentDocuments.clear();
        saveDocuments();
    }
    
    /**
     * Clear all favorite documents
     */
    public void clearFavoriteDocuments() {
        favoriteDocuments.clear();
        saveDocuments();
    }
    
    /**
     * Get documents sorted by last opened
     */
    public List<PDFDocument> getRecentDocumentsSorted() {
        List<PDFDocument> sorted = new ArrayList<>(recentDocuments);
        sorted.sort((doc1, doc2) -> {
            if (doc1.getLastOpened() == null && doc2.getLastOpened() == null) return 0;
            if (doc1.getLastOpened() == null) return 1;
            if (doc2.getLastOpened() == null) return -1;
            return doc2.getLastOpened().compareTo(doc1.getLastOpened());
        });
        return sorted;
    }
}
