package com.ccppdfreader.app.managers;

import android.content.Context;
import android.content.SharedPreferences;
import com.ccppdfreader.app.models.Bookmark;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class BookmarkManager {
    private static final String PREFS_NAME = "bookmark_manager_prefs";
    private static final String KEY_BOOKMARKS = "bookmarks";

    private Context context;
    private SharedPreferences prefs;
    private Gson gson;
    private List<Bookmark> bookmarks;

    public BookmarkManager(Context context) {
        this.context = context;
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.gson = new Gson();
        loadBookmarks();
    }

    private void loadBookmarks() {
        String bookmarksJson = prefs.getString(KEY_BOOKMARKS, "[]");
        Type type = new TypeToken<List<Bookmark>>(){}.getType();
        bookmarks = gson.fromJson(bookmarksJson, type);
        if (bookmarks == null) {
            bookmarks = new ArrayList<>();
        }
    }

    private void saveBookmarks() {
        String bookmarksJson = gson.toJson(bookmarks);
        prefs.edit().putString(KEY_BOOKMARKS, bookmarksJson).apply();
    }

    public void addBookmark(Bookmark bookmark) {
        if (bookmark == null) return;

        // Check if bookmark already exists for this page
        if (hasBookmarkForPage(bookmark.getDocumentId(), bookmark.getPageNumber())) {
            return; // Don't add duplicate bookmarks for the same page
        }

        bookmarks.add(bookmark);
        sortBookmarks();
        saveBookmarks();
    }

    public void removeBookmark(String bookmarkId) {
        bookmarks.removeIf(bookmark -> bookmark.getId().equals(bookmarkId));
        saveBookmarks();
    }

    public void removeBookmark(Bookmark bookmark) {
        if (bookmark != null) {
            removeBookmark(bookmark.getId());
        }
    }

    public void updateBookmark(Bookmark bookmark) {
        if (bookmark == null) return;

        for (int i = 0; i < bookmarks.size(); i++) {
            if (bookmarks.get(i).getId().equals(bookmark.getId())) {
                bookmarks.set(i, bookmark);
                break;
            }
        }
        
        sortBookmarks();
        saveBookmarks();
    }

    public List<Bookmark> getAllBookmarks() {
        return new ArrayList<>(bookmarks);
    }

    public List<Bookmark> getBookmarksForDocument(String documentId) {
        List<Bookmark> documentBookmarks = new ArrayList<>();
        for (Bookmark bookmark : bookmarks) {
            if (bookmark.getDocumentId().equals(documentId)) {
                documentBookmarks.add(bookmark);
            }
        }
        
        // Sort by page number
        Collections.sort(documentBookmarks, new Comparator<Bookmark>() {
            @Override
            public int compare(Bookmark o1, Bookmark o2) {
                return Integer.compare(o1.getPageNumber(), o2.getPageNumber());
            }
        });
        
        return documentBookmarks;
    }

    public Bookmark findBookmarkById(String bookmarkId) {
        for (Bookmark bookmark : bookmarks) {
            if (bookmark.getId().equals(bookmarkId)) {
                return bookmark;
            }
        }
        return null;
    }

    public boolean hasBookmarkForPage(String documentId, int pageNumber) {
        for (Bookmark bookmark : bookmarks) {
            if (bookmark.getDocumentId().equals(documentId) && 
                bookmark.getPageNumber() == pageNumber) {
                return true;
            }
        }
        return false;
    }

    public Bookmark getBookmarkForPage(String documentId, int pageNumber) {
        for (Bookmark bookmark : bookmarks) {
            if (bookmark.getDocumentId().equals(documentId) && 
                bookmark.getPageNumber() == pageNumber) {
                return bookmark;
            }
        }
        return null;
    }

    public void removeBookmarksForDocument(String documentId) {
        bookmarks.removeIf(bookmark -> bookmark.getDocumentId().equals(documentId));
        saveBookmarks();
    }

    public List<Bookmark> searchBookmarks(String query) {
        List<Bookmark> results = new ArrayList<>();
        String lowerQuery = query.toLowerCase();
        
        for (Bookmark bookmark : bookmarks) {
            if (bookmark.getTitle().toLowerCase().contains(lowerQuery) ||
                bookmark.getDocumentTitle().toLowerCase().contains(lowerQuery) ||
                (bookmark.getNote() != null && bookmark.getNote().toLowerCase().contains(lowerQuery))) {
                results.add(bookmark);
            }
        }
        
        return results;
    }

    public int getBookmarkCount() {
        return bookmarks.size();
    }

    public int getBookmarkCountForDocument(String documentId) {
        int count = 0;
        for (Bookmark bookmark : bookmarks) {
            if (bookmark.getDocumentId().equals(documentId)) {
                count++;
            }
        }
        return count;
    }

    public void clearAllBookmarks() {
        bookmarks.clear();
        saveBookmarks();
    }

    private void sortBookmarks() {
        Collections.sort(bookmarks, new Comparator<Bookmark>() {
            @Override
            public int compare(Bookmark o1, Bookmark o2) {
                // First sort by document title
                int docCompare = o1.getDocumentTitle().compareToIgnoreCase(o2.getDocumentTitle());
                if (docCompare != 0) {
                    return docCompare;
                }
                // Then by page number
                return Integer.compare(o1.getPageNumber(), o2.getPageNumber());
            }
        });
    }

    public List<String> getUniqueDocumentTitles() {
        List<String> titles = new ArrayList<>();
        for (Bookmark bookmark : bookmarks) {
            if (!titles.contains(bookmark.getDocumentTitle())) {
                titles.add(bookmark.getDocumentTitle());
            }
        }
        Collections.sort(titles);
        return titles;
    }

    public Bookmark createQuickBookmark(String documentId, String documentTitle, int pageNumber) {
        Bookmark bookmark = new Bookmark(documentId, documentTitle, pageNumber);
        bookmark.setTitle("Quick Bookmark - Page " + pageNumber);
        addBookmark(bookmark);
        return bookmark;
    }
}
