package com.ccppdfreader.app.managers;

import android.content.SharedPreferences;
import com.ccppdfreader.app.CCPPDFReaderApplication;
import com.ccppdfreader.app.models.Bookmark;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Manager class for handling PDF bookmarks
 */
public class BookmarkManager {
    
    private static final String TAG = "BookmarkManager";
    private static final String PREF_BOOKMARKS = "bookmarks";
    
    private static BookmarkManager instance;
    private final SharedPreferences preferences;
    private final Gson gson;
    
    private List<Bookmark> bookmarks;
    
    private BookmarkManager() {
        preferences = CCPPDFReaderApplication.getInstance().getAppPreferences();
        gson = new Gson();
        loadBookmarks();
    }
    
    public static synchronized BookmarkManager getInstance() {
        if (instance == null) {
            instance = new BookmarkManager();
        }
        return instance;
    }
    
    /**
     * Load bookmarks from preferences
     */
    private void loadBookmarks() {
        String bookmarksJson = preferences.getString(PREF_BOOKMARKS, "[]");
        Type listType = new TypeToken<List<Bookmark>>(){}.getType();
        bookmarks = gson.fromJson(bookmarksJson, listType);
        if (bookmarks == null) {
            bookmarks = new ArrayList<>();
        }
    }
    
    /**
     * Save bookmarks to preferences
     */
    private void saveBookmarks() {
        String bookmarksJson = gson.toJson(bookmarks);
        preferences.edit().putString(PREF_BOOKMARKS, bookmarksJson).apply();
    }
    
    /**
     * Add a bookmark
     */
    public void addBookmark(String filePath, int pageNumber, String title) {
        if (filePath == null || title == null) return;
        
        // Check if bookmark already exists
        Bookmark existing = getBookmark(filePath, pageNumber);
        if (existing != null) {
            // Update existing bookmark
            existing.setTitle(title);
            existing.setCreatedDate(new Date());
        } else {
            // Create new bookmark
            Bookmark bookmark = new Bookmark();
            bookmark.setFilePath(filePath);
            bookmark.setPageNumber(pageNumber);
            bookmark.setTitle(title);
            bookmark.setCreatedDate(new Date());
            bookmarks.add(bookmark);
        }
        
        saveBookmarks();
    }
    
    /**
     * Remove a bookmark
     */
    public void removeBookmark(String filePath, int pageNumber) {
        bookmarks.removeIf(bookmark -> 
            bookmark.getFilePath().equals(filePath) && 
            bookmark.getPageNumber() == pageNumber);
        saveBookmarks();
    }
    
    /**
     * Get bookmark for specific file and page
     */
    public Bookmark getBookmark(String filePath, int pageNumber) {
        return bookmarks.stream()
                .filter(bookmark -> 
                    bookmark.getFilePath().equals(filePath) && 
                    bookmark.getPageNumber() == pageNumber)
                .findFirst()
                .orElse(null);
    }
    
    /**
     * Get all bookmarks for a file
     */
    public List<Bookmark> getBookmarksForFile(String filePath) {
        List<Bookmark> fileBookmarks = new ArrayList<>();
        for (Bookmark bookmark : bookmarks) {
            if (bookmark.getFilePath().equals(filePath)) {
                fileBookmarks.add(bookmark);
            }
        }
        return fileBookmarks;
    }
    
    /**
     * Get all bookmarks
     */
    public List<Bookmark> getAllBookmarks() {
        return new ArrayList<>(bookmarks);
    }
    
    /**
     * Check if page is bookmarked
     */
    public boolean isBookmarked(String filePath, int pageNumber) {
        return getBookmark(filePath, pageNumber) != null;
    }
    
    /**
     * Clear all bookmarks
     */
    public void clearAllBookmarks() {
        bookmarks.clear();
        saveBookmarks();
    }
    
    /**
     * Clear bookmarks for specific file
     */
    public void clearBookmarksForFile(String filePath) {
        bookmarks.removeIf(bookmark -> bookmark.getFilePath().equals(filePath));
        saveBookmarks();
    }
    
    /**
     * Get bookmarks count
     */
    public int getBookmarksCount() {
        return bookmarks.size();
    }
    
    /**
     * Get bookmarks count for file
     */
    public int getBookmarksCountForFile(String filePath) {
        return (int) bookmarks.stream()
                .filter(bookmark -> bookmark.getFilePath().equals(filePath))
                .count();
    }
}
