package com.ccppdfreader.app.interfaces;

import android.net.Uri;
import com.ccppdfreader.app.models.PDFDocument;
import java.util.List;

/**
 * Contract interface for Main Activity MVP pattern
 */
public interface MainContract {
    
    interface View extends BaseView {
        void showRecentDocuments(List<PDFDocument> documents);
        void openPDFReader(PDFDocument document);
        void openPDFReader(Uri uri);
        void refreshDocumentList();
    }
    
    interface Presenter extends BasePresenter<View> {
        void loadRecentDocuments();
        void loadFavoriteDocuments();
        void openDocument(PDFDocument document);
        void openDocument(Uri uri);
        void toggleFavorite(PDFDocument document);
        void shareDocument(PDFDocument document);
        void removeFromRecent(PDFDocument document);
    }
}
