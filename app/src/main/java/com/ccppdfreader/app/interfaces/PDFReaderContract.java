package com.ccppdfreader.app.interfaces;

import android.net.Uri;
import com.ccppdfreader.app.models.PDFDocument;

/**
 * Contract interface for PDF Reader Activity MVP pattern
 */
public interface PDFReaderContract {
    
    interface View extends BaseView {
        void displayPDF(PDFDocument document);
        void displayPDF(Uri uri);
    }
    
    interface Presenter extends BasePresenter<View> {
        void loadDocument(PDFDocument document);
        void loadDocument(Uri uri);
        void updateReadingProgress(int currentPage, int totalPages);
        void toggleBookmark();
        void shareDocument();
        void searchText(String query);
    }
}
