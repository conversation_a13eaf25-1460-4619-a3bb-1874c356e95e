package com.ccppdfreader.app.interfaces;

/**
 * Base interface for all View components in MVP pattern
 */
public interface BaseView {
    
    /**
     * Show loading indicator
     */
    void showLoading();
    
    /**
     * Hide loading indicator
     */
    void hideLoading();
    
    /**
     * Show error message to user
     * @param message Error message to display
     */
    void showError(String message);
    
    /**
     * Show success message to user
     * @param message Success message to display
     */
    void showSuccess(String message);
    
    /**
     * Show a generic message to user
     * @param message Message to display
     */
    void showMessage(String message);
    
    /**
     * Check if view is active/attached
     * @return true if view is active
     */
    boolean isActive();
}
