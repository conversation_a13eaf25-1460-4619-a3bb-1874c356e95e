package com.ccppdfreader.app.interfaces;

/**
 * Base interface for all Presenter components in MVP pattern
 */
public interface BasePresenter<V extends BaseView> {
    
    /**
     * Attach view to presenter
     * @param view View to attach
     */
    void attachView(V view);
    
    /**
     * Detach view from presenter
     */
    void detachView();
    
    /**
     * Check if view is attached
     * @return true if view is attached
     */
    boolean isViewAttached();
    
    /**
     * Get attached view
     * @return attached view or null
     */
    V getView();
    
    /**
     * Called when presenter should start its work
     */
    void start();
    
    /**
     * Called when presenter should stop its work
     */
    void stop();
}
