package com.ccppdfreader.app.interfaces;

import com.ccppdfreader.app.models.FileItem;
import java.util.List;

/**
 * Contract interface for File Explorer Activity MVP pattern
 */
public interface FileExplorerContract {
    
    interface View extends BaseView {
        void showFiles(List<FileItem> files);
        void updateCurrentPath(String path);
        void openPDFReader(FileItem fileItem);
    }
    
    interface Presenter extends BasePresenter<View> {
        void loadInitialDirectory();
        void navigateToFolder(String folderPath);
        void navigateToHome();
        boolean navigateBack();
        void refreshCurrentDirectory();
        void openPDFFile(FileItem fileItem);
        void sortByName();
        void sortByDate();
        void sortBySize();
        void sortByType();
    }
}
