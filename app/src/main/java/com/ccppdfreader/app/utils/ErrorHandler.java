package com.ccppdfreader.app.utils;

import android.content.Context;
import android.view.View;
import androidx.annotation.StringRes;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.snackbar.Snackbar;
import com.ccppdfreader.app.R;

/**
 * Utility class for elegant error handling and user feedback
 */
public class ErrorHandler {
    
    /**
     * Error types for different handling
     */
    public enum ErrorType {
        NETWORK,
        FILE_ACCESS,
        PERMISSION,
        VALIDATION,
        GENERIC
    }
    
    /**
     * Show error dialog with appropriate message and actions
     */
    public static void showErrorDialog(Context context, ErrorType errorType, String message) {
        showErrorDialog(context, errorType, message, null);
    }
    
    public static void showErrorDialog(Context context, ErrorType errorType, String message, Runnable onRetry) {
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(context)
                .setTitle(getErrorTitle(context, errorType))
                .setMessage(message)
                .setPositiveButton(R.string.ok, null);
        
        // Add retry button if callback provided
        if (onRetry != null) {
            builder.setNegativeButton(R.string.retry, (dialog, which) -> onRetry.run());
        }
        
        // Set appropriate icon based on error type
        switch (errorType) {
            case NETWORK:
                builder.setIcon(R.drawable.ic_wifi_off);
                break;
            case FILE_ACCESS:
                builder.setIcon(R.drawable.ic_folder_empty);
                break;
            case PERMISSION:
                builder.setIcon(R.drawable.ic_security);
                break;
            default:
                builder.setIcon(R.drawable.ic_error);
                break;
        }
        
        builder.show();
    }
    
    /**
     * Show error snackbar with action
     */
    public static void showErrorSnackbar(View view, String message) {
        showErrorSnackbar(view, message, null, null);
    }
    
    public static void showErrorSnackbar(View view, String message, String actionText, Runnable action) {
        Snackbar snackbar = Snackbar.make(view, message, Snackbar.LENGTH_LONG);
        
        if (actionText != null && action != null) {
            snackbar.setAction(actionText, v -> action.run());
        }
        
        // Customize appearance
        snackbar.setBackgroundTint(view.getContext().getColor(R.color.md_theme_light_errorContainer));
        snackbar.setTextColor(view.getContext().getColor(R.color.md_theme_light_onErrorContainer));
        snackbar.setActionTextColor(view.getContext().getColor(R.color.md_theme_light_error));
        
        snackbar.show();
    }
    
    /**
     * Show success snackbar
     */
    public static void showSuccessSnackbar(View view, String message) {
        Snackbar snackbar = Snackbar.make(view, message, Snackbar.LENGTH_SHORT);
        
        // Customize appearance for success
        snackbar.setBackgroundTint(view.getContext().getColor(R.color.md_theme_light_primaryContainer));
        snackbar.setTextColor(view.getContext().getColor(R.color.md_theme_light_onPrimaryContainer));
        
        snackbar.show();
    }
    
    /**
     * Show info snackbar
     */
    public static void showInfoSnackbar(View view, String message) {
        Snackbar snackbar = Snackbar.make(view, message, Snackbar.LENGTH_SHORT);
        
        // Customize appearance for info
        snackbar.setBackgroundTint(view.getContext().getColor(R.color.md_theme_light_secondaryContainer));
        snackbar.setTextColor(view.getContext().getColor(R.color.md_theme_light_onSecondaryContainer));
        
        snackbar.show();
    }
    
    /**
     * Get appropriate error title based on error type
     */
    private static String getErrorTitle(Context context, ErrorType errorType) {
        switch (errorType) {
            case NETWORK:
                return "Network Error";
            case FILE_ACCESS:
                return "File Access Error";
            case PERMISSION:
                return "Permission Required";
            case VALIDATION:
                return "Invalid Input";
            default:
                return context.getString(R.string.error);
        }
    }
    
    /**
     * Get user-friendly error message
     */
    public static String getUserFriendlyMessage(Context context, Throwable throwable) {
        if (throwable == null) {
            return context.getString(R.string.error_generic);
        }
        
        String message = throwable.getMessage();
        if (message == null || message.isEmpty()) {
            return context.getString(R.string.error_generic);
        }
        
        // Convert technical errors to user-friendly messages
        if (message.contains("FileNotFoundException")) {
            return context.getString(R.string.error_file_access);
        } else if (message.contains("SecurityException") || message.contains("Permission")) {
            return context.getString(R.string.error_permission_denied);
        } else if (message.contains("NetworkException") || message.contains("UnknownHostException")) {
            return context.getString(R.string.error_network);
        }
        
        return message;
    }
    
    /**
     * Handle exception with appropriate UI feedback
     */
    public static void handleException(Context context, View view, Throwable throwable) {
        handleException(context, view, throwable, null);
    }
    
    public static void handleException(Context context, View view, Throwable throwable, Runnable onRetry) {
        String message = getUserFriendlyMessage(context, throwable);
        ErrorType errorType = getErrorTypeFromException(throwable);
        
        if (view != null) {
            // Show snackbar for less critical errors
            if (errorType == ErrorType.VALIDATION || errorType == ErrorType.GENERIC) {
                showErrorSnackbar(view, message, onRetry != null ? "Retry" : null, onRetry);
            } else {
                // Show dialog for more serious errors
                showErrorDialog(context, errorType, message, onRetry);
            }
        } else {
            // Fallback to dialog if no view available
            showErrorDialog(context, errorType, message, onRetry);
        }
    }
    
    /**
     * Determine error type from exception
     */
    private static ErrorType getErrorTypeFromException(Throwable throwable) {
        if (throwable == null) {
            return ErrorType.GENERIC;
        }
        
        String message = throwable.getMessage();
        if (message == null) {
            return ErrorType.GENERIC;
        }
        
        if (message.contains("FileNotFoundException") || message.contains("IOException")) {
            return ErrorType.FILE_ACCESS;
        } else if (message.contains("SecurityException") || message.contains("Permission")) {
            return ErrorType.PERMISSION;
        } else if (message.contains("NetworkException") || message.contains("UnknownHostException")) {
            return ErrorType.NETWORK;
        }
        
        return ErrorType.GENERIC;
    }
}
