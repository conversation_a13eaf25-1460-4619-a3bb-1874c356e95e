package com.ccppdfreader.app.utils;

import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.provider.OpenableColumns;
import android.webkit.MimeTypeMap;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Utility class for file operations
 */
public class FileUtils {
    
    private static final String TAG = "FileUtils";
    
    /**
     * Check if file is a PDF
     */
    public static boolean isPDFFile(String filePath) {
        if (filePath == null) return false;
        return filePath.toLowerCase().endsWith(".pdf");
    }
    
    /**
     * Check if file exists and is readable
     */
    public static boolean isFileReadable(String filePath) {
        if (filePath == null) return false;
        File file = new File(filePath);
        return file.exists() && file.canRead() && file.isFile();
    }
    
    /**
     * Get file name from path
     */
    public static String getFileName(String filePath) {
        if (filePath == null) return "";
        File file = new File(filePath);
        return file.getName();
    }
    
    /**
     * Get file name without extension
     */
    public static String getFileNameWithoutExtension(String filePath) {
        String fileName = getFileName(filePath);
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        }
        return fileName;
    }
    
    /**
     * Get file extension
     */
    public static String getFileExtension(String filePath) {
        if (filePath == null) return "";
        int lastDotIndex = filePath.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filePath.length() - 1) {
            return filePath.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }
    
    /**
     * Get file size
     */
    public static long getFileSize(String filePath) {
        if (filePath == null) return 0;
        File file = new File(filePath);
        return file.exists() ? file.length() : 0;
    }
    
    /**
     * Get file last modified date
     */
    public static Date getFileLastModified(String filePath) {
        if (filePath == null) return null;
        File file = new File(filePath);
        return file.exists() ? new Date(file.lastModified()) : null;
    }
    
    /**
     * Format file size to human readable string
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format(Locale.getDefault(), "%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format(Locale.getDefault(), "%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format(Locale.getDefault(), "%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * Format date to readable string
     */
    public static String formatDate(Date date) {
        if (date == null) return "";
        SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());
        return sdf.format(date);
    }
    
    /**
     * Format date and time to readable string
     */
    public static String formatDateTime(Date date) {
        if (date == null) return "";
        SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault());
        return sdf.format(date);
    }
    
    /**
     * Get MIME type from file extension
     */
    public static String getMimeType(String filePath) {
        String extension = getFileExtension(filePath);
        if (extension.isEmpty()) return null;
        
        MimeTypeMap mime = MimeTypeMap.getSingleton();
        return mime.getMimeTypeFromExtension(extension);
    }
    
    /**
     * Get file name from URI
     */
    public static String getFileNameFromUri(Context context, Uri uri) {
        String result = null;
        if (uri.getScheme().equals("content")) {
            try (Cursor cursor = context.getContentResolver().query(uri, null, null, null, null)) {
                if (cursor != null && cursor.moveToFirst()) {
                    int nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME);
                    if (nameIndex >= 0) {
                        result = cursor.getString(nameIndex);
                    }
                }
            } catch (Exception e) {
                // Handle exception
            }
        }
        if (result == null) {
            result = uri.getPath();
            int cut = result.lastIndexOf('/');
            if (cut != -1) {
                result = result.substring(cut + 1);
            }
        }
        return result;
    }
    
    /**
     * Create directory if it doesn't exist
     */
    public static boolean createDirectoryIfNotExists(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists()) {
            return dir.mkdirs();
        }
        return true;
    }
    
    /**
     * Delete file safely
     */
    public static boolean deleteFile(String filePath) {
        if (filePath == null) return false;
        File file = new File(filePath);
        return file.exists() && file.delete();
    }
}
