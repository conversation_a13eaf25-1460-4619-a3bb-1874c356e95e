package com.ccppdfreader.app.utils;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.animation.DecelerateInterpolator;
import androidx.interpolator.view.animation.FastOutSlowInInterpolator;
import com.ccppdfreader.app.R;

/**
 * Utility class for smooth animations and transitions
 */
public class AnimationUtils {
    
    private static final int ANIMATION_DURATION_SHORT = 200;
    private static final int ANIMATION_DURATION_MEDIUM = 300;
    private static final int ANIMATION_DURATION_LONG = 500;
    
    /**
     * Fade in animation
     */
    public static void fadeIn(View view) {
        fadeIn(view, ANIMATION_DURATION_MEDIUM, null);
    }
    
    public static void fadeIn(View view, int duration, Runnable onComplete) {
        if (view.getVisibility() == View.VISIBLE && view.getAlpha() == 1.0f) {
            if (onComplete != null) onComplete.run();
            return;
        }
        
        view.setAlpha(0f);
        view.setVisibility(View.VISIBLE);
        
        ObjectAnimator animator = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f);
        animator.setDuration(duration);
        animator.setInterpolator(new DecelerateInterpolator());
        
        if (onComplete != null) {
            animator.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    onComplete.run();
                }
            });
        }
        
        animator.start();
    }
    
    /**
     * Fade out animation
     */
    public static void fadeOut(View view) {
        fadeOut(view, ANIMATION_DURATION_MEDIUM, null);
    }
    
    public static void fadeOut(View view, int duration, Runnable onComplete) {
        if (view.getVisibility() == View.GONE || view.getAlpha() == 0f) {
            if (onComplete != null) onComplete.run();
            return;
        }
        
        ObjectAnimator animator = ObjectAnimator.ofFloat(view, "alpha", view.getAlpha(), 0f);
        animator.setDuration(duration);
        animator.setInterpolator(new AccelerateDecelerateInterpolator());
        
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                view.setVisibility(View.GONE);
                if (onComplete != null) onComplete.run();
            }
        });
        
        animator.start();
    }
    
    /**
     * Slide up animation
     */
    public static void slideUp(View view) {
        slideUp(view, ANIMATION_DURATION_MEDIUM, null);
    }
    
    public static void slideUp(View view, int duration, Runnable onComplete) {
        if (view.getVisibility() == View.VISIBLE && view.getTranslationY() == 0) {
            if (onComplete != null) onComplete.run();
            return;
        }
        
        view.setTranslationY(view.getHeight());
        view.setVisibility(View.VISIBLE);
        
        ObjectAnimator animator = ObjectAnimator.ofFloat(view, "translationY", view.getHeight(), 0f);
        animator.setDuration(duration);
        animator.setInterpolator(new FastOutSlowInInterpolator());
        
        if (onComplete != null) {
            animator.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    onComplete.run();
                }
            });
        }
        
        animator.start();
    }
    
    /**
     * Slide down animation
     */
    public static void slideDown(View view) {
        slideDown(view, ANIMATION_DURATION_MEDIUM, null);
    }
    
    public static void slideDown(View view, int duration, Runnable onComplete) {
        if (view.getVisibility() == View.GONE) {
            if (onComplete != null) onComplete.run();
            return;
        }
        
        ObjectAnimator animator = ObjectAnimator.ofFloat(view, "translationY", 0f, view.getHeight());
        animator.setDuration(duration);
        animator.setInterpolator(new FastOutSlowInInterpolator());
        
        animator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                view.setVisibility(View.GONE);
                view.setTranslationY(0f);
                if (onComplete != null) onComplete.run();
            }
        });
        
        animator.start();
    }
    
    /**
     * Scale animation
     */
    public static void scaleIn(View view) {
        scaleIn(view, ANIMATION_DURATION_MEDIUM, null);
    }
    
    public static void scaleIn(View view, int duration, Runnable onComplete) {
        view.setScaleX(0.8f);
        view.setScaleY(0.8f);
        view.setAlpha(0f);
        view.setVisibility(View.VISIBLE);
        
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(view, "scaleX", 0.8f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(view, "scaleY", 0.8f, 1f);
        ObjectAnimator alpha = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f);
        
        scaleX.setDuration(duration);
        scaleY.setDuration(duration);
        alpha.setDuration(duration);
        
        scaleX.setInterpolator(new FastOutSlowInInterpolator());
        scaleY.setInterpolator(new FastOutSlowInInterpolator());
        alpha.setInterpolator(new DecelerateInterpolator());
        
        if (onComplete != null) {
            alpha.addListener(new AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(Animator animation) {
                    onComplete.run();
                }
            });
        }
        
        scaleX.start();
        scaleY.start();
        alpha.start();
    }
    
    /**
     * Pulse animation for feedback
     */
    public static void pulse(View view) {
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(view, "scaleX", 1f, 1.1f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(view, "scaleY", 1f, 1.1f, 1f);
        
        scaleX.setDuration(ANIMATION_DURATION_SHORT);
        scaleY.setDuration(ANIMATION_DURATION_SHORT);
        
        scaleX.setInterpolator(new AccelerateDecelerateInterpolator());
        scaleY.setInterpolator(new AccelerateDecelerateInterpolator());
        
        scaleX.start();
        scaleY.start();
    }
    
    /**
     * Shake animation for errors
     */
    public static void shake(View view) {
        ObjectAnimator animator = ObjectAnimator.ofFloat(view, "translationX", 0, 25, -25, 25, -25, 15, -15, 6, -6, 0);
        animator.setDuration(ANIMATION_DURATION_LONG);
        animator.start();
    }
    
    /**
     * Get animation from resources
     */
    public static Animation loadAnimation(Context context, int animationRes) {
        return android.view.animation.AnimationUtils.loadAnimation(context, animationRes);
    }
    
    /**
     * Smooth transition between activities
     */
    public static void applyActivityTransition(Context context) {
        if (context instanceof android.app.Activity) {
            ((android.app.Activity) context).overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
        }
    }
    
    /**
     * Smooth fade transition
     */
    public static void applyFadeTransition(Context context) {
        if (context instanceof android.app.Activity) {
            ((android.app.Activity) context).overridePendingTransition(R.anim.fade_in, R.anim.fade_out);
        }
    }
}
