package com.ccppdfreader.app;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import androidx.appcompat.app.AppCompatDelegate;

/**
 * Application class for CCP PDF Reader
 * Handles global app initialization and configuration
 */
public class CCPPDFReaderApplication extends Application {
    
    private static CCPPDFReaderApplication instance;
    private SharedPreferences sharedPreferences;
    
    public static final String PREFS_NAME = "CCPPDFReaderPrefs";
    public static final String PREF_THEME_MODE = "theme_mode";
    public static final String PREF_FIRST_LAUNCH = "first_launch";
    public static final String PREF_LAST_OPENED_FILE = "last_opened_file";
    
    @Override
    public void onCreate() {
        super.onCreate();
        instance = this;
        
        initializePreferences();
        initializeTheme();
    }
    
    private void initializePreferences() {
        sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    private void initializeTheme() {
        int themeMode = sharedPreferences.getInt(PREF_THEME_MODE, AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM);
        AppCompatDelegate.setDefaultNightMode(themeMode);
    }
    
    public static CCPPDFReaderApplication getInstance() {
        return instance;
    }
    
    public SharedPreferences getAppPreferences() {
        return sharedPreferences;
    }
    
    public void setThemeMode(int mode) {
        sharedPreferences.edit().putInt(PREF_THEME_MODE, mode).apply();
        AppCompatDelegate.setDefaultNightMode(mode);
    }
    
    public int getThemeMode() {
        return sharedPreferences.getInt(PREF_THEME_MODE, AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM);
    }
    
    public boolean isFirstLaunch() {
        return sharedPreferences.getBoolean(PREF_FIRST_LAUNCH, true);
    }
    
    public void setFirstLaunchCompleted() {
        sharedPreferences.edit().putBoolean(PREF_FIRST_LAUNCH, false).apply();
    }
}
