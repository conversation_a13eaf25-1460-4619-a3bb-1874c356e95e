package com.ccppdfreader.app.presenters;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import com.ccppdfreader.app.CCPPDFReaderApplication;
import com.ccppdfreader.app.interfaces.MainContract;
import com.ccppdfreader.app.managers.DocumentManager;
import com.ccppdfreader.app.models.PDFDocument;
import com.ccppdfreader.app.utils.FileUtils;
import java.util.List;

/**
 * Presenter for MainActivity
 * Handles business logic for main screen
 */
public class MainPresenter extends BasePresenterImpl<MainContract.View> implements MainContract.Presenter {
    
    private final DocumentManager documentManager;
    
    public MainPresenter() {
        this.documentManager = DocumentManager.getInstance();
    }
    
    @Override
    public void start() {
        super.start();
        loadRecentDocuments();
    }
    
    @Override
    public void loadRecentDocuments() {
        executeIfViewActive(() -> {
            List<PDFDocument> recentDocuments = documentManager.getRecentDocumentsSorted();
            getView().showRecentDocuments(recentDocuments);
        });
    }
    
    @Override
    public void loadFavoriteDocuments() {
        executeIfViewActive(() -> {
            List<PDFDocument> favoriteDocuments = documentManager.getFavoriteDocuments();
            getView().showRecentDocuments(favoriteDocuments);
        });
    }
    
    @Override
    public void openDocument(PDFDocument document) {
        if (document == null || !FileUtils.isFileReadable(document.getFilePath())) {
            executeIfViewActive(() -> getView().showError("File not found or cannot be read"));
            return;
        }
        
        // Add to recent documents
        documentManager.addToRecent(document);
        
        executeIfViewActive(() -> getView().openPDFReader(document));
    }
    
    @Override
    public void openDocument(Uri uri) {
        if (uri == null) {
            executeIfViewActive(() -> getView().showError("Invalid file selected"));
            return;
        }
        
        executeIfViewActive(() -> getView().openPDFReader(uri));
    }
    
    @Override
    public void toggleFavorite(PDFDocument document) {
        if (document == null) return;
        
        if (document.isFavorite()) {
            documentManager.removeFromFavorites(document.getFilePath());
            document.setFavorite(false);
            executeIfViewActive(() -> getView().showMessage("Removed from favorites"));
        } else {
            documentManager.addToFavorites(document);
            document.setFavorite(true);
            executeIfViewActive(() -> getView().showMessage("Added to favorites"));
        }
        
        executeIfViewActive(() -> getView().refreshDocumentList());
    }
    
    @Override
    public void shareDocument(PDFDocument document) {
        if (document == null || !FileUtils.isFileReadable(document.getFilePath())) {
            executeIfViewActive(() -> getView().showError("Cannot share this file"));
            return;
        }
        
        executeIfViewActive(() -> {
            Context context = CCPPDFReaderApplication.getInstance();
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("application/pdf");
            
            // Create URI for file sharing
            Uri fileUri = Uri.parse("file://" + document.getFilePath());
            shareIntent.putExtra(Intent.EXTRA_STREAM, fileUri);
            shareIntent.putExtra(Intent.EXTRA_SUBJECT, document.getDisplayName());
            shareIntent.putExtra(Intent.EXTRA_TEXT, "Sharing PDF: " + document.getDisplayName());
            
            Intent chooser = Intent.createChooser(shareIntent, "Share PDF");
            chooser.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(chooser);
        });
    }
    
    @Override
    public void removeFromRecent(PDFDocument document) {
        if (document == null) return;
        
        // Note: DocumentManager doesn't have a direct method to remove single item
        // This would need to be implemented in DocumentManager
        executeIfViewActive(() -> {
            getView().showMessage("Removed from recent files");
            loadRecentDocuments(); // Refresh the list
        });
    }
}
