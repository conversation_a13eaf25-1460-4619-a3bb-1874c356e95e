package com.ccppdfreader.app.presenters;

import com.ccppdfreader.app.interfaces.BasePresenter;
import com.ccppdfreader.app.interfaces.BaseView;

/**
 * Base implementation of Presenter in MVP pattern
 * Provides common functionality for all presenters
 */
public abstract class BasePresenterImpl<V extends BaseView> implements BasePresenter<V> {
    
    private V view;
    
    @Override
    public void attachView(V view) {
        this.view = view;
    }
    
    @Override
    public void detachView() {
        this.view = null;
    }
    
    @Override
    public boolean isViewAttached() {
        return view != null;
    }
    
    @Override
    public V getView() {
        return view;
    }
    
    /**
     * Check if view is attached and active before performing operations
     * @return true if view is safe to use
     */
    protected boolean isViewActive() {
        return isViewAttached() && view.isActive();
    }
    
    /**
     * Execute action only if view is active
     * @param action Action to execute
     */
    protected void executeIfViewActive(Runnable action) {
        if (isViewActive()) {
            action.run();
        }
    }
    
    @Override
    public void start() {
        // Override in subclasses if needed
    }
    
    @Override
    public void stop() {
        // Override in subclasses if needed
    }
}
