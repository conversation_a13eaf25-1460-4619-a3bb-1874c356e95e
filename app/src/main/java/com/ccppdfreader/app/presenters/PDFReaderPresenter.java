package com.ccppdfreader.app.presenters;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import androidx.core.content.FileProvider;
import com.ccppdfreader.app.CCPPDFReaderApplication;
import com.ccppdfreader.app.interfaces.PDFReaderContract;
import com.ccppdfreader.app.managers.DocumentManager;
import com.ccppdfreader.app.managers.BookmarkManager;
import com.ccppdfreader.app.models.PDFDocument;
import com.ccppdfreader.app.utils.FileUtils;
import java.io.File;

/**
 * Presenter for PDF Reader Activity
 * Handles business logic for PDF reading
 */
public class PDFReaderPresenter extends BasePresenterImpl<PDFReaderContract.View> implements PDFReaderContract.Presenter {
    
    private final DocumentManager documentManager;
    private final BookmarkManager bookmarkManager;
    private PDFDocument currentDocument;
    private Uri currentUri;
    private int currentPage = 1;
    
    public PDFReaderPresenter() {
        this.documentManager = DocumentManager.getInstance();
        this.bookmarkManager = BookmarkManager.getInstance();
    }
    
    @Override
    public void loadDocument(PDFDocument document) {
        if (document == null) {
            executeIfViewActive(() -> getView().showError("Invalid document"));
            return;
        }
        
        if (!FileUtils.isFileReadable(document.getFilePath())) {
            executeIfViewActive(() -> getView().showError("File not found or cannot be read"));
            return;
        }
        
        this.currentDocument = document;
        this.currentUri = null;
        
        // Add to recent documents
        documentManager.addToRecent(document);
        
        executeIfViewActive(() -> getView().displayPDF(document));
    }
    
    @Override
    public void loadDocument(Uri uri) {
        if (uri == null) {
            executeIfViewActive(() -> getView().showError("Invalid file URI"));
            return;
        }
        
        this.currentUri = uri;
        this.currentDocument = null;
        
        // Try to create a PDFDocument from URI for tracking
        Context context = CCPPDFReaderApplication.getInstance();
        String fileName = FileUtils.getFileNameFromUri(context, uri);
        if (fileName != null) {
            // Create a temporary document for tracking
            PDFDocument tempDocument = new PDFDocument();
            tempDocument.setFileName(fileName);
            tempDocument.setDisplayName(FileUtils.getFileNameWithoutExtension(fileName));
            tempDocument.setFilePath(uri.toString()); // Store URI as path for now
            
            documentManager.addToRecent(tempDocument);
        }
        
        executeIfViewActive(() -> getView().displayPDF(uri));
    }
    
    @Override
    public void updateReadingProgress(int currentPage, int totalPages) {
        this.currentPage = currentPage;

        if (currentDocument != null) {
            documentManager.updateReadingProgress(
                currentDocument.getFilePath(),
                currentPage,
                totalPages
            );

            // Update current document
            currentDocument.setLastPageRead(currentPage);
            currentDocument.setTotalPages(totalPages);
        }
    }
    
    @Override
    public void toggleBookmark() {
        if (currentDocument != null) {
            String filePath = currentDocument.getFilePath();

            if (bookmarkManager.isBookmarked(filePath, currentPage)) {
                // Remove bookmark
                bookmarkManager.removeBookmark(filePath, currentPage);
                executeIfViewActive(() -> getView().showMessage("Bookmark removed"));
            } else {
                // Add bookmark
                String title = "Page " + currentPage;
                bookmarkManager.addBookmark(filePath, currentPage, title);
                executeIfViewActive(() -> getView().showMessage("Bookmark added"));
            }
        } else {
            executeIfViewActive(() -> getView().showMessage("Cannot bookmark this document"));
        }
    }
    
    @Override
    public void shareDocument() {
        if (currentDocument != null && FileUtils.isFileReadable(currentDocument.getFilePath())) {
            shareFile(currentDocument.getFilePath(), currentDocument.getDisplayName());
        } else if (currentUri != null) {
            shareUri(currentUri);
        } else {
            executeIfViewActive(() -> getView().showError("Cannot share this document"));
        }
    }
    
    private void shareFile(String filePath, String displayName) {
        try {
            Context context = CCPPDFReaderApplication.getInstance();
            File file = new File(filePath);
            
            Uri fileUri = FileProvider.getUriForFile(
                context,
                context.getPackageName() + ".fileprovider",
                file
            );
            
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("application/pdf");
            shareIntent.putExtra(Intent.EXTRA_STREAM, fileUri);
            shareIntent.putExtra(Intent.EXTRA_SUBJECT, displayName);
            shareIntent.putExtra(Intent.EXTRA_TEXT, "Sharing PDF: " + displayName);
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            
            Intent chooser = Intent.createChooser(shareIntent, "Share PDF");
            chooser.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(chooser);
            
            executeIfViewActive(() -> getView().showMessage("Sharing document..."));
            
        } catch (Exception e) {
            executeIfViewActive(() -> getView().showError("Error sharing document: " + e.getMessage()));
        }
    }
    
    private void shareUri(Uri uri) {
        try {
            Context context = CCPPDFReaderApplication.getInstance();
            
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("application/pdf");
            shareIntent.putExtra(Intent.EXTRA_STREAM, uri);
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            
            Intent chooser = Intent.createChooser(shareIntent, "Share PDF");
            chooser.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(chooser);
            
            executeIfViewActive(() -> getView().showMessage("Sharing document..."));
            
        } catch (Exception e) {
            executeIfViewActive(() -> getView().showError("Error sharing document: " + e.getMessage()));
        }
    }
    
    @Override
    public void searchText(String query) {
        // TODO: Implement text search functionality
        // This would require additional PDF text extraction capabilities
        executeIfViewActive(() -> getView().showMessage("Search functionality coming soon!"));
    }
}
