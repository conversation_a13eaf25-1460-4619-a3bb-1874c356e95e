package com.ccppdfreader.app.presenters;

import android.os.Environment;
import com.ccppdfreader.app.interfaces.FileExplorerContract;
import com.ccppdfreader.app.managers.DocumentManager;
import com.ccppdfreader.app.models.FileItem;
import com.ccppdfreader.app.models.PDFDocument;
import com.ccppdfreader.app.utils.FileUtils;
import com.ccppdfreader.app.utils.PerformanceUtils;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Stack;

/**
 * Presenter for File Explorer Activity
 * Handles file system navigation and operations
 */
public class FileExplorerPresenter extends BasePresenterImpl<FileExplorerContract.View> implements FileExplorerContract.Presenter {
    
    private final DocumentManager documentManager;
    private final Stack<String> navigationStack;
    private String currentPath;
    private List<FileItem> currentFiles;
    
    public FileExplorerPresenter() {
        this.documentManager = DocumentManager.getInstance();
        this.navigationStack = new Stack<>();
        this.currentFiles = new ArrayList<>();
    }
    
    @Override
    public void loadInitialDirectory() {
        // Start from external storage or downloads folder
        String initialPath = getInitialDirectory();
        navigateToFolder(initialPath);
    }
    
    private String getInitialDirectory() {
        // Try to get Downloads folder first
        File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
        if (downloadsDir.exists() && downloadsDir.canRead()) {
            return downloadsDir.getAbsolutePath();
        }
        
        // Fallback to external storage root
        File externalStorage = Environment.getExternalStorageDirectory();
        if (externalStorage.exists() && externalStorage.canRead()) {
            return externalStorage.getAbsolutePath();
        }
        
        // Last resort - root directory
        return "/";
    }
    
    @Override
    public void navigateToFolder(String folderPath) {
        if (folderPath == null) return;

        executeIfViewActive(() -> getView().showLoading());

        // Load files in background thread with performance optimization
        PerformanceUtils.executeInBackground(() -> {
            try {
                long startTime = System.currentTimeMillis();
                List<FileItem> files = loadFilesFromDirectory(folderPath);
                long loadTime = System.currentTimeMillis() - startTime;

                PerformanceUtils.logPerformance("Load directory: " + folderPath, loadTime);

                PerformanceUtils.runOnMainThread(() -> {
                    if (isViewActive()) {
                        // Update navigation stack
                        if (currentPath != null && !currentPath.equals(folderPath)) {
                            navigationStack.push(currentPath);
                        }

                        currentPath = folderPath;
                        currentFiles = files;

                        getView().updateCurrentPath(currentPath);
                        getView().showFiles(files);
                    }
                });

            } catch (Exception e) {
                PerformanceUtils.runOnMainThread(() -> {
                    if (isViewActive()) {
                        getView().hideLoading();
                        getView().showError("Error loading folder: " + e.getMessage());
                    }
                });
            }
        });
    }
    
    private List<FileItem> loadFilesFromDirectory(String directoryPath) {
        List<FileItem> fileItems = new ArrayList<>();
        
        File directory = new File(directoryPath);
        if (!directory.exists() || !directory.isDirectory() || !directory.canRead()) {
            return fileItems;
        }
        
        File[] files = directory.listFiles();
        if (files == null) return fileItems;
        
        for (File file : files) {
            // Skip hidden files unless specifically showing them
            if (file.isHidden()) continue;
            
            FileItem fileItem = new FileItem(file);
            
            // Include folders and PDF files
            if (fileItem.isFolder() || fileItem.isPDF()) {
                fileItems.add(fileItem);
            }
        }
        
        // Sort by default (folders first, then by name)
        sortFilesByDefault(fileItems);
        
        return fileItems;
    }
    
    private void sortFilesByDefault(List<FileItem> files) {
        Collections.sort(files, (f1, f2) -> {
            // Folders first
            if (f1.isFolder() && !f2.isFolder()) return -1;
            if (!f1.isFolder() && f2.isFolder()) return 1;
            
            // Then by name
            return f1.getName().compareToIgnoreCase(f2.getName());
        });
    }
    
    @Override
    public void navigateToHome() {
        String homePath = getInitialDirectory();
        navigationStack.clear();
        navigateToFolder(homePath);
    }
    
    @Override
    public boolean navigateBack() {
        if (navigationStack.isEmpty()) {
            return false; // Can't go back further
        }
        
        String previousPath = navigationStack.pop();
        currentPath = previousPath;
        navigateToFolder(previousPath);
        return true;
    }
    
    @Override
    public void refreshCurrentDirectory() {
        if (currentPath != null) {
            navigateToFolder(currentPath);
        }
    }
    
    @Override
    public void openPDFFile(FileItem fileItem) {
        if (!fileItem.isPDF()) {
            executeIfViewActive(() -> getView().showError("Selected file is not a PDF"));
            return;
        }
        
        // Create PDFDocument and add to recent
        PDFDocument document = documentManager.createDocumentFromPath(fileItem.getPath());
        if (document != null) {
            documentManager.addToRecent(document);
        }
        
        executeIfViewActive(() -> getView().openPDFReader(fileItem));
    }
    
    @Override
    public void sortByName() {
        if (currentFiles.isEmpty()) return;
        
        Collections.sort(currentFiles, (f1, f2) -> {
            // Folders first
            if (f1.isFolder() && !f2.isFolder()) return -1;
            if (!f1.isFolder() && f2.isFolder()) return 1;
            
            return f1.getName().compareToIgnoreCase(f2.getName());
        });
        
        executeIfViewActive(() -> getView().showFiles(currentFiles));
    }
    
    @Override
    public void sortByDate() {
        if (currentFiles.isEmpty()) return;
        
        Collections.sort(currentFiles, (f1, f2) -> {
            // Folders first
            if (f1.isFolder() && !f2.isFolder()) return -1;
            if (!f1.isFolder() && f2.isFolder()) return 1;
            
            // Then by date (newest first)
            if (f1.getLastModified() == null && f2.getLastModified() == null) return 0;
            if (f1.getLastModified() == null) return 1;
            if (f2.getLastModified() == null) return -1;
            return f2.getLastModified().compareTo(f1.getLastModified());
        });
        
        executeIfViewActive(() -> getView().showFiles(currentFiles));
    }
    
    @Override
    public void sortBySize() {
        if (currentFiles.isEmpty()) return;
        
        Collections.sort(currentFiles, (f1, f2) -> {
            // Folders first
            if (f1.isFolder() && !f2.isFolder()) return -1;
            if (!f1.isFolder() && f2.isFolder()) return 1;
            
            // Then by size (largest first)
            return Long.compare(f2.getSize(), f1.getSize());
        });
        
        executeIfViewActive(() -> getView().showFiles(currentFiles));
    }
    
    @Override
    public void sortByType() {
        if (currentFiles.isEmpty()) return;
        
        Collections.sort(currentFiles, (f1, f2) -> {
            // Folders first
            if (f1.isFolder() && !f2.isFolder()) return -1;
            if (!f1.isFolder() && f2.isFolder()) return 1;
            
            // Then by type and name
            int typeCompare = f1.getType().compareTo(f2.getType());
            if (typeCompare != 0) return typeCompare;
            
            return f1.getName().compareToIgnoreCase(f2.getName());
        });
        
        executeIfViewActive(() -> getView().showFiles(currentFiles));
    }
}
