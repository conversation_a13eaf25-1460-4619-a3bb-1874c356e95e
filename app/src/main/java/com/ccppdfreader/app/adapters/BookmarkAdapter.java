package com.ccppdfreader.app.adapters;

import android.view.LayoutInflater;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.ccppdfreader.app.databinding.ItemBookmarkBinding;
import com.ccppdfreader.app.models.Bookmark;
import java.util.ArrayList;
import java.util.List;

/**
 * RecyclerView adapter for bookmarks
 */
public class BookmarkAdapter extends RecyclerView.Adapter<BookmarkAdapter.BookmarkViewHolder> {
    
    private List<Bookmark> bookmarks = new ArrayList<>();
    private final OnBookmarkClickListener clickListener;
    private final OnBookmarkLongClickListener longClickListener;
    
    public interface OnBookmarkClickListener {
        void onBookmarkClick(Bookmark bookmark);
    }
    
    public interface OnBookmarkLongClickListener {
        void onBookmarkLongClick(Bookmark bookmark);
    }
    
    public BookmarkAdapter(OnBookmarkClickListener clickListener, 
                          OnBookmarkLongClickListener longClickListener) {
        this.clickListener = clickListener;
        this.longClickListener = longClickListener;
    }
    
    @NonNull
    @Override
    public BookmarkViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ItemBookmarkBinding binding = ItemBookmarkBinding.inflate(
            LayoutInflater.from(parent.getContext()), parent, false);
        return new BookmarkViewHolder(binding);
    }
    
    @Override
    public void onBindViewHolder(@NonNull BookmarkViewHolder holder, int position) {
        Bookmark bookmark = bookmarks.get(position);
        holder.bind(bookmark);
    }
    
    @Override
    public int getItemCount() {
        return bookmarks.size();
    }
    
    public void updateBookmarks(List<Bookmark> newBookmarks) {
        this.bookmarks.clear();
        if (newBookmarks != null) {
            this.bookmarks.addAll(newBookmarks);
        }
        notifyDataSetChanged();
    }
    
    class BookmarkViewHolder extends RecyclerView.ViewHolder {
        private final ItemBookmarkBinding binding;
        
        public BookmarkViewHolder(@NonNull ItemBookmarkBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
            
            // Set click listeners
            binding.getRoot().setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && clickListener != null) {
                    clickListener.onBookmarkClick(bookmarks.get(position));
                }
            });
            
            binding.getRoot().setOnLongClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && longClickListener != null) {
                    longClickListener.onBookmarkLongClick(bookmarks.get(position));
                    return true;
                }
                return false;
            });
            
            binding.btnMoreOptions.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && longClickListener != null) {
                    longClickListener.onBookmarkLongClick(bookmarks.get(position));
                }
            });
        }
        
        public void bind(Bookmark bookmark) {
            // Set bookmark title
            binding.tvBookmarkTitle.setText(bookmark.getDisplayTitle());
            
            // Set file name
            binding.tvFileName.setText(bookmark.getFileName());
            
            // Set bookmark info (page and date)
            StringBuilder infoBuilder = new StringBuilder();
            infoBuilder.append("Page ").append(bookmark.getPageNumber());
            
            if (bookmark.getCreatedDate() != null) {
                infoBuilder.append(" • ").append(bookmark.getFormattedDate());
            }
            
            binding.tvBookmarkInfo.setText(infoBuilder.toString());
        }
    }
}
