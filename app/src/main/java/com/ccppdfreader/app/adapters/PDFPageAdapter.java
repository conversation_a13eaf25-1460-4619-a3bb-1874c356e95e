package com.ccppdfreader.app.adapters;

import android.content.Context;
import android.graphics.Bitmap;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.ccppdfreader.app.R;
import java.util.ArrayList;
import java.util.List;

public class PDFPageAdapter extends RecyclerView.Adapter<PDFPageAdapter.PageViewHolder> {

    private Context context;
    private List<Bitmap> pages;

    public PDFPageAdapter(Context context) {
        this.context = context;
        this.pages = new ArrayList<>();
    }

    public void setPages(List<Bitmap> pages) {
        this.pages = pages != null ? pages : new ArrayList<>();
        notifyDataSetChanged();
    }

    public void addPage(Bitmap page) {
        if (page != null) {
            pages.add(page);
            notifyItemInserted(pages.size() - 1);
        }
    }

    @NonNull
    @Override
    public PageViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_pdf_page, parent, false);
        return new PageViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull PageViewHolder holder, int position) {
        Bitmap page = pages.get(position);
        holder.bind(page, position + 1);
    }

    @Override
    public int getItemCount() {
        return pages.size();
    }

    public static class PageViewHolder extends RecyclerView.ViewHolder {
        private ImageView imageViewPage;
        private TextView textViewPageNumber;

        public PageViewHolder(@NonNull View itemView) {
            super(itemView);
            imageViewPage = itemView.findViewById(R.id.imageViewPage);
            textViewPageNumber = itemView.findViewById(R.id.textViewPageNumber);
        }

        public void bind(Bitmap page, int pageNumber) {
            imageViewPage.setImageBitmap(page);
            textViewPageNumber.setText("Page " + pageNumber);
        }
    }

    public void clearPages() {
        pages.clear();
        notifyDataSetChanged();
    }

    public boolean isEmpty() {
        return pages.isEmpty();
    }

    public int getPageCount() {
        return pages.size();
    }
}
