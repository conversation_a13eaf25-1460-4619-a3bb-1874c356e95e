package com.ccppdfreader.app.adapters;

import android.view.LayoutInflater;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.ccppdfreader.app.R;
import com.ccppdfreader.app.databinding.ItemFileBinding;
import com.ccppdfreader.app.models.FileItem;
import java.util.ArrayList;
import java.util.List;

/**
 * RecyclerView adapter for file explorer
 */
public class FileExplorerAdapter extends RecyclerView.Adapter<FileExplorerAdapter.FileViewHolder> {
    
    private List<FileItem> files = new ArrayList<>();
    private final OnFileClickListener fileClickListener;
    private final OnFolderClickListener folderClickListener;
    
    public interface OnFileClickListener {
        void onFileClick(FileItem fileItem);
    }
    
    public interface OnFolderClickListener {
        void onFolderClick(FileItem fileItem);
    }
    
    public FileExplorerAdapter(OnFileClickListener fileClickListener, 
                              OnFolderClickListener folderClickListener) {
        this.fileClickListener = fileClickListener;
        this.folderClickListener = folderClickListener;
    }
    
    @NonNull
    @Override
    public FileViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ItemFileBinding binding = ItemFileBinding.inflate(
            LayoutInflater.from(parent.getContext()), parent, false);
        return new FileViewHolder(binding);
    }
    
    @Override
    public void onBindViewHolder(@NonNull FileViewHolder holder, int position) {
        FileItem fileItem = files.get(position);
        holder.bind(fileItem);
    }
    
    @Override
    public int getItemCount() {
        return files.size();
    }
    
    public void updateFiles(List<FileItem> newFiles) {
        this.files.clear();
        if (newFiles != null) {
            this.files.addAll(newFiles);
        }
        notifyDataSetChanged();
    }
    
    class FileViewHolder extends RecyclerView.ViewHolder {
        private final ItemFileBinding binding;
        
        public FileViewHolder(@NonNull ItemFileBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
            
            // Set click listeners
            binding.getRoot().setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION) {
                    FileItem fileItem = files.get(position);
                    if (fileItem.isFolder()) {
                        if (folderClickListener != null) {
                            folderClickListener.onFolderClick(fileItem);
                        }
                    } else {
                        if (fileClickListener != null) {
                            fileClickListener.onFileClick(fileItem);
                        }
                    }
                }
            });
            
            binding.btnMoreOptions.setOnClickListener(v -> {
                // TODO: Implement context menu for files
            });
        }
        
        public void bind(FileItem fileItem) {
            // Set file name
            binding.tvFileName.setText(fileItem.getDisplayName());
            
            // Set file details
            StringBuilder details = new StringBuilder();
            details.append(fileItem.getFormattedSize());
            
            if (fileItem.getLastModified() != null) {
                details.append(" • ").append(fileItem.getFormattedDate());
            }
            
            binding.tvFileDetails.setText(details.toString());
            
            // Set appropriate icon
            int iconRes;
            switch (fileItem.getType()) {
                case FOLDER:
                    iconRes = R.drawable.ic_folder;
                    break;
                case PDF:
                    iconRes = R.drawable.ic_pdf_document;
                    break;
                default:
                    iconRes = R.drawable.ic_document_empty;
                    break;
            }
            
            binding.ivFileIcon.setImageResource(iconRes);
        }
    }
}
