package com.ccppdfreader.app.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.ccppdfreader.app.databinding.ItemPdfDocumentBinding;
import com.ccppdfreader.app.models.PDFDocument;
import com.ccppdfreader.app.utils.FileUtils;
import java.util.ArrayList;
import java.util.List;

/**
 * RecyclerView adapter for PDF documents
 */
public class PDFDocumentAdapter extends RecyclerView.Adapter<PDFDocumentAdapter.DocumentViewHolder> {
    
    private List<PDFDocument> documents = new ArrayList<>();
    private final OnDocumentClickListener clickListener;
    private final OnDocumentLongClickListener longClickListener;
    
    public interface OnDocumentClickListener {
        void onDocumentClick(PDFDocument document);
    }
    
    public interface OnDocumentLongClickListener {
        void onDocumentLongClick(PDFDocument document);
    }
    
    public PDFDocumentAdapter(OnDocumentClickListener clickListener, 
                             OnDocumentLongClickListener longClickListener) {
        this.clickListener = clickListener;
        this.longClickListener = longClickListener;
    }
    
    @NonNull
    @Override
    public DocumentViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ItemPdfDocumentBinding binding = ItemPdfDocumentBinding.inflate(
            LayoutInflater.from(parent.getContext()), parent, false);
        return new DocumentViewHolder(binding);
    }
    
    @Override
    public void onBindViewHolder(@NonNull DocumentViewHolder holder, int position) {
        PDFDocument document = documents.get(position);
        holder.bind(document);
    }
    
    @Override
    public int getItemCount() {
        return documents.size();
    }
    
    public void updateDocuments(List<PDFDocument> newDocuments) {
        this.documents.clear();
        if (newDocuments != null) {
            this.documents.addAll(newDocuments);
        }
        notifyDataSetChanged();
    }
    
    class DocumentViewHolder extends RecyclerView.ViewHolder {
        private final ItemPdfDocumentBinding binding;
        
        public DocumentViewHolder(@NonNull ItemPdfDocumentBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
            
            // Set click listeners
            binding.getRoot().setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && clickListener != null) {
                    clickListener.onDocumentClick(documents.get(position));
                }
            });
            
            binding.getRoot().setOnLongClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && longClickListener != null) {
                    longClickListener.onDocumentLongClick(documents.get(position));
                    return true;
                }
                return false;
            });
            
            binding.btnMoreOptions.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && longClickListener != null) {
                    longClickListener.onDocumentLongClick(documents.get(position));
                }
            });
        }
        
        public void bind(PDFDocument document) {
            // Set document title
            binding.tvDocumentTitle.setText(document.getDisplayName());
            
            // Set document info (size, pages, date)
            StringBuilder infoBuilder = new StringBuilder();
            infoBuilder.append(document.getFormattedFileSize());
            
            if (document.getTotalPages() > 0) {
                infoBuilder.append(" • ").append(document.getTotalPages()).append(" pages");
            }
            
            if (document.getLastModified() != null) {
                infoBuilder.append(" • ").append(FileUtils.formatDate(document.getLastModified()));
            }
            
            binding.tvDocumentInfo.setText(infoBuilder.toString());
            
            // Set favorite icon visibility
            binding.ivFavorite.setVisibility(document.isFavorite() ? View.VISIBLE : View.GONE);
            
            // Set reading progress
            int progress = document.getReadingProgress();
            if (progress > 0) {
                binding.progressReading.setVisibility(View.VISIBLE);
                binding.tvProgress.setVisibility(View.VISIBLE);
                binding.progressReading.setProgress(progress);
                
                String progressText = String.format("Page %d of %d (%d%%)", 
                    document.getLastPageRead(), 
                    document.getTotalPages(), 
                    progress);
                binding.tvProgress.setText(progressText);
            } else {
                binding.progressReading.setVisibility(View.GONE);
                binding.tvProgress.setVisibility(View.GONE);
            }
        }
    }
}
