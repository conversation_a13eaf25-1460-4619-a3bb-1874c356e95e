package com.ccppdfreader.app.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.ccppdfreader.app.R;
import com.ccppdfreader.app.models.PDFDocument;
import java.util.ArrayList;
import java.util.List;

public class PDFDocumentAdapter extends RecyclerView.Adapter<PDFDocumentAdapter.ViewHolder> {

    public interface OnDocumentClickListener {
        void onDocumentClick(PDFDocument document);
        void onDocumentLongClick(PDFDocument document);
        void onFavoriteClick(PDFDocument document);
    }

    private Context context;
    private List<PDFDocument> documents;
    private OnDocumentClickListener listener;

    public PDFDocumentAdapter(Context context) {
        this.context = context;
        this.documents = new ArrayList<>();
    }

    public void setOnDocumentClickListener(OnDocumentClickListener listener) {
        this.listener = listener;
    }

    public void setDocuments(List<PDFDocument> documents) {
        this.documents = documents != null ? documents : new ArrayList<>();
        notifyDataSetChanged();
    }

    public void addDocument(PDFDocument document) {
        if (document != null) {
            documents.add(document);
            notifyItemInserted(documents.size() - 1);
        }
    }

    public void removeDocument(PDFDocument document) {
        int position = documents.indexOf(document);
        if (position >= 0) {
            documents.remove(position);
            notifyItemRemoved(position);
        }
    }

    public void updateDocument(PDFDocument document) {
        for (int i = 0; i < documents.size(); i++) {
            if (documents.get(i).getId().equals(document.getId())) {
                documents.set(i, document);
                notifyItemChanged(i);
                break;
            }
        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_pdf_document, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        PDFDocument document = documents.get(position);
        holder.bind(document);
    }

    @Override
    public int getItemCount() {
        return documents.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private TextView textViewTitle;
        private TextView textViewPath;
        private TextView textViewSize;
        private TextView textViewPages;
        private TextView textViewProgress;
        private ImageView imageViewFavorite;
        private ProgressBar progressBarReading;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            textViewTitle = itemView.findViewById(R.id.textViewPdfTitle);
            textViewPath = itemView.findViewById(R.id.textViewPdfPath);
            textViewSize = itemView.findViewById(R.id.textViewPdfSize);
            textViewPages = itemView.findViewById(R.id.textViewPdfPages);
            textViewProgress = itemView.findViewById(R.id.textViewPdfProgress);
            imageViewFavorite = itemView.findViewById(R.id.imageViewFavorite);
            progressBarReading = itemView.findViewById(R.id.progressBarReading);

            itemView.setOnClickListener(v -> {
                if (listener != null && getAdapterPosition() != RecyclerView.NO_POSITION) {
                    listener.onDocumentClick(documents.get(getAdapterPosition()));
                }
            });

            itemView.setOnLongClickListener(v -> {
                if (listener != null && getAdapterPosition() != RecyclerView.NO_POSITION) {
                    listener.onDocumentLongClick(documents.get(getAdapterPosition()));
                    return true;
                }
                return false;
            });

            if (imageViewFavorite != null) {
                imageViewFavorite.setOnClickListener(v -> {
                    if (listener != null && getAdapterPosition() != RecyclerView.NO_POSITION) {
                        listener.onFavoriteClick(documents.get(getAdapterPosition()));
                    }
                });
            }
        }

        public void bind(PDFDocument document) {
            textViewTitle.setText(document.getTitle());
            textViewPath.setText(document.getFilePath());

            if (textViewSize != null) {
                textViewSize.setText(document.getFormattedFileSize());
            }

            if (textViewPages != null) {
                String pagesText = document.getTotalPages() > 0 ? 
                    document.getTotalPages() + " pages" : "Unknown pages";
                textViewPages.setText(pagesText);
            }

            if (textViewProgress != null) {
                if (document.getReadingProgress() > 0) {
                    String progressText = String.format("%.0f%% complete", document.getReadingProgress() * 100);
                    textViewProgress.setText(progressText);
                    textViewProgress.setVisibility(View.VISIBLE);
                } else {
                    textViewProgress.setVisibility(View.GONE);
                }
            }

            if (progressBarReading != null) {
                if (document.getReadingProgress() > 0) {
                    progressBarReading.setProgress((int)(document.getReadingProgress() * 100));
                    progressBarReading.setVisibility(View.VISIBLE);
                } else {
                    progressBarReading.setVisibility(View.GONE);
                }
            }

            if (imageViewFavorite != null) {
                imageViewFavorite.setImageResource(document.isFavorite() ? 
                    android.R.drawable.btn_star_big_on : android.R.drawable.btn_star_big_off);
                imageViewFavorite.setVisibility(View.VISIBLE);
            }
        }
    }

    public List<PDFDocument> getDocuments() {
        return new ArrayList<>(documents);
    }

    public void clearDocuments() {
        documents.clear();
        notifyDataSetChanged();
    }

    public boolean isEmpty() {
        return documents.isEmpty();
    }
}
