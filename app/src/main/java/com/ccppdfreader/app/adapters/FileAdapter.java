package com.ccppdfreader.app.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.ccppdfreader.app.R;
import com.ccppdfreader.app.models.FileItem;
import java.util.ArrayList;
import java.util.List;

public class FileAdapter extends RecyclerView.Adapter<FileAdapter.ViewHolder> {

    public interface OnFileClickListener {
        void onFileClick(FileItem fileItem);
        void onFileLongClick(FileItem fileItem);
    }

    private Context context;
    private List<FileItem> files;
    private OnFileClickListener listener;

    public FileAdapter(Context context) {
        this.context = context;
        this.files = new ArrayList<>();
    }

    public void setOnFileClickListener(OnFileClickListener listener) {
        this.listener = listener;
    }

    public void setFiles(List<FileItem> files) {
        this.files = files != null ? files : new ArrayList<>();
        notifyDataSetChanged();
    }

    public void addFile(FileItem file) {
        if (file != null) {
            files.add(file);
            notifyItemInserted(files.size() - 1);
        }
    }

    public void removeFile(FileItem file) {
        int position = files.indexOf(file);
        if (position >= 0) {
            files.remove(position);
            notifyItemRemoved(position);
        }
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_file, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        FileItem file = files.get(position);
        holder.bind(file);
    }

    @Override
    public int getItemCount() {
        return files.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        private ImageView imageViewIcon;
        private TextView textViewFileName;
        private TextView textViewFileSize;
        private TextView textViewFileDate;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            imageViewIcon = itemView.findViewById(R.id.imageViewFileIcon);
            textViewFileName = itemView.findViewById(R.id.textViewFileName);
            textViewFileSize = itemView.findViewById(R.id.textViewFileSize);
            textViewFileDate = itemView.findViewById(R.id.textViewFileDate);

            itemView.setOnClickListener(v -> {
                if (listener != null && getAdapterPosition() != RecyclerView.NO_POSITION) {
                    listener.onFileClick(files.get(getAdapterPosition()));
                }
            });

            itemView.setOnLongClickListener(v -> {
                if (listener != null && getAdapterPosition() != RecyclerView.NO_POSITION) {
                    listener.onFileLongClick(files.get(getAdapterPosition()));
                    return true;
                }
                return false;
            });
        }

        public void bind(FileItem file) {
            textViewFileName.setText(file.getDisplayName());

            // Set icon based on file type
            if (file.isDirectory()) {
                imageViewIcon.setImageResource(R.drawable.ic_folder);
                textViewFileSize.setText("Folder");
            } else if (file.isPDF()) {
                imageViewIcon.setImageResource(R.drawable.ic_pdf);
                textViewFileSize.setText(file.getFormattedSize());
            } else {
                imageViewIcon.setImageResource(R.drawable.ic_file);
                textViewFileSize.setText(file.getFormattedSize());
            }

            if (textViewFileDate != null) {
                textViewFileDate.setText(file.getFormattedDate());
            }
        }
    }

    public List<FileItem> getFiles() {
        return new ArrayList<>(files);
    }

    public void clearFiles() {
        files.clear();
        notifyDataSetChanged();
    }

    public boolean isEmpty() {
        return files.isEmpty();
    }

    public List<FileItem> getPDFFiles() {
        List<FileItem> pdfFiles = new ArrayList<>();
        for (FileItem file : files) {
            if (file.isPDF()) {
                pdfFiles.add(file);
            }
        }
        return pdfFiles;
    }

    public List<FileItem> getDirectories() {
        List<FileItem> directories = new ArrayList<>();
        for (FileItem file : files) {
            if (file.isDirectory()) {
                directories.add(file);
            }
        }
        return directories;
    }
}
