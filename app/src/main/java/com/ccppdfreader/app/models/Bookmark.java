package com.ccppdfreader.app.models;

import android.os.Parcel;
import android.os.Parcelable;
import com.ccppdfreader.app.utils.FileUtils;
import java.util.Date;

/**
 * Model class representing a PDF bookmark
 */
public class Bookmark implements Parcelable {
    
    private String filePath;
    private int pageNumber;
    private String title;
    private Date createdDate;
    private String note;
    
    public Bookmark() {
        // Default constructor
    }
    
    public Bookmark(String filePath, int pageNumber, String title) {
        this.filePath = filePath;
        this.pageNumber = pageNumber;
        this.title = title;
        this.createdDate = new Date();
    }
    
    // Parcelable implementation
    protected Bookmark(Parcel in) {
        filePath = in.readString();
        pageNumber = in.readInt();
        title = in.readString();
        createdDate = new Date(in.readLong());
        note = in.readString();
    }
    
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(filePath);
        dest.writeInt(pageNumber);
        dest.writeString(title);
        dest.writeLong(createdDate != null ? createdDate.getTime() : 0);
        dest.writeString(note);
    }
    
    @Override
    public int describeContents() {
        return 0;
    }
    
    public static final Creator<Bookmark> CREATOR = new Creator<Bookmark>() {
        @Override
        public Bookmark createFromParcel(Parcel in) {
            return new Bookmark(in);
        }
        
        @Override
        public Bookmark[] newArray(int size) {
            return new Bookmark[size];
        }
    };
    
    // Getters and Setters
    public String getFilePath() { return filePath; }
    public void setFilePath(String filePath) { this.filePath = filePath; }
    
    public int getPageNumber() { return pageNumber; }
    public void setPageNumber(int pageNumber) { this.pageNumber = pageNumber; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public Date getCreatedDate() { return createdDate; }
    public void setCreatedDate(Date createdDate) { this.createdDate = createdDate; }
    
    public String getNote() { return note; }
    public void setNote(String note) { this.note = note; }
    
    // Utility methods
    public String getFileName() {
        return FileUtils.getFileName(filePath);
    }
    
    public String getFormattedDate() {
        return FileUtils.formatDateTime(createdDate);
    }
    
    public String getDisplayTitle() {
        if (title != null && !title.trim().isEmpty()) {
            return title;
        }
        return "Page " + pageNumber;
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Bookmark bookmark = (Bookmark) obj;
        return pageNumber == bookmark.pageNumber &&
               filePath != null ? filePath.equals(bookmark.filePath) : bookmark.filePath == null;
    }
    
    @Override
    public int hashCode() {
        int result = filePath != null ? filePath.hashCode() : 0;
        result = 31 * result + pageNumber;
        return result;
    }
    
    @Override
    public String toString() {
        return "Bookmark{" +
                "filePath='" + filePath + '\'' +
                ", pageNumber=" + pageNumber +
                ", title='" + title + '\'' +
                ", createdDate=" + createdDate +
                '}';
    }
}
