package com.ccppdfreader.app.models;

import java.io.Serializable;
import java.util.Date;

public class Bookmark implements Serializable {
    private String id;
    private String documentId;
    private String documentTitle;
    private String title;
    private String note;
    private int pageNumber;
    private Date dateCreated;
    private Date lastModified;
    private String color; // For bookmark color coding

    public Bookmark() {
        this.id = generateId();
        this.dateCreated = new Date();
        this.lastModified = new Date();
        this.color = "#2196F3"; // Default blue color
    }

    public Bookmark(String documentId, String documentTitle, int pageNumber) {
        this();
        this.documentId = documentId;
        this.documentTitle = documentTitle;
        this.pageNumber = pageNumber;
        this.title = "Page " + pageNumber;
    }

    public Bookmark(String documentId, String documentTitle, int pageNumber, String title) {
        this(documentId, documentTitle, pageNumber);
        this.title = title;
    }

    private String generateId() {
        return "bookmark_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getDocumentId() { return documentId; }
    public void setDocumentId(String documentId) { this.documentId = documentId; }

    public String getDocumentTitle() { return documentTitle; }
    public void setDocumentTitle(String documentTitle) { this.documentTitle = documentTitle; }

    public String getTitle() { return title; }
    public void setTitle(String title) { 
        this.title = title;
        this.lastModified = new Date();
    }

    public String getNote() { return note; }
    public void setNote(String note) { 
        this.note = note;
        this.lastModified = new Date();
    }

    public int getPageNumber() { return pageNumber; }
    public void setPageNumber(int pageNumber) { this.pageNumber = pageNumber; }

    public Date getDateCreated() { return dateCreated; }
    public void setDateCreated(Date dateCreated) { this.dateCreated = dateCreated; }

    public Date getLastModified() { return lastModified; }
    public void setLastModified(Date lastModified) { this.lastModified = lastModified; }

    public String getColor() { return color; }
    public void setColor(String color) { 
        this.color = color;
        this.lastModified = new Date();
    }

    public String getDisplayTitle() {
        return title != null && !title.isEmpty() ? title : "Page " + pageNumber;
    }

    public String getFormattedDate() {
        if (dateCreated == null) return "";
        
        long diff = System.currentTimeMillis() - dateCreated.getTime();
        long days = diff / (24 * 60 * 60 * 1000);
        
        if (days == 0) return "Today";
        if (days == 1) return "Yesterday";
        if (days < 7) return days + " days ago";
        if (days < 30) return (days / 7) + " weeks ago";
        if (days < 365) return (days / 30) + " months ago";
        return (days / 365) + " years ago";
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Bookmark bookmark = (Bookmark) obj;
        return id != null ? id.equals(bookmark.id) : bookmark.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "Bookmark{" +
                "title='" + title + '\'' +
                ", documentTitle='" + documentTitle + '\'' +
                ", pageNumber=" + pageNumber +
                ", dateCreated=" + getFormattedDate() +
                '}';
    }
}
