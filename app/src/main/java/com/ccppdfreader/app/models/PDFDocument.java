package com.ccppdfreader.app.models;

import java.io.Serializable;
import java.util.Date;

public class PDFDocument implements Serializable {
    private String id;
    private String title;
    private String filePath;
    private String fileName;
    private long fileSize;
    private Date lastOpened;
    private Date dateAdded;
    private int totalPages;
    private int lastPageRead;
    private boolean isFavorite;
    private String thumbnailPath;
    private double readingProgress; // 0.0 to 1.0

    public PDFDocument() {
        this.id = generateId();
        this.dateAdded = new Date();
        this.lastPageRead = 1;
        this.readingProgress = 0.0;
        this.isFavorite = false;
    }

    public PDFDocument(String filePath, String fileName) {
        this();
        this.filePath = filePath;
        this.fileName = fileName;
        this.title = extractTitleFromFileName(fileName);
    }

    private String generateId() {
        return "pdf_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    private String extractTitleFromFileName(String fileName) {
        if (fileName == null) return "Unknown Document";
        
        // Remove file extension
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0) {
            return fileName.substring(0, lastDot);
        }
        return fileName;
    }

    // Getters and Setters
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }

    public String getFilePath() { return filePath; }
    public void setFilePath(String filePath) { this.filePath = filePath; }

    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }

    public long getFileSize() { return fileSize; }
    public void setFileSize(long fileSize) { this.fileSize = fileSize; }

    public Date getLastOpened() { return lastOpened; }
    public void setLastOpened(Date lastOpened) { this.lastOpened = lastOpened; }

    public Date getDateAdded() { return dateAdded; }
    public void setDateAdded(Date dateAdded) { this.dateAdded = dateAdded; }

    public int getTotalPages() { return totalPages; }
    public void setTotalPages(int totalPages) { this.totalPages = totalPages; }

    public int getLastPageRead() { return lastPageRead; }
    public void setLastPageRead(int lastPageRead) { this.lastPageRead = lastPageRead; }

    public boolean isFavorite() { return isFavorite; }
    public void setFavorite(boolean favorite) { isFavorite = favorite; }

    public String getThumbnailPath() { return thumbnailPath; }
    public void setThumbnailPath(String thumbnailPath) { this.thumbnailPath = thumbnailPath; }

    public double getReadingProgress() { return readingProgress; }
    public void setReadingProgress(double readingProgress) { this.readingProgress = readingProgress; }

    public String getFormattedFileSize() {
        if (fileSize < 1024) return fileSize + " B";
        if (fileSize < 1024 * 1024) return String.format("%.1f KB", fileSize / 1024.0);
        if (fileSize < 1024 * 1024 * 1024) return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
    }

    public void updateReadingProgress() {
        if (totalPages > 0) {
            this.readingProgress = (double) lastPageRead / totalPages;
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        PDFDocument that = (PDFDocument) obj;
        return id != null ? id.equals(that.id) : that.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "PDFDocument{" +
                "title='" + title + '\'' +
                ", fileName='" + fileName + '\'' +
                ", fileSize=" + getFormattedFileSize() +
                ", totalPages=" + totalPages +
                ", readingProgress=" + String.format("%.1f%%", readingProgress * 100) +
                '}';
    }
}
