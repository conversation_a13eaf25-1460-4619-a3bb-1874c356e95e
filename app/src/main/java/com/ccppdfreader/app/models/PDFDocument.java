package com.ccppdfreader.app.models;

import android.os.Parcel;
import android.os.Parcelable;
import java.util.Date;

/**
 * Model class representing a PDF document
 */
public class PDFDocument implements Parcelable {
    
    private String filePath;
    private String fileName;
    private String displayName;
    private long fileSize;
    private Date lastModified;
    private Date lastOpened;
    private int totalPages;
    private int lastPageRead;
    private boolean isFavorite;
    private String thumbnailPath;
    
    public PDFDocument() {
        // Default constructor
    }
    
    public PDFDocument(String filePath, String fileName) {
        this.filePath = filePath;
        this.fileName = fileName;
        this.displayName = fileName;
        this.lastPageRead = 1;
        this.isFavorite = false;
    }
    
    // Parcelable implementation
    protected PDFDocument(Parcel in) {
        filePath = in.readString();
        fileName = in.readString();
        displayName = in.readString();
        fileSize = in.readLong();
        lastModified = new Date(in.readLong());
        lastOpened = new Date(in.readLong());
        totalPages = in.readInt();
        lastPageRead = in.readInt();
        isFavorite = in.readByte() != 0;
        thumbnailPath = in.readString();
    }
    
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(filePath);
        dest.writeString(fileName);
        dest.writeString(displayName);
        dest.writeLong(fileSize);
        dest.writeLong(lastModified != null ? lastModified.getTime() : 0);
        dest.writeLong(lastOpened != null ? lastOpened.getTime() : 0);
        dest.writeInt(totalPages);
        dest.writeInt(lastPageRead);
        dest.writeByte((byte) (isFavorite ? 1 : 0));
        dest.writeString(thumbnailPath);
    }
    
    @Override
    public int describeContents() {
        return 0;
    }
    
    public static final Creator<PDFDocument> CREATOR = new Creator<PDFDocument>() {
        @Override
        public PDFDocument createFromParcel(Parcel in) {
            return new PDFDocument(in);
        }
        
        @Override
        public PDFDocument[] newArray(int size) {
            return new PDFDocument[size];
        }
    };
    
    // Getters and Setters
    public String getFilePath() { return filePath; }
    public void setFilePath(String filePath) { this.filePath = filePath; }
    
    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }
    
    public String getDisplayName() { return displayName; }
    public void setDisplayName(String displayName) { this.displayName = displayName; }
    
    public long getFileSize() { return fileSize; }
    public void setFileSize(long fileSize) { this.fileSize = fileSize; }
    
    public Date getLastModified() { return lastModified; }
    public void setLastModified(Date lastModified) { this.lastModified = lastModified; }
    
    public Date getLastOpened() { return lastOpened; }
    public void setLastOpened(Date lastOpened) { this.lastOpened = lastOpened; }
    
    public int getTotalPages() { return totalPages; }
    public void setTotalPages(int totalPages) { this.totalPages = totalPages; }
    
    public int getLastPageRead() { return lastPageRead; }
    public void setLastPageRead(int lastPageRead) { this.lastPageRead = lastPageRead; }
    
    public boolean isFavorite() { return isFavorite; }
    public void setFavorite(boolean favorite) { isFavorite = favorite; }
    
    public String getThumbnailPath() { return thumbnailPath; }
    public void setThumbnailPath(String thumbnailPath) { this.thumbnailPath = thumbnailPath; }
    
    /**
     * Get formatted file size string
     */
    public String getFormattedFileSize() {
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        }
    }
    
    /**
     * Get reading progress percentage
     */
    public int getReadingProgress() {
        if (totalPages <= 0) return 0;
        return (int) ((lastPageRead * 100.0) / totalPages);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        PDFDocument that = (PDFDocument) obj;
        return filePath != null ? filePath.equals(that.filePath) : that.filePath == null;
    }
    
    @Override
    public int hashCode() {
        return filePath != null ? filePath.hashCode() : 0;
    }
}
