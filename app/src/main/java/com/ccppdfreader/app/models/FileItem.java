package com.ccppdfreader.app.models;

import java.io.File;
import java.io.Serializable;
import java.util.Date;

public class FileItem implements Serializable {
    private String name;
    private String path;
    private long size;
    private Date lastModified;
    private boolean isDirectory;
    private boolean isPDF;
    private String extension;
    private boolean isHidden;

    public FileItem() {}

    public FileItem(File file) {
        this.name = file.getName();
        this.path = file.getAbsolutePath();
        this.size = file.length();
        this.lastModified = new Date(file.lastModified());
        this.isDirectory = file.isDirectory();
        this.isHidden = file.isHidden();
        
        if (!isDirectory) {
            this.extension = getFileExtension(name);
            this.isPDF = "pdf".equalsIgnoreCase(extension);
        }
    }

    public FileItem(String name, String path, boolean isDirectory) {
        this.name = name;
        this.path = path;
        this.isDirectory = isDirectory;
        this.isHidden = name.startsWith(".");
        
        if (!isDirectory) {
            this.extension = getFileExtension(name);
            this.isPDF = "pdf".equalsIgnoreCase(extension);
        }
    }

    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) return "";
        
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0 && lastDot < fileName.length() - 1) {
            return fileName.substring(lastDot + 1).toLowerCase();
        }
        return "";
    }

    // Getters and Setters
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getPath() { return path; }
    public void setPath(String path) { this.path = path; }

    public long getSize() { return size; }
    public void setSize(long size) { this.size = size; }

    public Date getLastModified() { return lastModified; }
    public void setLastModified(Date lastModified) { this.lastModified = lastModified; }

    public boolean isDirectory() { return isDirectory; }
    public void setDirectory(boolean directory) { isDirectory = directory; }

    public boolean isPDF() { return isPDF; }
    public void setPDF(boolean PDF) { isPDF = PDF; }

    public String getExtension() { return extension; }
    public void setExtension(String extension) { this.extension = extension; }

    public boolean isHidden() { return isHidden; }
    public void setHidden(boolean hidden) { isHidden = hidden; }

    public String getFormattedSize() {
        if (isDirectory) return "";
        if (size < 1024) return size + " B";
        if (size < 1024 * 1024) return String.format("%.1f KB", size / 1024.0);
        if (size < 1024 * 1024 * 1024) return String.format("%.1f MB", size / (1024.0 * 1024.0));
        return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
    }

    public String getFormattedDate() {
        if (lastModified == null) return "";
        
        long diff = System.currentTimeMillis() - lastModified.getTime();
        long days = diff / (24 * 60 * 60 * 1000);
        
        if (days == 0) return "Today";
        if (days == 1) return "Yesterday";
        if (days < 7) return days + " days ago";
        if (days < 30) return (days / 7) + " weeks ago";
        if (days < 365) return (days / 30) + " months ago";
        return (days / 365) + " years ago";
    }

    public String getDisplayName() {
        return name != null ? name : "Unknown";
    }

    public File getFile() {
        return new File(path);
    }

    public boolean canRead() {
        return getFile().canRead();
    }

    public boolean exists() {
        return getFile().exists();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        FileItem fileItem = (FileItem) obj;
        return path != null ? path.equals(fileItem.path) : fileItem.path == null;
    }

    @Override
    public int hashCode() {
        return path != null ? path.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "FileItem{" +
                "name='" + name + '\'' +
                ", path='" + path + '\'' +
                ", size=" + getFormattedSize() +
                ", isDirectory=" + isDirectory +
                ", isPDF=" + isPDF +
                '}';
    }
}
