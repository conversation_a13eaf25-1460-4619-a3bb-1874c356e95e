package com.ccppdfreader.app.models;

import android.os.Parcel;
import android.os.Parcelable;
import com.ccppdfreader.app.utils.FileUtils;
import java.io.File;
import java.util.Date;

/**
 * Model class representing a file or folder item
 */
public class FileItem implements Parcelable {
    
    public enum FileType {
        FOLDER,
        PDF,
        OTHER
    }
    
    private String name;
    private String path;
    private FileType type;
    private long size;
    private Date lastModified;
    private boolean isHidden;
    private int itemCount; // For folders, number of items inside
    
    public FileItem() {
        // Default constructor
    }
    
    public FileItem(File file) {
        this.name = file.getName();
        this.path = file.getAbsolutePath();
        this.size = file.length();
        this.lastModified = new Date(file.lastModified());
        this.isHidden = file.isHidden();
        
        if (file.isDirectory()) {
            this.type = FileType.FOLDER;
            // Count items in folder
            File[] files = file.listFiles();
            this.itemCount = files != null ? files.length : 0;
        } else if (FileUtils.isPDFFile(file.getName())) {
            this.type = FileType.PDF;
        } else {
            this.type = FileType.OTHER;
        }
    }
    
    // Parcelable implementation
    protected FileItem(Parcel in) {
        name = in.readString();
        path = in.readString();
        type = FileType.valueOf(in.readString());
        size = in.readLong();
        lastModified = new Date(in.readLong());
        isHidden = in.readByte() != 0;
        itemCount = in.readInt();
    }
    
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(name);
        dest.writeString(path);
        dest.writeString(type.name());
        dest.writeLong(size);
        dest.writeLong(lastModified != null ? lastModified.getTime() : 0);
        dest.writeByte((byte) (isHidden ? 1 : 0));
        dest.writeInt(itemCount);
    }
    
    @Override
    public int describeContents() {
        return 0;
    }
    
    public static final Creator<FileItem> CREATOR = new Creator<FileItem>() {
        @Override
        public FileItem createFromParcel(Parcel in) {
            return new FileItem(in);
        }
        
        @Override
        public FileItem[] newArray(int size) {
            return new FileItem[size];
        }
    };
    
    // Getters and Setters
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getPath() { return path; }
    public void setPath(String path) { this.path = path; }
    
    public FileType getType() { return type; }
    public void setType(FileType type) { this.type = type; }
    
    public long getSize() { return size; }
    public void setSize(long size) { this.size = size; }
    
    public Date getLastModified() { return lastModified; }
    public void setLastModified(Date lastModified) { this.lastModified = lastModified; }
    
    public boolean isHidden() { return isHidden; }
    public void setHidden(boolean hidden) { isHidden = hidden; }
    
    public int getItemCount() { return itemCount; }
    public void setItemCount(int itemCount) { this.itemCount = itemCount; }
    
    // Utility methods
    public boolean isFolder() {
        return type == FileType.FOLDER;
    }
    
    public boolean isPDF() {
        return type == FileType.PDF;
    }
    
    public String getFormattedSize() {
        if (isFolder()) {
            return itemCount + " items";
        }
        return FileUtils.formatFileSize(size);
    }
    
    public String getFormattedDate() {
        return FileUtils.formatDate(lastModified);
    }
    
    public String getDisplayName() {
        return name;
    }
    
    public File getFile() {
        return new File(path);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        FileItem fileItem = (FileItem) obj;
        return path != null ? path.equals(fileItem.path) : fileItem.path == null;
    }
    
    @Override
    public int hashCode() {
        return path != null ? path.hashCode() : 0;
    }
    
    @Override
    public String toString() {
        return "FileItem{" +
                "name='" + name + '\'' +
                ", path='" + path + '\'' +
                ", type=" + type +
                ", size=" + size +
                '}';
    }
}
