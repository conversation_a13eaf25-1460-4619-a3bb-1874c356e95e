plugins {
    id 'com.android.application'
}

android {
    namespace 'com.ccppdfreader.app'
    compileSdk 34

    defaultConfig {
        applicationId "com.ccppdfreader.app"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        
        vectorDrawables {
            useSupportLibrary true
        }
    }

    signingConfigs {
        // debug {
        //     // Using default debug keystore
        // }
        release {
            // For production, create a release keystore and configure:
            // storeFile file('release.keystore')
            // storePassword System.getenv("KEYSTORE_PASSWORD")
            // keyAlias System.getenv("KEY_ALIAS")
            // keyPassword System.getenv("KEY_PASSWORD")

            // For now, using debug keystore (CHANGE FOR PRODUCTION)
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release

            // Optimization flags
            zipAlignEnabled true
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false

            // Version name suffix for release builds
            versionNameSuffix ""
        }
        debug {
            minifyEnabled false
            shrinkResources false
            debuggable true
            signingConfig signingConfigs.debug
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    buildFeatures {
        viewBinding true
    }
    
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
            excludes += '/META-INF/DEPENDENCIES'
            excludes += '/META-INF/LICENSE'
            excludes += '/META-INF/LICENSE.txt'
            excludes += '/META-INF/NOTICE'
            excludes += '/META-INF/NOTICE.txt'
        }
    }

    bundle {
        language {
            // Enable language splits for smaller downloads
            enableSplit = true
        }
        density {
            // Enable density splits for smaller downloads
            enableSplit = true
        }
        abi {
            // Enable ABI splits for smaller downloads
            enableSplit = true
        }
    }
}

repositories {
    google()
    mavenCentral()
    maven { url 'https://jitpack.io' }
}

configurations {
    implementation {
        exclude group: 'org.jetbrains.kotlin', module: 'kotlin-stdlib-jdk7'
        exclude group: 'org.jetbrains.kotlin', module: 'kotlin-stdlib-jdk8'
    }
}

dependencies {
    // Minimal Android dependencies
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.core:core:1.12.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    // Testing (minimal)
    testImplementation 'junit:junit:4.13.2'
}
