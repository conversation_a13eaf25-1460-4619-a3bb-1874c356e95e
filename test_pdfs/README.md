# Instrucciones para Probar la App CCP PDF Reader

## 📱 Instalación y Configuración

1. **Instalar el APK**:
   - Transfiere el archivo `app-debug.apk` a tu dispositivo Android
   - Habilita "Fuentes desconocidas" en Configuración > Seguridad
   - Instala el APK

2. **Otorgar Permisos**:
   - Al abrir la app, otorga permisos de almacenamiento
   - Esto es necesario para acceder a archivos PDF

## 📄 Obtener PDFs de Prueba

### Opción 1: Descargar PDFs de Internet
- Abre el navegador en tu dispositivo
- Busca "sample PDF download" o "PDF test file"
- Descarga algunos PDFs a tu carpeta de Descargas

### Opción 2: Crear PDF Simple
- Usa Google Docs o cualquier procesador de texto
- Crea un documento simple con texto e imágenes
- Exporta como PDF
- Guarda en tu dispositivo

### Opción 3: PDFs Recomendados para Prueba
- Manual de usuario de alguna app
- Documentos de trabajo o estudio
- Libros electrónicos en formato PDF

## 🧪 Cómo Probar la App

### 1. Pantalla Principal
- ✅ Verifica que se muestre la lista de documentos recientes
- ✅ Toca "File Explorer" para navegar por archivos

### 2. Explorador de Archivos
- ✅ Navega a la carpeta de Descargas
- ✅ Verifica que solo se muestren PDFs y carpetas
- ✅ Toca un archivo PDF para abrirlo

### 3. Visor de PDF
- ✅ Verifica que se muestre el contenido real del PDF
- ✅ Desplázate verticalmente para ver diferentes páginas
- ✅ Usa los botones de navegación (anterior/siguiente)
- ✅ Agrega marcadores tocando el ícono de bookmark
- ✅ Marca como favorito tocando el corazón

### 4. Funcionalidades Avanzadas
- ✅ Ve a Bookmarks para ver marcadores guardados
- ✅ Verifica que el progreso de lectura se guarde
- ✅ Cierra y reabre la app para verificar persistencia

## 🐛 Solución de Problemas

### Si la app se cierra al abrir un PDF:
- Verifica que el archivo PDF no esté corrupto
- Prueba con un PDF más pequeño (menos de 10 MB)
- Asegúrate de tener suficiente memoria libre

### Si no se muestran PDFs:
- Verifica que los permisos de almacenamiento estén otorgados
- Navega manualmente a la carpeta correcta
- Verifica que los archivos tengan extensión .pdf

### Si el PDF no se ve correctamente:
- La app carga solo las primeras 5 páginas para evitar problemas de memoria
- Funciona mejor con PDFs simples (texto e imágenes básicas)
- PDFs muy complejos pueden tener problemas de renderizado

## 📊 Funcionalidades Implementadas

✅ **Explorador de archivos** - Navega por el sistema de archivos  
✅ **Visualización real de PDFs** - Muestra el contenido usando PdfRenderer  
✅ **Navegación por páginas** - Scroll y botones de navegación  
✅ **Sistema de marcadores** - Guarda páginas importantes  
✅ **Favoritos** - Marca documentos preferidos  
✅ **Historial reciente** - Acceso rápido a documentos abiertos  
✅ **Progreso de lectura** - Continúa donde lo dejaste  
✅ **Persistencia de datos** - Guarda configuración y progreso  

## 🎯 Limitaciones Actuales

- Solo carga las primeras 5 páginas para optimizar memoria
- Funciona mejor con PDFs simples
- Requiere Android 5.0+ para visualización de PDFs
- No incluye zoom manual (solo ajuste automático)

¡Disfruta probando tu nueva app de lectura de PDFs! 📚
