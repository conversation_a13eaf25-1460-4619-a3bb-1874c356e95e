#!/bin/bash

# CCP PDF Reader - Production Build Script
# This script builds the app for production release

set -e  # Exit on any error

echo "🚀 Starting CCP PDF Reader production build..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Android SDK is available
if [ -z "$ANDROID_HOME" ]; then
    print_error "ANDROID_HOME is not set. Please set it to your Android SDK path."
    exit 1
fi

# Check if gradlew exists
if [ ! -f "./gradlew" ]; then
    print_error "gradlew not found. Please run this script from the project root directory."
    exit 1
fi

# Make gradlew executable
chmod +x ./gradlew

print_status "Cleaning previous builds..."
./gradlew clean

print_status "Running lint checks..."
./gradlew lintRelease

print_status "Running unit tests..."
./gradlew testReleaseUnitTest

print_status "Building release APK..."
./gradlew assembleRelease

print_status "Building release App Bundle..."
./gradlew bundleRelease

# Check if builds were successful
if [ -f "app/build/outputs/apk/release/app-release.apk" ]; then
    print_success "APK build successful!"
    print_status "APK location: app/build/outputs/apk/release/app-release.apk"
else
    print_error "APK build failed!"
    exit 1
fi

if [ -f "app/build/outputs/bundle/release/app-release.aab" ]; then
    print_success "App Bundle build successful!"
    print_status "App Bundle location: app/build/outputs/bundle/release/app-release.aab"
else
    print_error "App Bundle build failed!"
    exit 1
fi

# Get file sizes
APK_SIZE=$(du -h "app/build/outputs/apk/release/app-release.apk" | cut -f1)
AAB_SIZE=$(du -h "app/build/outputs/bundle/release/app-release.aab" | cut -f1)

print_success "Build completed successfully!"
echo ""
echo "📦 Build artifacts:"
echo "   APK: app/build/outputs/apk/release/app-release.apk ($APK_SIZE)"
echo "   AAB: app/build/outputs/bundle/release/app-release.aab ($AAB_SIZE)"
echo ""
echo "📋 Next steps:"
echo "   1. Test the APK on different devices"
echo "   2. Upload the AAB to Google Play Console"
echo "   3. Configure store listing and screenshots"
echo "   4. Submit for review"
echo ""
print_warning "Remember to use a proper release keystore for production builds!"

# Optional: Open build output directory
if command -v open &> /dev/null; then
    read -p "Open build output directory? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        open app/build/outputs/
    fi
fi
