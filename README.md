# CCP PDF Reader

A modern, feature-rich PDF reader for Android with Material Design 3.

## 📱 Features

### Core Features
- **PDF Viewing**: High-quality PDF rendering with smooth scrolling
- **File Management**: Browse and organize PDF files with intuitive file explorer
- **Bookmarks**: Save and manage bookmarks for quick navigation
- **Recent Files**: Quick access to recently opened documents
- **Favorites**: Mark important documents as favorites

### Advanced Features
- **Search**: Find text within PDF documents
- **Night Mode**: Eye-friendly dark theme for reading
- **Zoom Controls**: Pinch-to-zoom and zoom controls
- **Page Navigation**: Easy page navigation with slider and buttons
- **Gestures**: Swipe gestures for page turning
- **Volume Key Navigation**: Use volume keys to navigate pages

### UI/UX Features
- **Material Design 3**: Modern, beautiful interface following Google's design guidelines
- **Smooth Animations**: Fluid transitions and animations
- **Responsive Design**: Optimized for different screen sizes
- **Accessibility**: Full accessibility support
- **Theme Support**: Light, dark, and system themes

## 🏗️ Architecture

The app follows the **MVP (Model-View-Presenter)** architecture pattern:

```
├── activities/          # UI Activities
├── adapters/           # RecyclerView Adapters
├── interfaces/         # MVP Contracts
├── managers/           # Business Logic Managers
├── models/            # Data Models
├── presenters/        # MVP Presenters
└── utils/             # Utility Classes
```

### Key Components

- **DocumentManager**: Handles PDF document metadata and recent files
- **BookmarkManager**: Manages bookmarks and reading progress
- **SettingsManager**: Handles app preferences and settings
- **FileExplorerPresenter**: Manages file system navigation
- **PDFReaderPresenter**: Handles PDF viewing logic

## 🛠️ Technical Stack

- **Language**: Java
- **UI Framework**: Android Views with ViewBinding
- **Design System**: Material Design 3
- **PDF Library**: android-pdf-viewer
- **Permissions**: Dexter
- **Architecture**: MVP Pattern
- **Build System**: Gradle
- **Min SDK**: 24 (Android 7.0)
- **Target SDK**: 34 (Android 14)

## 📦 Dependencies

```gradle
// Core Android
implementation 'androidx.appcompat:appcompat:1.6.1'
implementation 'androidx.core:core:1.12.0'
implementation 'com.google.android.material:material:1.11.0'

// PDF Viewer
implementation 'com.github.barteksc:android-pdf-viewer:3.2.0-beta.1'

// Permissions
implementation 'com.karumi:dexter:6.2.3'

// Lifecycle
implementation 'androidx.lifecycle:lifecycle-viewmodel:2.7.0'
implementation 'androidx.lifecycle:lifecycle-livedata:2.7.0'
```

## 🚀 Getting Started

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK 24+
- Java 8+

### Setup
1. Clone the repository
2. Open in Android Studio
3. Sync project with Gradle files
4. Run the app

### Building for Release
```bash
# Make the build script executable
chmod +x build-release.sh

# Run the build script
./build-release.sh
```

## 📁 Project Structure

```
app/
├── src/main/
│   ├── java/com/ccppdfreader/app/
│   │   ├── activities/         # Activities
│   │   ├── adapters/          # RecyclerView adapters
│   │   ├── interfaces/        # MVP contracts
│   │   ├── managers/          # Business logic
│   │   ├── models/           # Data models
│   │   ├── presenters/       # MVP presenters
│   │   └── utils/            # Utilities
│   ├── res/
│   │   ├── layout/           # XML layouts
│   │   ├── values/           # Resources
│   │   ├── drawable/         # Vector drawables
│   │   └── anim/            # Animations
│   └── AndroidManifest.xml
├── build.gradle              # App-level Gradle
└── proguard-rules.pro        # ProGuard configuration
```

## 🎨 Design System

The app uses Material Design 3 with:
- **Dynamic Color**: Adapts to system theme
- **Typography**: Roboto font family
- **Components**: Material 3 components
- **Theming**: Light/Dark theme support

## 🔧 Configuration

### Release Configuration
- **Minification**: Enabled with R8
- **Obfuscation**: ProGuard rules configured
- **App Bundle**: Optimized for Google Play
- **Signing**: Release keystore configuration

### Performance Optimizations
- **Background Threading**: File operations on background threads
- **Memory Management**: Optimized for low-memory devices
- **Caching**: Intelligent caching for better performance
- **Animations**: Hardware-accelerated animations

## 📱 Supported Devices

- **Minimum**: Android 7.0 (API 24)
- **Target**: Android 14 (API 34)
- **Architecture**: ARM, ARM64, x86, x86_64
- **Screen Sizes**: Phone, Tablet, Foldable

## 🧪 Testing

```bash
# Run unit tests
./gradlew testDebugUnitTest

# Run instrumented tests
./gradlew connectedAndroidTest

# Run lint checks
./gradlew lintDebug
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>

## 🔄 Version History

### v1.0.0 (Current)
- Initial release
- PDF viewing with zoom and navigation
- File explorer and management
- Bookmarks and favorites
- Material Design 3 UI
- Dark/Light theme support

## 🎯 Roadmap

- [ ] Cloud storage integration
- [ ] PDF annotation tools
- [ ] Text-to-speech
- [ ] PDF form filling
- [ ] Multi-language support
- [ ] Tablet-optimized UI

---

Made with ❤️ for Android developers
